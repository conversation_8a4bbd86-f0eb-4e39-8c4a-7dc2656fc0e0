package com.joinus.youkate.schedule;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.youkate.model.params.YouKaTeServiceUrlParam;
import com.joinus.youkate.service.YouKaTeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 向优卡特增量同步学生信息
 * 十分钟一次（可配置）
 *
 * <AUTHOR>
 */
@Slf4j
@Component("youKaTeIncrementStudentScheduleTask")
@JobHandler(value = "youKaTeIncrementStudentScheduleHandler")
public class YouKaTeIncrementStudentSchedule extends IJob<PERSON>andler {
    @Value("${youkate_service_url}")
    private String serviceUrl;
    @Resource
    private YouKaTeService youKaTeStudentService;

    @Override
    public ReturnT<String> execute(String minute) throws Exception {
        JSONArray jsonArray = JSONUtil.parseArray(serviceUrl);
        if (jsonArray.size() > 0) {
            for (Object object : jsonArray) {
                YouKaTeServiceUrlParam youKaTeServiceUrlParam = JSONUtil.toBean((JSONObject)object, YouKaTeServiceUrlParam.class);
                youKaTeStudentService.incrementStudent(youKaTeServiceUrlParam.getSchoolId(),youKaTeServiceUrlParam.getUrl(),minute);
            }
        }
        return ReturnT.SUCCESS;
    }
}
