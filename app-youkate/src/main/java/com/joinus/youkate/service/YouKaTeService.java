package com.joinus.youkate.service;

import com.joinus.dao.StudentEntity;
import com.joinus.youkate.model.params.*;
import com.joinus.youkate.model.result.YouKaTeGetStudentResult;
import com.joinus.youkate.model.result.YouKaTeModifyBalanceResult;
import com.joinus.youkate.model.result.YouKaTeQueryBalanceResult;
import com.joinus.youkate.model.result.YouKaTeResult;

/**
* 优卡特Service
* <AUTHOR> anpy
* @create 2023/6/28 15:27
*/
public interface YouKaTeService {

    /**
     * 获取优卡特平台学生信息
     * @param youKaTeGetUserParam 参数
     *                            fieldType 使用用户编号：yhbh
     * @param youKaTeServiceUrl 优卡特服务器地址
     * @return 学生信息
     */
    YouKaTeGetStudentResult getStudentInfo(YouKaTeGetUserParam youKaTeGetUserParam,String youKaTeServiceUrl);

    /**
     * 向优卡特添加学生
     * @param addUserParam 参数
     * @param youKaTeServiceUrl 优卡特服务器地址
     * @return 结果
     */
    YouKaTeResult addStudentInfo(YouKaTeAddUserParam addUserParam,String youKaTeServiceUrl);
    YouKaTeResult updateStudentInfo(YouKaTeUpdateUserParam updateUserParam,String youKaTeServiceUrl);

    YouKaTeResult deleteStudentInfo(YouKaTeParam param,String youKaTeServiceUrl);

    /**
     * 优卡特同步所有学生
     * @param schoolId 学校id
     * @param youKaTeServiceUrl 优卡特服务器地址
     */
    void syncAllStudent(Long schoolId,String youKaTeServiceUrl);

    /**
     * 优卡特增量同步学生
     * @param schoolId 学校id
     * @param youKaTeServiceUrl 优卡特服务器地址
     * @param minute 分钟
     */
    void incrementStudent(Long schoolId,String youKaTeServiceUrl,String minute);

    /**
     * 消费接口
     *
     * @param schoolId 学校id
     * @param param   消费参数
     * @param youKaTeServiceUrl 优卡特服务器地址
     * @return 消费结果
     */
    YouKaTeModifyBalanceResult modifyBalance(Long schoolId, YouKaTeModifyBalanceParam param,String youKaTeServiceUrl) throws Exception;

    /**
     * 删除平台所有学生(测试用)
     * @param schoolId 学校id
     * @param youKaTeServiceUrl 优卡特服务器地址
     */
    void deleteAllStudent(Long schoolId,String youKaTeServiceUrl);

    YouKaTeQueryBalanceResult queryBalance(StudentEntity student);

    YouKaTeQueryBalanceResult swipingCardModifyBalance(SwipingCardModifyBalanceParam param);

    //退款接口
    YouKaTeQueryBalanceResult swipingCardRefundMoney(SwipingCardModifyBalanceParam param);
}
