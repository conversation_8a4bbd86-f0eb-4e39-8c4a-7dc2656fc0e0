package com.joinus.youkate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.common.CustomThreadPool;
import com.joinus.common.mapper.impl.LifeServiceMapperImpl;
import com.joinus.common.model.enums.*;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.service.SyncStudentErrorServiceImpl;
import com.joinus.common.utils.CardSwitchUtil;
import com.joinus.common.utils.CommonUtils;
import com.joinus.common.utils.LocalHttpUtil;
import com.joinus.dao.LifeServiceEntity;
import com.joinus.dao.StudentEntity;
import com.joinus.dao.SyncStudentErrorEntity;
import com.joinus.dao.TerminalYoukateEntity;
import com.joinus.youkate.common.LocalConstant;
import com.joinus.youkate.mapper.StudentMapper;
import com.joinus.youkate.mapper.impl.StudentMapperImpl;
import com.joinus.youkate.model.enums.YouKaTeDeviceTypeEnum;
import com.joinus.youkate.model.params.*;
import com.joinus.youkate.model.result.*;
import com.joinus.youkate.service.LifeServiceSchoolConfigService;
import com.joinus.youkate.service.YouKaTeService;
import com.joinus.youkate.service.YouKaTeTerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

@Slf4j
@Service
public class YouKaTeServiceImpl implements YouKaTeService {

    @Value("${youkate_host_url}")
    String youKaTeUrl;
    @Value("${youkate_ipay_url}")
    String youKaTeIPayUrl;
    @Value("${youkate_ipay_clientId}")
    String youKaTeIPayClientId;
    @Value("${youkate_ipay_secret}")
    String youKaTeIPaySecret;
    @Value("${youkate_ipay_equipment}")
    String youKaTeIPayEquipment;
    @Value("${youkate_machine_number}")
    String youKaTeMachineNumber;
    @Value("${youkate_ipay_type}")
    String youKaTeIPayType;

    @Resource
    private StudentMapper studentMapper;
    @Resource
    private StudentMapperImpl studentMapperImpl;
    @Resource
    private SyncStudentErrorServiceImpl syncStudentErrorServiceImpl;
    @Resource
    private LifeServiceMapperImpl lifeServiceMapperImpl;
    @Resource
    private YouKaTeTerminalService youKaTeTerminalService;
    @Resource
    private LifeServiceSchoolConfigService lifeServiceSchoolConfigService;

    @Override
    public YouKaTeGetStudentResult getStudentInfo(YouKaTeGetUserParam youKaTeGetUserParam, String youKaTeServiceUrl) {
        String url = getYouKaTeServiceUrl(youKaTeServiceUrl);
        String response = LocalHttpUtil.postAndParam(url + LocalConstant.YouKaTeInterface.GET_USER, youKaTeGetUserParam);
        JSONObject jsonObject = JSONUtil.parseObj(response);
        JSONObject data = (JSONObject) jsonObject.get("Data");
        //如果优卡特平台查不到学生，List<ResultsDTO> results Results返回空字符串，需要单独处理
        if (data != null) {
            if (data.get("Results") instanceof String) {
                return null;
            }
        }
        return CommonUtils.jsonToObject(response, YouKaTeGetStudentResult.class);
    }

    @Override
    public YouKaTeResult addStudentInfo(YouKaTeAddUserParam addUserParam, String youKaTeServiceUrl) {
        String url = getYouKaTeServiceUrl(youKaTeServiceUrl);
        String response = LocalHttpUtil.postAndParam(url + LocalConstant.YouKaTeInterface.ADD_USER, addUserParam);
        log.info("优卡特：新增学生，请求地址：{}，请求参数：{}，响应结果：{}", url, JSON.toJSONString(addUserParam), response);
        return CommonUtils.jsonToObject(response, YouKaTeResult.class);
    }

    @Override
    public YouKaTeResult updateStudentInfo(YouKaTeUpdateUserParam updateUserParam, String youKaTeServiceUrl) {
        String url = getYouKaTeServiceUrl(youKaTeServiceUrl);
        String response = LocalHttpUtil.postAndParam(url + LocalConstant.YouKaTeInterface.UPDATE_USER, updateUserParam);
        log.info("优卡特：新增学生，请求地址：{}，请求参数：{}，响应结果：{}", url, JSON.toJSONString(updateUserParam), response);
        return CommonUtils.jsonToObject(response, YouKaTeResult.class);
    }

    @Override
    public YouKaTeResult deleteStudentInfo(YouKaTeParam param, String youKaTeServiceUrl) {
        String url = getYouKaTeServiceUrl(youKaTeServiceUrl);
        String response = LocalHttpUtil.postAndParam(url + LocalConstant.YouKaTeInterface.DELETE_USER, param);
        return CommonUtils.jsonToObject(response, YouKaTeResult.class);
    }

    @Override
    public void syncAllStudent(Long schoolId, String youKaTeServiceUrl) {
        int pageSize = 200;
        Integer total = studentMapper.loadStudentByYouKaTeCount(schoolId);
        log.info("优卡特：全量同步学生，学校:{},学生总数:{}", schoolId, total);
        int pageCount = (total + pageSize - 1) / pageSize;
        for (int i = 1; i <= pageCount; i++) {
            //分页获取全校学生信息
            List<YouKaTeAddUserParam> studentList = getYouKaTeAddUserParams(schoolId, pageSize, i);
            ExecutorService executor = CustomThreadPool.getExecutor();
            for (YouKaTeAddUserParam studentEntity : studentList) {
                syncStudent(schoolId, studentEntity, executor, youKaTeServiceUrl);
            }
        }
    }

    /**
     * 分页查询学生信息
     */
    private List<YouKaTeAddUserParam> getYouKaTeAddUserParams(Long schoolId, int pageSize, int i) {
        IPage<StudentEntity> pageParameters = new Page<>();
        pageParameters.setCurrent(i);
        pageParameters.setSize(pageSize);
        IPage<YouKaTeAddUserParam> userPage = studentMapper.loadStudentByYouKaTe(pageParameters, schoolId);
        return userPage.getRecords();
    }

    private List<YouKaTeAddUserParam> loadStudentByDeleteYouKaTe(Long schoolId, int pageSize, int i) {
        IPage<StudentEntity> pageParameters = new Page<>();
        pageParameters.setCurrent(i);
        pageParameters.setSize(pageSize);
        IPage<YouKaTeAddUserParam> userPage = studentMapper.loadStudentByDeleteYouKaTe(pageParameters, schoolId);
        return userPage.getRecords();
    }

    private void syncStudent(Long schoolId, YouKaTeAddUserParam studentEntity, ExecutorService executor, String youKaTeServiceUrl) {
        CompletableFuture.supplyAsync(() -> {
            synchronized (this) {
                if (Long.parseLong(studentEntity.getUserCard()) <= Long.parseLong(CardSwitchUtil.cardMaxNum())) {
                    //卡号高低位转换
                    studentEntity.setUserCard(CardSwitchUtil.switchCardNum(studentEntity.getUserCard()));
                }
                //获取优卡特平台学生信息
                YouKaTeGetUserParam getYouKaTeParam = YouKaTeGetUserParam.builder()
                        .pageIndex(1)
                        .pageSize(1)
                        .fieldType(YouKaTeFieldTypeEnum.FIELD_RYBH.getFieldType())
                        .requestId(studentEntity.getUserNumber()).build();
                YouKaTeGetStudentResult studentInfo = getStudentInfo(getYouKaTeParam, youKaTeServiceUrl);
                //学生姓名、卡号、照片不一致则更新
                if (studentInfoExist(studentInfo)) {
                    YouKaTeGetStudentResult.DataDTO.ResultsDTO resultsDTO = studentInfo.getData().getResults().get(0);
                    log.info("优卡特：更新学生对比,脸爱云平台:{},admin:{}", resultsDTO, studentEntity);
                    if (changeYouKaTeStudentRule(studentEntity, resultsDTO)) {
                        //姓名、照片、卡号不一致，更新优卡特平台学生信息
                        YouKaTeUpdateUserParam updateUserParam = YouKaTeUpdateUserParam.builder()
                                .fieldType(YouKaTeFieldTypeEnum.FIELD_RYBH.getFieldType())
                                .requestId(studentEntity.getUserNumber())
                                .faceImages(studentEntity.getFaceImages())
                                .userCardState(YouKaTeCardStateEnum.NORMAL_CARD.getCardState())
                                .userCard(studentEntity.getUserCard())
                                .userName(studentEntity.getUserName()).build();
                        YouKaTeResult youKaTeResult = updateStudentInfo(updateUserParam, youKaTeServiceUrl);
                        //同步失败存储数据
                        saveErrorInfo(schoolId, studentEntity, getYouKaTeParam, youKaTeResult, youKaTeServiceUrl);
                    }
                } else {
                    log.info("优卡特：学校:{},学生:{},未在优卡特平台查到学生，添加学生信息", schoolId, studentEntity);
                    //没有在优卡特平台查到学生，则添加
                    saveErrorInfo(schoolId, studentEntity, getYouKaTeParam, addStudentInfo(studentEntity, youKaTeServiceUrl), youKaTeServiceUrl);
                }
            }
            return null;
        }, executor);
    }

    /**
     * 更新学生时条件判断
     * 学生姓名、卡号、照片不一致则更新
     */
    private static boolean changeYouKaTeStudentRule(YouKaTeAddUserParam studentEntity, YouKaTeGetStudentResult.DataDTO.ResultsDTO resultsDTO) {
        return !resultsDTO.getFullName().equals(studentEntity.getUserName())
                || !resultsDTO.getImgData().equals(studentEntity.getFaceImages())
                || !resultsDTO.getCardNumber().equals(studentEntity.getUserCard())
                || !resultsDTO.getUserNumber().equals(studentEntity.getUserNumber());
    }

    private void saveErrorInfo(Long schoolId, YouKaTeAddUserParam studentEntity, YouKaTeGetUserParam getYouKaTeParam, YouKaTeResult youKaTeResult, String youKaTeServiceUrl) {
        SyncStudentErrorEntity.SyncStudentErrorEntityBuilder syncStudentErrorEntityBuilder = SyncStudentErrorEntity.builder()
                .errorType(SyncStudentErrorEnum.YOU_KA_TE.getErrorType())
                .studentName(studentEntity.getUserName())
                .studentId(studentEntity.getStudentId())
                .schoolId(schoolId);
        String errorMsg = "";
        //学生上传成功
        if (youKaTeResult.getCode() != null && youKaTeResult.getCode() == InterfaceResultEnum.YOUKATE_SUCCESS.getCode()) {
            YouKaTeGetStudentResult newStudentInfo = getStudentInfo(getYouKaTeParam, youKaTeServiceUrl);
            //学生成功后确定平台是否有该学生
            if (studentInfoExist(newStudentInfo)) {
                //如果有则判断照片是否上传成功
                String imgData = newStudentInfo.getData().getResults().get(0).getImgData();
                if (StrUtil.isEmpty(imgData)) {
                    //优卡特学生图片地址为学生编号，无法判断此次上传和上次是否一致
                    errorMsg = LocalConstant.YouKaTeResultConstant.IMG_UPLOAD_ERROR;
                }
            } else {
                errorMsg = LocalConstant.YouKaTeResultConstant.STUDENT_UPLOAD_ERROR;
            }
        } else {
            //学生上传失败直接存
            errorMsg = youKaTeResult.getMessage();
        }
        //存储上传失败数据
        if (StrUtil.isNotEmpty(errorMsg)) {
            syncStudentErrorEntityBuilder.errorMsg(errorMsg);
            SyncStudentErrorEntity syncStudentErrorEntity = syncStudentErrorEntityBuilder.build();
            syncStudentErrorServiceImpl.insertErrorInfo(syncStudentErrorEntity);
        }
    }

    @Override
    public void incrementStudent(Long schoolId, String youKaTeServiceUrl, String minute) {
        List<YouKaTeAddUserParam> youKaTeAddUserParams = studentMapper.loadIncrementStudent(schoolId, minute);
        log.info("优卡特：增量同步学生，学校:{},学生总数:{}，时间：{}分钟之内", schoolId, youKaTeAddUserParams.size(),minute);
        if (CollUtil.isNotEmpty(youKaTeAddUserParams)) {
            ExecutorService executor = CustomThreadPool.getExecutor();
            youKaTeAddUserParams.forEach(item -> {
                syncStudent(schoolId, item, executor, youKaTeServiceUrl);
            });
        }
    }

    /**
     * 判断学生信息是否在优卡特平台存在
     */
    private boolean studentInfoExist(YouKaTeGetStudentResult studentInfo) {
        return studentInfo != null && studentInfo.getCode() == InterfaceResultEnum.YOUKATE_SUCCESS.getCode() && CollUtil.isNotEmpty(studentInfo.getData().getResults());
    }

    @Override
    public YouKaTeModifyBalanceResult modifyBalance(Long schoolId, YouKaTeModifyBalanceParam param, String youKaTeServiceUrl) throws Exception {
        //验签时consumption_type参数不参与校验
        YouKaTeModifyBalanceSignParam signParam = new YouKaTeModifyBalanceSignParam();
        BeanUtil.copyProperties(param, signParam);
        String signContent = CommonUtils.getSignContent(signParam);
        String sign = CommonUtils.md5Encrypt(signContent);
        log.info("优卡特验签，value排序：{}，md5加密：{}，参数sign：{}", signContent, sign, param.getSign());
        if (sign != null && sign.equals(param.getSign())) {
            TerminalYoukateEntity terminalYoukate =
                    youKaTeTerminalService.queryTerminalByDeviceNumber(param.getMachine_Number(),
                            TerminalSupplierEnum.YOU_KA_TE);
            if (null == terminalYoukate) {
                return getYouKaTeModifyBalanceErrorResult(LocalConstant.YouKaTeResultConstant.DEVICE_NOT_EXIST);
            }

            //根据type判断学校是否启用该服务
            LifeServiceTypeEnum lifeServiceType =
                    LifeServiceTypeEnum.fromName(YouKaTeDeviceTypeEnum.fromTableType(terminalYoukate.getType()).name());

            boolean enable = lifeServiceSchoolConfigService.checkSchoolEnableLifeService(schoolId, lifeServiceType.getCode());
            if (!enable) {
                return getYouKaTeModifyBalanceErrorResult(LocalConstant.YouKaTeResultConstant.SCHOOL_CONFIG_NOT_ENABLE);
            }

            //根据卡号查询优卡特平台是否存在该学生
            YouKaTeGetUserParam getUserParam = YouKaTeGetUserParam.builder()
                    .requestId(param.getCardNumber())
                    .fieldType(YouKaTeFieldTypeEnum.FIELD_RFKH.getFieldType())
                    .pageIndex(LocalConstant.PAGE_INDEX)
                    .pageSize(LocalConstant.PAGE_SIZE)
                    .build();
            YouKaTeGetStudentResult studentResult = getStudentInfo(getUserParam, youKaTeServiceUrl);
            if (studentInfoExist(studentResult)) {
                YouKaTeGetStudentResult.DataDTO.ResultsDTO studentInfo = studentResult.getData().getResults().get(0);
                //查询学生生活服务是否签约
                long cardCode = Long.parseLong(studentInfo.getCardNumber());
                if (Long.parseLong(studentInfo.getCardNumber()) <= Long.parseLong(CardSwitchUtil.cardMaxNum())) {
                    cardCode = Long.parseLong(CardSwitchUtil.switchCardNum(studentInfo.getCardNumber()));
                }
                log.info("学生卡号:{}", cardCode);
                List<StudentEntity> studentList = studentMapperImpl.findStudentByCardCode(Long.toString(cardCode), schoolId);
                if (CollUtil.isEmpty(studentList)) {
                    return getYouKaTeModifyBalanceErrorResult(LocalConstant.YouKaTeResultConstant.ADMIN_STUDENT_NOT_EXIST);
                }
                if (studentList.size() > 1) {
                    return getYouKaTeModifyBalanceErrorResult(LocalConstant.YouKaTeResultConstant.STUDENT_IDENTITY_REPETITION);
                }
                StudentEntity studentEntity = studentList.get(0);
                List<LifeServiceEntity> lifeServiceEntities =
                        lifeServiceMapperImpl.loadAgreementStateByStudent(studentEntity.getId(), null);
                if (CollUtil.isEmpty(lifeServiceEntities)) {
                    return getYouKaTeModifyBalanceErrorResult(LocalConstant.YouKaTeResultConstant.STUDENT_NOT_SIGN);
                }
                //生活服务已签约，调用一卡通支付接口
                IPayModifyBalanceResult iPayModifyBalanceResult = iPayTradePay(schoolId,
                        YouKaTeDeviceTypeEnum.fromTableType(terminalYoukate.getType()),
                        param.getMoney(), studentInfo.getUserNumber());
                if (iPayModifyBalanceResult.getCode().equals(String.valueOf(InterfaceResultEnum.SUCCESS.getCode()))) {

                    //优卡特签约消费
                    youKaTeConsumption(param, iPayModifyBalanceResult, youKaTeServiceUrl);

                    //返回数据给设备
                    return YouKaTeModifyBalanceResult.builder()
                            .code(InterfaceResultEnum.YOUKATE_SUCCESS.getCode())
                            .message(LocalConstant.YouKaTeResultConstant.SUCCESS)
                            .data(YouKaTeModifyBalanceResult.Data.builder()
                                    .balance(iPayModifyBalanceResult.getData())
                                    .consumptionMone(Double.valueOf(param.getMoney()))
                                    .fullName(studentInfo.getFullName()).build()).build();
                } else {
                    return getYouKaTeModifyBalanceErrorResult(iPayModifyBalanceResult.getMsg());
                }
            } else {
                return getYouKaTeModifyBalanceErrorResult(LocalConstant.YouKaTeResultConstant.STUDENT_NOT_EXIST);
            }
        }

        return getYouKaTeModifyBalanceErrorResult(LocalConstant.YouKaTeResultConstant.SIGN_ERROR,InterfaceResultEnum.YOUKATE_ORDER_REPORT.getCode());
    }


    /**
     * 优卡特平台扣款消费
     */
    private void youKaTeConsumption(YouKaTeModifyBalanceParam param, IPayModifyBalanceResult iPayModifyBalanceResult, String youKaTeServiceUrl) throws Exception {
        TerminalYoukateEntity youkateEntity =
                youKaTeTerminalService.queryTerminalByDeviceNumber(param.getMachine_Number(), TerminalSupplierEnum.YOU_KA_TE);
        Date date = new Date(Long.parseLong(iPayModifyBalanceResult.getTimestamp()));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        YouKaTeConsumptionParam consumptionParam =
                YouKaTeConsumptionParam
                        .builder()
                        .requestId(param.getCardNumber())
                        .fieldType(YouKaTeFieldTypeEnum.FIELD_RFKH.getFieldType())
                        .transDtTm(sdf.format(date))
                        .transAmt(Double.valueOf(param.getMoney()))
                        .orderNumber(param.getOnline_Order_number())
                        .consumption_Type(param.getConsumption_type().toString())
                        .equipment(youkateEntity.getMachineNumber().toString())
                        .build();
        String url = getYouKaTeServiceUrl(youKaTeServiceUrl);
        LocalHttpUtil.postAndParam(url + LocalConstant.YouKaTeInterface.CONSUMPTION, consumptionParam);
    }

    /**
     * 如果youKaTeServiceUrl为空，则使用youKaTeUrl测试地址
     *
     * @param youKaTeServiceUrl apollo配置的和schoolId对应的url
     * @return url
     */
    private String getYouKaTeServiceUrl(String youKaTeServiceUrl) {
        return StrUtil.isEmpty(youKaTeServiceUrl) ? youKaTeUrl : youKaTeServiceUrl;
    }

    private static YouKaTeModifyBalanceResult getYouKaTeModifyBalanceErrorResult(String message) {
        return getYouKaTeModifyBalanceErrorResult(message, InterfaceResultEnum.YOUKATE_FAIL.getCode());
    }

    /**
     * 向优卡特设备返回数据
     */
    private static YouKaTeModifyBalanceResult getYouKaTeModifyBalanceErrorResult(String message, Integer code) {
        return YouKaTeModifyBalanceResult.builder()
                .code(code)
                .message(message)
                .build();
    }

    /**
     * 调用iPay扣款接口
     */
    private IPayModifyBalanceResult iPayTradePay(Long schoolId, YouKaTeDeviceTypeEnum deviceTypeEnum, String amount,
                                                 String identity) {

        YouKaTeIPayParam youKaTeIPayParam = YouKaTeIPayParam.builder()
                .clientId(youKaTeIPayClientId)
                .timeStamp(DateUtil.formatDateTime(new Date()))
                .norceStr(UUID.randomUUID().toString())
                .schoolId(schoolId.toString())
                .identity(identity)
                .orderNo(DateUtil.formatDateTime(new Date()))
                .amount(amount)
                .deviceType(String.valueOf(deviceTypeEnum.getType()))
                .build();
        HashMap<String, String> signMapParam = JSON.parseObject(JSON.toJSONString(youKaTeIPayParam), HashMap.class);
        signMapParam.put("clientSecret", youKaTeIPaySecret);
        String iPaySign = SecureUtil.md5(CommonUtils.signContent(signMapParam)).toUpperCase();
        youKaTeIPayParam.setSign(iPaySign);
        log.info("一卡通参数：{} {} {}", JSONUtil.toJsonStr(youKaTeIPayParam), youKaTeIPayUrl, LocalConstant.IPayInterface.pay);
        String response = LocalHttpUtil.post(youKaTeIPayUrl + LocalConstant.IPayInterface.pay, youKaTeIPayParam);
        log.info("一卡通接口结果：{}", response);
        return CommonUtils.jsonToObject(response, IPayModifyBalanceResult.class);
    }

    /**
     * 调用iPay退款接口
     */
    private IPayModifyBalanceResult iPayTradeRefund(Long schoolId, String amount, String identity, String orderNo) {

        YouKaTeIPayParam youKaTeIPayParam = YouKaTeIPayParam.builder()
                .clientId(youKaTeIPayClientId)
                .timeStamp(DateUtil.formatDateTime(new Date()))
                .norceStr(UUID.randomUUID().toString())
                .schoolId(schoolId.toString())
                .identity(identity)
                .orderNo(orderNo)
                .amount(amount)
                .build();
        HashMap<String, String> signMapParam = JSON.parseObject(JSON.toJSONString(youKaTeIPayParam), HashMap.class);
        signMapParam.put("clientSecret", youKaTeIPaySecret);
        String iPaySign = SecureUtil.md5(CommonUtils.signContent(signMapParam)).toUpperCase();
        youKaTeIPayParam.setSign(iPaySign);
        log.info("一卡通参数：{} {} {}", JSONUtil.toJsonStr(youKaTeIPayParam), youKaTeIPayUrl,
                LocalConstant.IPayInterface.refund);
        String response = LocalHttpUtil.post(youKaTeIPayUrl + LocalConstant.IPayInterface.refund, youKaTeIPayParam);
        log.info("一卡通接口结果：{}", response);
        return CommonUtils.jsonToObject(response, IPayModifyBalanceResult.class);
    }
    /**
     * 测试用接口，删除平台上所有学生
     */
    @Override
    public void deleteAllStudent(Long schoolId, String youKaTeServiceUrl) {
        int pageSize = 200;
        Integer total = studentMapper.loadStudentByYouKaTeDeleteCount(schoolId);
        int pageCount = (total + pageSize - 1) / pageSize;
        for (int i = 1; i <= pageCount; i++) {
            //分页获取全校学生信息
            List<YouKaTeAddUserParam> studentList = loadStudentByDeleteYouKaTe(schoolId, pageSize, i);
            ExecutorService executor = CustomThreadPool.getExecutor();
            for (YouKaTeAddUserParam studentEntity : studentList) {
                deleteAllStudent(studentEntity, executor, youKaTeServiceUrl);
            }
        }
    }

    private void deleteAllStudent(YouKaTeAddUserParam studentEntity, ExecutorService executor, String youKaTeServiceUrl) {
        CompletableFuture.supplyAsync(() -> {
            synchronized (this) {
                YouKaTeParam param = YouKaTeParam.builder().requestId(studentEntity.getUserNumber()).fieldType(YouKaTeFieldTypeEnum.FIELD_RYBH.getFieldType()).build();
                deleteStudentInfo(param, youKaTeServiceUrl);
            }
            return null;
        }, executor);
    }

    public static void main(String[] args) {
        System.out.println(CardSwitchUtil.cardMaxNum());
        System.out.println(CardSwitchUtil.switchCardNum("2969492291"));
    }

    @Override
    public YouKaTeQueryBalanceResult queryBalance(StudentEntity student) {

        YouKaTeIPayBalanceParam youKaTeIPayBalanceParam = YouKaTeIPayBalanceParam.builder()
                .clientId(youKaTeIPayClientId)
                .timeStamp(DateUtil.formatDateTime(new Date()))
                .norceStr(UUID.randomUUID().toString())
                .schoolId(student.getSchoolId())
                .identity(student.getIdentity())
                .build();
        HashMap<String, String> signMapParam = JSON.parseObject(JSON.toJSONString(youKaTeIPayBalanceParam), HashMap.class);
        signMapParam.put("clientSecret", youKaTeIPaySecret);
        String iPaySign = SecureUtil.md5(CommonUtils.signContent(signMapParam)).toUpperCase();
        youKaTeIPayBalanceParam.setSign(iPaySign);
        log.info("一卡通查询余额接口参数：{} {} {}", youKaTeIPayBalanceParam, youKaTeIPayUrl, LocalConstant.IPayInterface.balance);
        String response = LocalHttpUtil.post(youKaTeIPayUrl + LocalConstant.IPayInterface.balance, youKaTeIPayBalanceParam);
        log.info("一卡通余额接口结果：{}", response);
        IPayModifyBalanceResult iPayModifyBalanceResult = CommonUtils.jsonToObject(response, IPayModifyBalanceResult.class);
        return YouKaTeQueryBalanceResult.builder()
                .balance(iPayModifyBalanceResult.getData())
                .identity(student.getIdentity())
                .cardCode(student.getCardCode())
                .studentName(student.getStudentName())
                .build();
    }

    @Override
    public YouKaTeQueryBalanceResult swipingCardModifyBalance(SwipingCardModifyBalanceParam param) {
        List<StudentEntity> studentList = studentMapperImpl.findStudentByCardCode(param.getCardCode(), param.getSchoolId());
        CommonResponse.ERROR.assertCollNotNull(studentList, LocalConstant.YouKaTeResultConstant.ADMIN_STUDENT_NOT_EXIST);
        CommonResponse.ERROR.assertIsTrue(studentList.size() == 1, LocalConstant.YouKaTeResultConstant.STUDENT_IDENTITY_REPETITION);

        StudentEntity studentEntity = studentList.get(0);
        CommonResponse.ERROR.assertNotEmpty(studentEntity.getIdentity(), LocalConstant.YouKaTeResultConstant.STUDENT_NOT_IDENTITY);

        if (null != param.getLifeServiceType()) {
            //校验学生是否签约
            List<LifeServiceEntity> lifeServiceEntities =
                    lifeServiceMapperImpl.loadAgreementStateByStudent(studentEntity.getId(), null);
            CommonResponse.ERROR.assertCollNotNull(lifeServiceEntities,
                    LocalConstant.YouKaTeResultConstant.STUDENT_NOT_SIGN);
        }

        IPayModifyBalanceResult result = iPayTradePay(param.getSchoolId(),param.getDeviceTypeEnum(),param.getConsumptionMoney(),
                studentEntity.getIdentity());
        return YouKaTeQueryBalanceResult.builder()
                .balance(result.getData())
                .identity(studentEntity.getIdentity())
                .cardCode(param.getCardCode())
                .code(result.getCode())
                .message(result.getMsg())
                .consumeAmount(param.getConsumptionMoney())
                .studentName(studentEntity.getStudentName()).build();
    }

    @Override
    public YouKaTeQueryBalanceResult swipingCardRefundMoney(SwipingCardModifyBalanceParam param) {
        List<StudentEntity> studentList = studentMapperImpl.findStudentByCardCode(param.getCardCode(), param.getSchoolId());
        CommonResponse.ERROR.assertCollNotNull(studentList, LocalConstant.YouKaTeResultConstant.ADMIN_STUDENT_NOT_EXIST);
        CommonResponse.ERROR.assertIsTrue(studentList.size() == 1, LocalConstant.YouKaTeResultConstant.STUDENT_IDENTITY_REPETITION);

        StudentEntity studentEntity = studentList.get(0);
        CommonResponse.ERROR.assertNotEmpty(studentEntity.getIdentity(), LocalConstant.YouKaTeResultConstant.STUDENT_NOT_IDENTITY);

        if (null != param.getLifeServiceType()) {
            //校验学生是否签约
            List<LifeServiceEntity> lifeServiceEntities =
                    lifeServiceMapperImpl.loadAgreementStateByStudent(studentEntity.getId(), null);
            CommonResponse.ERROR.assertCollNotNull(lifeServiceEntities,
                    LocalConstant.YouKaTeResultConstant.STUDENT_NOT_SIGN);
        }

        IPayModifyBalanceResult result = iPayTradeRefund(param.getSchoolId(), param.getConsumptionMoney(),
                studentEntity.getIdentity(), param.getRefundDesc());
        return YouKaTeQueryBalanceResult.builder()
                .balance(result.getData())
                .identity(studentEntity.getIdentity())
                .cardCode(param.getCardCode())
                .code(result.getCode())
                .message(result.getMsg())
                .consumeAmount(param.getConsumptionMoney())
                .studentName(studentEntity.getStudentName()).build();
    }
}
