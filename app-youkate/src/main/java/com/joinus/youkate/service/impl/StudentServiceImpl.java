package com.joinus.youkate.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.joinus.common.BaseServiceImpl;
import com.joinus.dao.StudentEntity;
import com.joinus.youkate.mapper.StudentMapper;
import com.joinus.youkate.mapper.impl.StudentMapperImpl;
import com.joinus.youkate.service.StudentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.annotation.Reference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service("StudentServiceImpl")
public class StudentServiceImpl extends BaseServiceImpl<StudentMapper, StudentEntity> implements StudentService {

    @Resource
    private StudentMapperImpl studentMapperImpl;
    @Override
    public StudentEntity queryStudentByCardCode(String cardCode, Long schoolId) {
        List<StudentEntity> studentByCardCodeList = studentMapperImpl.findStudentByCardCode(cardCode, schoolId);
        if (CollUtil.isNotEmpty(studentByCardCodeList) && studentByCardCodeList.size() == 1) {
            return studentByCardCodeList.get(0);
        }
        return null;
    }
}
