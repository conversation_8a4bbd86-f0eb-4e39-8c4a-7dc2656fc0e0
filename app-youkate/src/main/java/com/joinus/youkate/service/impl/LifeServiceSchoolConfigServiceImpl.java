package com.joinus.youkate.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.model.enums.LifeServiceTypeEnum;
import com.joinus.dao.LifeServiceSchoolConfigEntity;
import com.joinus.youkate.mapper.LifeServiceSchoolConfigMapper;
import com.joinus.youkate.mapper.impl.LifeServiceSchoolConfigMapperImpl;
import com.joinus.youkate.model.enums.YouKaTeEnableFlagEnum;
import com.joinus.youkate.service.LifeServiceSchoolConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class LifeServiceSchoolConfigServiceImpl extends BaseServiceImpl<LifeServiceSchoolConfigMapper,
        LifeServiceSchoolConfigEntity> implements LifeServiceSchoolConfigService {

    @Resource
    private LifeServiceSchoolConfigMapperImpl lifeServiceSchoolConfigMapperImpl;


    @Override
    public boolean checkSchoolEnableLifeService(Long schoolId, Integer lifeServiceType) {

        List<LifeServiceSchoolConfigEntity> lifeServiceConfigList =
                lifeServiceSchoolConfigMapperImpl.loadLifeServiceSchoolConfigByType(schoolId,
                        CollUtil.toList(lifeServiceType, LifeServiceTypeEnum.LIFE_SERVICE_CONFIG.getCode()));

        if (CollUtil.isNotEmpty(lifeServiceConfigList)) {
            long enableCount =
                    lifeServiceConfigList.stream()
                            .filter(config -> config.getEnableFlag() == YouKaTeEnableFlagEnum.ENABLE.enableFlag)
                            .count();
            if (enableCount == 2) {
                return true;
            }
        }
        return false;
    }
}
