package com.joinus.youkate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.common.model.enums.TerminalSupplierEnum;
import com.joinus.dao.TerminalYoukateEntity;
import com.joinus.youkate.model.params.YoukateTerminalParam;

public interface YouKaTeTerminalService  extends IService<TerminalYoukateEntity> {

    void saveTerminal(YoukateTerminalParam param) throws Exception;

    TerminalYoukateEntity queryTerminalByDeviceNumber(String deviceNumber, TerminalSupplierEnum terminalSupplier) throws Exception;
}
