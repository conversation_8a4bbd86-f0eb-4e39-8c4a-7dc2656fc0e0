package com.joinus.youkate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.joinus.common.BaseServiceImpl;
import com.joinus.common.model.enums.StudentActiveTypeEnum;
import com.joinus.common.model.enums.TerminalSupplierEnum;
import com.joinus.dao.TerminalYoukateEntity;
import com.joinus.youkate.mapper.YoukateTerminalMapper;
import com.joinus.youkate.mapper.impl.YoukateTerminalMapperImpl;
import com.joinus.youkate.model.params.YoukateTerminalParam;
import com.joinus.youkate.service.YouKaTeTerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class YouKaTeTerminalServiceImpl extends BaseServiceImpl<YoukateTerminalMapper, TerminalYoukateEntity> implements YouKaTeTerminalService {

    @Resource
    private YoukateTerminalMapperImpl youkateTerminalMapperImpl;

    @Override
    public void saveTerminal(YoukateTerminalParam param) throws Exception {
        TerminalYoukateEntity terminalYoukate = youkateTerminalMapperImpl.queryByDeviceNumber(param.getDeviceNumber()
                , param.getTerminalSupplier());
        if (null != terminalYoukate) {
            throw new Exception("设备已存在");
        }
        if (null == terminalYoukate) {
            TerminalYoukateEntity terminalYoukateEntity = BeanUtil.copyProperties(param, TerminalYoukateEntity.class);
            terminalYoukateEntity.setIsactive(StudentActiveTypeEnum.NORMAL.getIsActive());
            terminalYoukateEntity.setType(param.getType().getTableType());
            terminalYoukateEntity.setDeviceSupplier(param.getTerminalSupplier().getCode());
            baseMapper.insert(terminalYoukateEntity);
        }
    }

    @Override
    public TerminalYoukateEntity queryTerminalByDeviceNumber(String deviceNumber, TerminalSupplierEnum terminalSupplier) throws Exception {
        return youkateTerminalMapperImpl.queryByDeviceNumber(deviceNumber, terminalSupplier);
    }
}
