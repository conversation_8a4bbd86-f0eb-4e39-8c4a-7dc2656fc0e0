package com.joinus.youkate.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.common.BaseController;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.enums.InterfaceResultEnum;
import com.joinus.common.model.enums.LifeServiceTypeEnum;
import com.joinus.common.model.enums.TerminalSupplierEnum;
import com.joinus.common.model.response.ApiResponse;
import com.joinus.dao.TerminalYoukateEntity;
import com.joinus.youkate.common.LocalConstant;
import com.joinus.youkate.model.params.*;
import com.joinus.youkate.model.result.YouKaTeGetStudentResult;
import com.joinus.youkate.model.result.YouKaTeModifyBalanceResult;
import com.joinus.youkate.model.result.YouKaTeQueryBalanceResult;
import com.joinus.youkate.model.result.YouKaTeResult;
import com.joinus.youkate.service.YouKaTeService;
import com.joinus.youkate.service.YouKaTeTerminalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 优卡特控制器
 *
 * <AUTHOR> anpy
 * @create 2023/6/29 10:12
 */
@Slf4j
@RestController
@Api(value = "/youkate", tags = "优卡特相关接口")
@RequestMapping("/api/public/youkate")
public class YouKaTeController extends BaseController {

    @Value("${youkate_service_url}")
    private String serviceUrl;
    @Resource
    private YouKaTeService youKaTeService;
    @Resource
    private YouKaTeTerminalService youKaTeTerminalService;

    @ApiOperation(value = "获取优卡特平台学生信息")
    @GetMapping("/student/detail")
    public ResultVO studentDetail(YouKaTeGetUserParam param) {
        try {
            YouKaTeGetStudentResult studentInfo = youKaTeService.getStudentInfo(param, "");
            return ResultVO.ok(studentInfo);
        } catch (Exception e) {
            return ResultVO.error("学生不存在");
        }
    }

    @ApiOperation(value = "向优卡特平台添加学生")
    @PostMapping("/student/add")
    public ResultVO add(YouKaTeAddUserParam param) {
        YouKaTeResult studentInfo = youKaTeService.addStudentInfo(param, "");
        return ResultVO.ok(studentInfo);
    }

    @ApiOperation(value = "优卡特平台更新学生")
    @PostMapping("/student/update")
    public ResultVO update(YouKaTeUpdateUserParam param) {
        YouKaTeResult studentInfo = youKaTeService.updateStudentInfo(param, "");
        return ResultVO.ok(studentInfo);
    }

    @ApiOperation(value = "向优卡特平台删除学生")
    @PostMapping("/student/delete")
    public ResultVO delete(YouKaTeParam param) {
        YouKaTeResult studentInfo = youKaTeService.deleteStudentInfo(param, "");
        return ResultVO.ok(studentInfo);
    }

    @ApiOperation(value = "全量同步所有学生")
    @PostMapping("/student/syncAllStudent")
    public ResultVO syncAllStudent(Long schoolId) {
        youKaTeService.syncAllStudent(schoolId, "");
        return ResultVO.ok();
    }

    @ApiOperation(value = "增量同步学生")
    @PostMapping("/student/syncIncrementStudent")
    public ResultVO syncIncrementStudent(Long schoolId, String minute) {
        youKaTeService.incrementStudent(schoolId, "", minute);
        return ResultVO.ok();
    }

    @ApiOperation(value = "删除平台所有学生(测试用)")
    @PostMapping("/student/deleteAllStudent")
    public ResultVO deleteAllStudent(Long schoolId) {
        youKaTeService.deleteAllStudent(schoolId, "");
        return ResultVO.ok();
    }

    @ApiOperation(value = "扣款消费接口")
    @GetMapping("/home/<USER>")
    public YouKaTeModifyBalanceResult modifyBalance(@RequestHeader("schoolid") Long currentSchoolId, YouKaTeModifyBalanceParam param) throws Exception {
        System.out.println("扣款消费接口：" + param);
        JSONArray jsonArray = JSONUtil.parseArray(serviceUrl);
        if (jsonArray.size() > 0) {
            for (Object object : jsonArray) {
                YouKaTeServiceUrlParam youKaTeServiceUrlParam = JSONUtil.toBean((JSONObject) object, YouKaTeServiceUrlParam.class);
                if (currentSchoolId.equals(youKaTeServiceUrlParam.getSchoolId())) {
                    return youKaTeService.modifyBalance(youKaTeServiceUrlParam.getSchoolId(), param, youKaTeServiceUrlParam.getUrl());
                }
            }
        }
        return YouKaTeModifyBalanceResult.builder()
                .code(InterfaceResultEnum.YOUKATE_FAIL.getCode())
                .message(LocalConstant.YouKaTeResultConstant.SCHOOL_NOT_EXIST)
                .build();
    }

    @ApiOperation(value = "配置脸爱云设备信息")
    @PostMapping("/platform/terminal")
    public ResultVO saveTerminal(@Validated @RequestBody YoukateTerminalParam param) throws Exception {
        youKaTeTerminalService.saveTerminal(param);
        return ResultVO.ok();
    }

    @ApiOperation(value = "配置脸爱云设备信息")
    @GetMapping("/platform/terminal")
    public ResultVO queryTerminal(String deviceNumber) throws Exception {
        TerminalYoukateEntity termianl = youKaTeTerminalService.queryTerminalByDeviceNumber(deviceNumber, TerminalSupplierEnum.YOU_KA_TE);
        return ResultVO.ok(termianl);
    }


    @ApiOperation(value = "刷卡设备——消费")
    @PostMapping("/swipingCard/modifyBalance")
    public ApiResponse<YouKaTeQueryBalanceResult> modifyBalance(@RequestBody SwipingCardModifyBalanceParam param) {
        YouKaTeQueryBalanceResult youKaTeQueryBalanceResult = youKaTeService.swipingCardModifyBalance(param);
        if (youKaTeQueryBalanceResult.getCode().equals(String.valueOf(InterfaceResultEnum.SUCCESS.getCode()))) {
            return ApiResponse.success(youKaTeQueryBalanceResult);
        }
        return ApiResponse.result(Integer.valueOf(youKaTeQueryBalanceResult.getCode()),youKaTeQueryBalanceResult.getMessage());
    }
}
