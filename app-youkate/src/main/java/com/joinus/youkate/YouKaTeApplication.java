package com.joinus.youkate;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
* 入口
* <AUTHOR> anpy
* @create 2023/6/28 15:25
*/
@EnableCaching
@EnableTransactionManagement(proxyTargetClass=true)
@MapperScan(basePackages = {"com.joinus.youkate.mapper","com.joinus.common.mapper"})
@SpringBootApplication
@ComponentScan(basePackages = {"com.joinus.youkate","com.joinus.common"})
public class YouKaTeApplication {
    public static void main(String[] args) {
        SpringApplication.run(YouKaTeApplication.class);
    }

}