spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher #集成swagger后报错
  datasource:
    dynamic:
      primary: oracle
      strict: false
      datasource:
        oracle:
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ${jdbc.url}
          username: ijx
          password: ijxuat9671-hwy
  application:
    name: app-youkate
  redis:
    host: 127.0.0.1
    port: 6379

server:
  port: 9095

logging:
  level:
    com.joinus.basic.mapper: debug
    org.apache.kafka.clients: ERROR
    com.xxl.job.core.log.XxlJobLogger: OFF

apollo:
  bootstrap:
    enabled: true
    namespaces: application,basic.common.database
    eagerLoad:
      enabled: true

mybatis-plus:
  configuration:
    jdbc-type-for-null: null
  global-config:
    db-config:
      type: oracle
      id-type: input
netty:
  port: 3009
management:
  endpoint:
    health:
      enabled: true
      show-details: always
