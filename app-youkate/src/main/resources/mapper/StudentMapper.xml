<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.youkate.mapper.StudentMapper">

    <select id="loadStudentByYouKaTeCount" resultType="int">
        SELECT
            COUNT(DISTINCT t.id)
        FROM T_STUDENT t
                 INNER JOIN T_LIFE_SERVICE_AGREEMENT lsa ON lsa.STUDENT_ID = t.ID AND lsa.AGREEMENT_STATE  = 1 AND lsa.ISACTIVE = 1
                 LEFT JOIN T_CLASS tc ON t.CLASS_ID = tc.ID
        WHERE t.IDENTITY IS NOT NULL
          AND t.STUDENT_IMG  IS NOT NULL
          AND t.CARDCODE IS NOT NULL
          AND t.ISACTIVE = 1
          AND t.SCHOOL_ID = #{schoolId}
    </select>

    <select id="loadStudentByYouKaTe" resultType="com.joinus.youkate.model.params.YouKaTeAddUserParam">
        SELECT
            DISTINCT t.id "studentId",
            t.IDENTITY "userNumber",
            t.STUDENT_NAME "userName",
            t.TEL_NUM "userPhone",
            tc.CLASS_NAME "department",
            '学生' AS "position",
            t.CARDCODE "userCard",
            '' AS "userMailbox",
            t.STUDENT_IMG "faceImages",
            t."IDENTITY" "identityId"
        FROM T_STUDENT t
                 INNER JOIN T_LIFE_SERVICE_AGREEMENT lsa ON lsa.STUDENT_ID = t.ID AND lsa.AGREEMENT_STATE  = 1 AND lsa.ISACTIVE = 1
                 LEFT JOIN T_CLASS tc ON t.CLASS_ID = tc.ID
        WHERE t.IDENTITY IS NOT NULL
          AND t.STUDENT_IMG  IS NOT NULL
          AND t.CARDCODE IS NOT NULL
          AND t.ISACTIVE = 1
          AND t.SCHOOL_ID = #{schoolId}
    </select>

    <select id="loadIncrementStudent" resultType="com.joinus.youkate.model.params.YouKaTeAddUserParam">
        SELECT
            DISTINCT t.id "studentId",
            t.IDENTITY "userNumber",
               t.STUDENT_NAME "userName",
               t.TEL_NUM "userPhone",
               tc.CLASS_NAME "department",
               '学生' AS "position",
               t.CARDCODE "userCard",
               '' AS "userMailbox",
               t.STUDENT_IMG "faceImages",
               t."IDENTITY" "identityId"
        FROM T_STUDENT t
                    INNER JOIN T_LIFE_SERVICE_AGREEMENT lsa ON lsa.STUDENT_ID = t.ID AND lsa.AGREEMENT_STATE  = 1 AND lsa.ISACTIVE = 1
                    LEFT JOIN T_CLASS tc ON t.CLASS_ID = tc.ID
        WHERE (lsa.UPDATE_TIME > sysdate - #{minute} / (24 * 60) OR t.UPDATE_TIME > sysdate - #{minute} / (24 * 60))
          AND t.SCHOOL_ID = #{schoolId}
          AND t.IDENTITY IS NOT NULL
          AND t.STUDENT_IMG  IS NOT NULL
          AND t.CARDCODE IS NOT NULL
          AND t.ISACTIVE = 1
    </select>

    <select id="loadStudentByYouKaTeDeleteCount" resultType="int">
        SELECT
            count(*)
        FROM T_STUDENT t
                 LEFT JOIN T_CLASS tc ON t.CLASS_ID = tc.ID
        WHERE t.IDENTITY IS NOT NULL
          AND t.STUDENT_IMG  IS NOT NULL
          AND t.ISACTIVE = 1
          AND t.SCHOOL_ID = #{schoolId}
    </select>

    <select id="loadStudentByDeleteYouKaTe" resultType="com.joinus.youkate.model.params.YouKaTeAddUserParam">
        SELECT
            t.IDENTITY "userNumber",
            t.STUDENT_NAME "userName",
            t.TEL_NUM "userPhone",
            tc.CLASS_NAME "department",
            '学生' AS "position",
            t.CARDCODE "userCard",
            '' AS "userMailbox",
            t.STUDENT_IMG "faceImages",
            t."IDENTITY" "identityId",
            t.id "studentId"
        FROM T_STUDENT t
                 LEFT JOIN T_CLASS tc ON t.CLASS_ID = tc.ID
        WHERE t.IDENTITY IS NOT NULL
          AND t.STUDENT_IMG  IS NOT NULL
          AND t.ISACTIVE = 1
          AND t.SCHOOL_ID = #{schoolId}
    </select>
</mapper>