apply plugin: 'java-library'

dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'org.projectlombok:lombok'
    api 'org.springframework.boot:spring-boot-starter-web'
    api 'com.baomidou:mybatis-plus-boot-starter'
//    api 'com.baomidou:mybatis-plus-extension'
    //  gateway
//    api 'org.springframework.cloud:spring-cloud-starter-bootstrap'
    //  nacos
//    api 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config'
//    api 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery'
    api 'com.alibaba.fastjson2:fastjson2'
    api 'cn.hutool:hutool-all'
    api 'mysql:mysql-connector-java'
    api 'com.oracle.database.jdbc:ojdbc8'
    api 'com.baomidou:dynamic-datasource-spring-boot-starter'
    api 'cn.easyproject:orai18n'
    api 'javax.validation:validation-api'
    api 'org.springframework.boot:spring-boot-starter-validation'
    api 'org.apache.httpcomponents:httpasyncclient'
    api 'com.ctrip.framework.apollo:apollo-client'
    api 'io.micrometer:micrometer-registry-prometheus'
    api 'org.springframework.kafka:spring-kafka'
    api 'com.github.xiaoymin:knife4j-spring-boot-starter'
    api 'com.xuxueli:xxl-job-core'
    api ('org.slf4j:slf4j-api') {
        exclude group: 'org.slf4j', module: 'slf4j-api'
    }

    api 'org.springframework.kafka:spring-kafka'

    api 'org.springframework.boot:spring-boot-starter-data-redis'
    api 'com.qiniu:qiniu-java-sdk'

    api project(':dao')

    implementation 'com.alibaba.mqtt:server-sdk'
}