app:
  id: wx-videophone
apollo:
  bootstrap:
    enabled: true
    namespaces: application,basic.common.database
    eagerload:
      enabled: true
#
#
spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher #集成swagger后报错
  datasource:
    dynamic:
      primary: oracle
      strict: false
      datasource:
        oracle:
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ${jdbc.url}
          username: ijx
          password: ijxuat9671-hwy
  application:
    name: snake-videophone-wx-miniapp
  redis:
    host: ************
    port: 6379
    password:
    database: 0
  #cloud:
#    nacos:
#      discovery:
#        server-addr: 127.0.0.1:8848
server:
  port: 9093
  servlet:
    context-path: /api/videophone

logging:
  level:
    com.joinus.wx.videophone.mapper: info
#
#apollo:
#  bootstrap:
#    enabled: true
#    eagerload:
#      enabled: true
#
mybatis-plus:
  global-config:
    db-config:
      insert-strategy:
        not_null
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
wx:
  miniapp:
    config-storage:
      type: redistemplate
      redis:
        host: *************
        port: 6380
        password: 123456
        database: 0
    appid: wxd8a79383a44703ae
    secret: 9a2c6d0d20b4ee0b464bd4f53e2954ca

springfox:
  documentation:
    swagger:
      use-model-v3: false

management:
  endpoint:
    health:
      enabled: true
      show-details: always
