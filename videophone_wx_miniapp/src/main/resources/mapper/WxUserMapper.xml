<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.wx.videophone.mapper.WxUserMapper">

    <select id="loadOpenIdByUnionId" resultType="String">
        SELECT OPENID,USER_TYPE
        FROM T_WX_USER twu
        WHERE twu.UNION_ID = (SELECT UNION_ID FROM T_WX_USER twu WHERE twu.PHONE = #{phone} AND USER_TYPE = #{userType})
            AND USER_TYPE = 2
    </select>
</mapper>