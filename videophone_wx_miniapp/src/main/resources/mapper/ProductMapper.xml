<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.wx.videophone.mapper.ProductMapper">


    <select id="loadGivenDuration" resultType="String">
        SELECT a."givenDuration" FROM (
            SELECT DISTINCT (p.id),
                   s.GIVEN_DURATION "givenDuration"
            FROM sys_product_school s
                 LEFT JOIN t_product_self p ON p.id = s.product_id
                 LEFT JOIN T_PRODUCT_SELF_SUB ss ON p.ID = ss.PRODUCT_ID
            WHERE s.PRODUCT_TYPE = 1
                  AND p.PRODUCT_TYPE = 3
            <if test="null != schoolId">
                AND s.school_id = #{schoolId}
            </if>
        ) a
    </select>

    <select id="getProductMinutePrice" resultType="int">
        SELECT tpss.COST_PER_MINUTE
        FROM SYS_PRODUCT_SCHOOL sps , T_PRODUCT_SELF tps ,T_PRODUCT_SELF_SUB tpss
        WHERE sps.PRODUCT_ID = tps.ID
          AND sps.SCHOOL_ID = #{schoolId}
          AND tps.ID = tpss.PRODUCT_ID
          AND tps.PRODUCT_TYPE = 1
          AND sps.product_type = 1
        <if test="null != gradeId">
            AND sps.grade_id = #{gradeId}
        </if>
    </select>
</mapper>