<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.wx.videophone.mapper.ParentMapper">

    <resultMap id="ParentInfo" type="com.joinus.wx.videophone.model.dto.ParentDTO">
        <result property="parentId" column="id" jdbcType="BIGINT"/>
        <result property="parentName" column="PARENT_NAME" jdbcType="VARCHAR"/>
        <result property="childRelation" column="CHILD_RELATION" jdbcType="VARCHAR"/>
        <result property="telNum" column="TEL_NUM" jdbcType="VARCHAR"/>
    </resultMap>
    
    <select id="parentList" resultMap="ParentInfo">
        SELECT
            tp.id,
            tp.PARENT_NAME ,
            tsp.CHILD_RELATION ,
            tp.TEL_NUM
        FROM
            T_PARENT tp ,
            T_STUDENT_PARENT tsp
        WHERE
            tp.id = tsp.PARENT_ID
          AND tsp.STUDENT_ID = #{studentId}
          AND tp.ISACTIVE = 1
    </select>
</mapper>