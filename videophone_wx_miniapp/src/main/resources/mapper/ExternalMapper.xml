<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.wx.videophone.mapper.ExternalPlatformLinkMapper">

    <select id="loadGroupIdAndModelIdBySn" resultType="com.joinus.wx.videophone.model.response.GroupIdResponse">
        SELECT tt.id "terminalId",tepl.id, tt.wx_videophone_group_id, tepl.EXTERNAL_ID "groupId", tepl.MODEL_ID "modelId"
        FROM adapter.t_external_platform_link tepl
                 JOIN T_TERMINAL tt on tepl.ID =  tt.WX_VIDEOPHONE_GROUP_ID
        WHERE tt.sn = #{sn}    </select>

    <delete id="delById">
        DELETE FROM adapter.t_external_platform_link WHERE id = #{id}
    </delete>

</mapper>