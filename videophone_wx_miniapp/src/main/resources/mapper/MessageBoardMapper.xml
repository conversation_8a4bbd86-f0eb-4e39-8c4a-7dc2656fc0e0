<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.wx.videophone.mapper.MessageBoardMapper">

    <!-- 小程序首页学生消息列表，只显示第一条   -->
    <select id="messageBoardListByParent" resultType="com.joinus.wx.videophone.model.response.MessageBoardHomeResponse">
        WITH ranked_messages AS (
            SELECT
                tlsmb.id,
                ts.STUDENT_NAME,
                tp.id AS parent_id,
                ts.id AS student_id,
                tp.ISACTIVE ,
                s.SCHOOL_NATURE,
                case when tlsa.AGREEMENT_STATE is null then 0 else tlsa.AGREEMENT_STATE end as AGREEMENT_STATE,
                case when tscd.TOTAL_CALL_DURATION is null then 0 else tscd.TOTAL_CALL_DURATION end as TOTAL_CALL_DURATION,
                case when tscd.USED_VIDEO_DURATION is null then 0 else tscd.USED_VIDEO_DURATION end as USED_VIDEO_DURATION,
                case when tlssc.ENABLED_FLAG is null then 0 else tlssc.ENABLED_FLAG end as ENABLED_FLAG,
                MIN(CASE
                        WHEN (tlsmb.read_state = 0 AND tlsmb.SENDER_TYPE = 0) THEN 0
                        ELSE 1
                        END) OVER (
                        PARTITION BY tlsmb.parent_id,
                            tlsmb.student_id
                        ) AS hasNoreadMessage,
                tlsmb.CREATE_TIME,
                tlsmb.MESSAGE_TYPE,
                tlsmb.TXT_MESSAGE,
                ROW_NUMBER() OVER (
                    PARTITION BY ts.id
                    ORDER BY tlsmb.CREATE_TIME DESC
                    ) AS rn
            FROM
                T_STUDENT ts
                    left JOIN T_STUDENT_CALL_DURATION tscd on tscd.student_id = ts.id
                    INNER JOIN T_SCHOOL s on s.id = ts.SCHOOL_ID
                    INNER JOIN T_STUDENT_PARENT tsp ON tsp.STUDENT_ID = ts.id AND ts.ISACTIVE = 1
                    INNER JOIN T_PARENT tp ON tp.id = tsp.PARENT_ID AND tp.id = #{parentId} AND tp.ISACTIVE =  1
                    left JOIN T_LIFE_SERVICE_AGREEMENT tlsa
                              ON tlsa.STUDENT_ID = ts.ID
                                  and tlsa.TYPE = 1
                                  AND tlsa.ISACTIVE = 1
                    left join T_LIFE_SERVICE_SCHOOL_CONFIG tlssc on tlssc.SCHOOL_ID = s.id and tlssc.TYPE = 1
                    LEFT JOIN T_LIFE_SERVICE_MSG_BOARD tlsmb ON tlsmb.student_id = ts.id AND tlsmb.PARENT_ID = #{parentId} AND tlsmb.CREATE_TIME > (SYSTIMESTAMP - NUMTODSINTERVAL(30, 'DAY'))  AND tlsmb.READ_STATE  IN (0,1)
        )
        SELECT
            id,
            STUDENT_NAME,
            parent_id,
            student_id,
            hasNoreadMessage,
            CREATE_TIME,
            MESSAGE_TYPE,
            TXT_MESSAGE,
            ISACTIVE,
            SCHOOL_NATURE,
            AGREEMENT_STATE,
            TOTAL_CALL_DURATION,
            USED_VIDEO_DURATION,
            ENABLED_FLAG
        FROM
            ranked_messages
        WHERE
            rn = 1
    </select>

    <select id="loadMessageBoard" resultType="com.joinus.wx.videophone.model.response.MessageBoardData">
        SELECT tlsmb.*,
               s.STUDENT_NAME,
               tsp.CHILD_RELATION
        FROM T_LIFE_SERVICE_MSG_BOARD tlsmb
                 LEFT JOIN T_STUDENT s ON tlsmb.STUDENT_ID = s.ID
                 LEFT JOIN T_PARENT tp ON tp.id = tlsmb.PARENT_ID
                 LEFT JOIN T_STUDENT_PARENT tsp ON tsp.PARENT_ID = tp.id AND tsp.STUDENT_ID = s.id
        WHERE tlsmb.ISACTIVE = 1
          AND tlsmb.STUDENT_ID = #{studentId}
          AND tlsmb.PARENT_ID = #{parentId}
          AND tlsmb.READ_STATE in (0, 1)
          AND s.ISACTIVE = 1
          AND CREATE_TIME > (SYSTIMESTAMP - NUMTODSINTERVAL(30, 'DAY'))
        ORDER BY tlsmb.CREATE_TIME desc
    </select>

    <select id="pagingList" resultType="com.joinus.wx.videophone.model.response.MessageBoardListResponse">
        select *
        from (SELECT student_id, ts.STUDENT_NAME, COUNT(read_state) as notReadNum
              FROM T_LIFE_SERVICE_MSG_BOARD lsmb,
                   T_STUDENT ts
              WHERE lsmb.read_state = 0
                and lsmb.school_id = #{schoolId}
                AND ts.ISACTIVE = 1
                AND lsmb.SENDER_TYPE = 1
                AND lsmb.STUDENT_ID = ts.id
                and lsmb.SCHOOL_ID = ts.SCHOOL_ID
              GROUP BY lsmb.student_id, ts.STUDENT_NAME
              ORDER BY lsmb.student_id)
    </select>

    <select id="messageBoardById" resultType="com.joinus.wx.videophone.model.response.MessageBoardData">
        select * from T_LIFE_SERVICE_MSG_BOARD where id = #{id}
    </select>
    <select id="getCabinetMsg" resultType="com.joinus.wx.videophone.model.response.CabinetMsgResponse">
        SELECT s.SCHOOL_ID, sc.SCHOOL_NAME, s.STUDENT_NAME,h.STUDENT_ID,cg.GROUP_NAME,cg.LOCATION,cc.BOX_ID,h.DEPOSIT_TIME
        FROM T_YKT_CABINET_HISTORY h
                 INNER JOIN T_STUDENT s ON s.id = h.STUDENT_ID and s.ISACTIVE = 1
                 inner join T_SCHOOL sc on sc.id = s.SCHOOL_ID
                 LEFT JOIN T_CABINET_ORDER o ON o.ORDER_NO = h.DEPOSIT_NO
                 INNER JOIN T_YKT_CABINET_GROUP cg on cg.id = h.GROUP_ID
                 INNER JOIN T_YKT_CABINET_CELL cc on cc.id = h.CELL_ID
        WHERE h.RECEIVE_TIME IS NULL
          AND s.id = #{studentId}
          AND ( h.HISTORY_TYPE = 0 OR h.HISTORY_TYPE = 4 OR ( h.HISTORY_TYPE in (1,3) AND o.SVC_STATE IN ( 3, 5 ) ) )
        order by h.DEPOSIT_TIME desc
    </select>
    <select id="getStudentsMsg" resultType="com.joinus.wx.videophone.model.response.MessageBoardListResponse">
        SELECT nvl(s1.id,s2.id) as student_id,nvl(s1.STUDENT_NAME,s2.STUDENT_NAME) as student_name,
               nvl(s1.CLASS_NAME,s2.CLASS_NAME) as class_name,nvl(s1.num,0)+nvl(s2.num,0) as not_read_num
        FROM
            (SELECT s.id, s.STUDENT_NAME,c.CLASS_NAME, COUNT(mb.id) AS num
             FROM T_LIFE_SERVICE_MSG_BOARD mb
                      INNER JOIN t_student s  ON mb.STUDENT_ID = s.id and s.ISACTIVE = 1
                      inner join T_CLASS c on c.id = s.CLASS_ID and c.ISACTIVE = 1
             WHERE mb.READ_STATE = 0
               AND mb.SENDER_TYPE = 1
               AND s.SCHOOL_ID = #{schoolId}
             GROUP BY s.id, s.STUDENT_NAME,c.CLASS_NAME) s1
                FULL OUTER JOIN
            (SELECT s.id, s.STUDENT_NAME, c.CLASS_NAME,COUNT(h.id) AS num
             FROM T_YKT_CABINET_HISTORY h
                      INNER JOIN T_STUDENT s ON s.id = h.STUDENT_ID and s.ISACTIVE = 1
                      inner join T_CLASS c on c.id = s.CLASS_ID and c.ISACTIVE = 1
                      LEFT JOIN T_CABINET_ORDER o ON o.ORDER_NO = h.DEPOSIT_NO
             WHERE h.RECEIVE_TIME IS NULL
               AND s.SCHOOL_ID = #{schoolId}
               AND (h.HISTORY_TYPE = 0 OR h.HISTORY_TYPE = 4 OR
                    (h.HISTORY_TYPE IN (1, 3) AND o.SVC_STATE IN (3, 5)))
             GROUP BY s.id, s.STUDENT_NAME,c.CLASS_NAME) s2
            ON s1.id = s2.id
    </select>
    <select id="getClassStudentsMsg"
            resultType="com.joinus.wx.videophone.model.response.MessageBoardListResponse">
        SELECT s.id as student_id, s.STUDENT_NAME,c.CLASS_NAME, COUNT(mb.id) AS notReadNum
        FROM T_LIFE_SERVICE_MSG_BOARD mb
                 INNER JOIN t_student s  ON mb.STUDENT_ID = s.id and s.ISACTIVE = 1
                 inner join T_CLASS c on c.id = s.CLASS_ID and c.ISACTIVE = 1
        WHERE mb.READ_STATE = 0
          AND mb.SENDER_TYPE = 1
          AND s.CLASS_ID = #{classId}
        GROUP BY s.id, s.STUDENT_NAME,c.CLASS_NAME
    </select>
</mapper>