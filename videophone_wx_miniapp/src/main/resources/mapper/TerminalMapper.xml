<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.wx.videophone.mapper.WxUserTerminalMapper">

    <resultMap id="TerminalResponse" type="com.joinus.common.model.TerminalDTO">
        <result property="terminalNum" column="TERMINAL_NUM" jdbcType="VARCHAR"/>
        <result property="authType" column="auth_type" jdbcType="INTEGER"/>
        <result property="sn" column="sn" jdbcType="VARCHAR"/>
        <result property="modelId" column="model_id" jdbcType="VARCHAR"/>
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="wxVideoPhoneGroupId" column="wx_videophone_group_id" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="loadAuthTerminalList" resultMap="TerminalResponse">
        SELECT TERMINAL_NUM,
               (CASE
                    WHEN twut.auth_type IS NULL THEN 0
                    ELSE twut.auth_type
                   END)         "auth_type",
               tt.SN,
               twut.id,
               tepl.model_id,
               tepl.EXTERNAL_ID "wx_videophone_group_id"
        FROM T_TERMINAL tt
                 LEFT JOIN T_WX_USER_TERMINAL twut
                           ON
                                       twut.TERMINAL_CODE = tt.TERMINAL_NUM
                                   AND (twut.STUDENT_ID = #{param.studentId} or twut.parent_id = #{param.parentId})
                                   AND twut.is_active = 1
                 inner JOIN adapter.T_EXTERNAL_PLATFORM_LINK tepl ON
                    tepl.SCHOOL_ID = tt.SCHOOL_ID
                AND tepl.PLATFORM_TYPE = #{platformType}
                AND tepl.EXTERNAL_ID_TYPE = #{externalIdType}
                AND tepl.ID = tt.WX_VIDEOPHONE_GROUP_ID
                AND tepl.isactive = 1
        WHERE tt.WX_VIDEOPHONE_GROUP_ID IS NOT NULL
          AND tt.SCHOOL_ID = #{param.schoolId}
          AND tt.ISACTIVE = 1
    </select>

    <select id="loadAuthTerminalListByParam" resultMap="TerminalResponse">
        SELECT
            DISTINCT TERMINAL_NUM ,
            (CASE
                 WHEN twut.auth_type IS NULL THEN 0
                 ELSE twut.auth_type
                END ) "auth_type",
            tt.SN,
            tepl.model_id,
            tepl.EXTERNAL_ID "wx_videophone_group_id"
        FROM
            T_TERMINAL tt
                LEFT JOIN T_WX_USER_TERMINAL twut
                          ON
                                      twut.TERMINAL_CODE = tt.TERMINAL_NUM
                                  AND (twut.STUDENT_ID = #{param.studentId} or twut.parent_id = #{param.parentId})
                                  AND twut.is_active = 1
                LEFT JOIN T_WX_USER twu
                          ON
                                      twu.OPENID = twut.OPENID
                                  AND twut.OPENID = #{param.openId}
                                  AND twu.SCHOOL_ID = #{param.schoolId}
                inner JOIN adapter.T_EXTERNAL_PLATFORM_LINK tepl ON
                        tepl.SCHOOL_ID = tt.SCHOOL_ID
                    AND tepl.PLATFORM_TYPE = #{platformType}
                    AND tepl.EXTERNAL_ID_TYPE = #{externalIdType}
                    AND tepl.ID = tt.WX_VIDEOPHONE_GROUP_ID
                    AND tepl.isactive = 1
        WHERE
            tt.WX_VIDEOPHONE_GROUP_ID IS NOT NULL
          AND tt.SCHOOL_ID = #{param.schoolId}
          AND tt.ISACTIVE = 1
    </select>

</mapper>