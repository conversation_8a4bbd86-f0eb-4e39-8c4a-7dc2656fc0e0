<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.wx.videophone.mapper.StudentMapper">

    <select id="loadStudentListByPhone" resultType="com.joinus.wx.videophone.model.dto.StudentDTO">
        SELECT tp.id                     "parent_id",
               t.id                      "studentId",
               t.STUDENT_NAME,
               tc.CLASS_NAME,
               s.SCHOOL_NAME,
               t.school_id,
               tg.GRADE_NAME,
               tg.id                     "gradeId",
               t.STUDENT_IMG,
               s.TEN_QINPHONE,
               s.BIZ_CONFIG_METHOD,
               t."IDENTITY"              "identity",
               tbs.ID                    "businessId",
               tepl.external_id          "wxVideoPhoneGroupNum",
               tepl.id                   "wxVideoPhoneGroupId",
                s.SCHOOL_NATURE          "schoolNature",
               (select distinct tlsa.STUDENT_ID
                from T_LIFE_SERVICE_AGREEMENT tlsa
                where tlsa.STUDENT_ID = t.ID
                  and tlsa.PARENT_ID = tp.ID
                  AND tlsa.AGREEMENT_STATE = 1
                  AND tlsa.ISACTIVE = 1) "signedAgreement",
               (SELECT CASE
                           WHEN EXISTS(
                                   SELECT 1
                                   FROM T_WX_USER_TERMINAL twut
                                   WHERE twut.parent_id = tp.id
                                     AND twut.is_active = 1
                                     and twut.SCHOOL_ID = s.id
                                     AND tepl.id = twut.GROUP_ID
                               ) THEN 1
                           ELSE 0
                           END AS FLAG
                FROM DUAL)               "authType",
               (SELECT CASE
                           WHEN EXISTS(
                                   SELECT 1
                                   FROM T_LIFE_SERVICE_SCHOOL_CONFIG
                                   WHERE school_id = s.id
                                     AND type IN (1)
                                     AND ENABLED_FLAG = 0
                               ) THEN 0
                           ELSE 1
                           END AS FLAG
                FROM DUAL)               "lifeServiceEnable"
        FROM ijx.T_STUDENT t
                 inner join ijx.T_PARENT tp on tp.TEL_NUM = #{phone}
                 inner join ijx.T_STUDENT_PARENT tsp on tsp.STUDENT_ID = t.id and tsp.PARENT_ID = tp.id
                 inner join ijx.T_CLASS tc on t.CLASS_ID = tc.ID
                 inner join ijx.T_GRADE tg on tg.ID = tc.GRADE_ID
                 inner join ijx.T_SCHOOL s on s.id = t.SCHOOL_ID
                 left join adapter.t_external_platform_link tepl
                           on tepl.EXTERNAL_ID_TYPE = 3 and tepl.PLATFORM_TYPE = 2 and tepl.ISACTIVE = 1 and
                              tepl.school_id = s.id
                 left join T_BUSINESS_SELF tbs on tbs.SCHOOL_ID = s.id and tbs.STUDENT_ID = t.ID AND tbs.BUSINESS_TYPE = 3
        WHERE s.id = t.SCHOOL_ID
          AND t.ISACTIVE = 1
          AND tp.ISACTIVE = 1
          AND tc.ISACTIVE = 1
          AND tg.ISACTIVE = 1
          AND s.ISACTIVE = 1
    </select>

    <select id="loadStudentParentBindState" resultType="long">
        SELECT s.id
        FROM T_STUDENT s,
             T_PARENT p,
             T_STUDENT_PARENT tsp
        WHERE p.id = tsp.PARENT_ID
          AND p.id = tsp.parent_id
          AND s.id = tsp.STUDENT_ID
          AND s.id = #{studentId}
          AND p.id = #{parentId}
    </select>

    <select id="loadTerminalByWxVideoPhoneId" resultType="int">
        SELECT count(1) FROM T_TERMINAL tt
                 WHERE tt.WX_VIDEOPHONE_GROUP_ID = #{wxVideoPhoneId}
                   AND tt.SCHOOL_ID = #{schoolId}
    </select>
</mapper>