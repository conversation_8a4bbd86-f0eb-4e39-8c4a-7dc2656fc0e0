package com.joinus.wx.videophone.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.common.common.BaseController;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.OperatorVO;
import com.joinus.common.model.param.CreateGroupParam;
import com.joinus.dao.SchoolEntity;
import com.joinus.wx.videophone.mapper.impl.SchoolMapperImpl;
import com.joinus.wx.videophone.model.param.*;
import com.joinus.wx.videophone.model.response.WxAddDeviceResponse;
import com.joinus.wx.videophone.model.response.WxShowGroupResponse;
import com.joinus.wx.videophone.service.WxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "微信交互接口（测试用）")
@Slf4j
@RestController
@RequestMapping("/wx")
public class WxController extends BaseController {

    @Resource
    private WxService weChatService;
    @Resource
    private SchoolMapperImpl schoolMapperImpl;
    /**
     * 获取微信token
     *
     * @return token
     */
    @GetMapping("/token")
    @ApiOperation(value = "获取微信平台接口token", notes = "获取微信平台接口token", response = ResultVO.class)
    public ResultVO token() {
        String token = weChatService.getServerAccessToken();
        return ResultVO.ok().putData("token", token);
    }

    @PostMapping("/group/show")
    @ApiOperation(value = "获取微信平台设备组下设备", notes = "获取微信平台设备组下设备", response = ResultVO.class)
    public ResultVO showGroup(String groupId) {
        String response = weChatService.showGroupDevices(groupId);
        WxShowGroupResponse showGroupResponse = JSONUtil.toBean(response, WxShowGroupResponse.class);
        return ResultVO.ok().putData("data",showGroupResponse);
    }

    @PostMapping("/group/addWxDevice")
    @ApiOperation(value = "向微信平台添加设备（admin平台调用）", notes = "向微信平台添加设备（admin平台调用）", response = ResultVO.class)
    public WxAddDeviceResponse addWxDevice(HttpServletRequest request, @RequestBody AddDeviceParam addDeviceParam) {
        OperatorVO operatorVO = getUserType(request);
        return weChatService.adminAddGroupOrDevice(addDeviceParam, operatorVO);
    }

    @PostMapping("/group/showAllGroupId")
    @ApiOperation(value = "获取所有groupID，按学校分类", notes = "获取所有groupID", response = ResultVO.class)
    public ResultVO showAllGroupId() {
        return ResultVO.ok().putData("data", weChatService.showBySchool());
    }

    @PostMapping("/group/addDeviceToWx")
    @ApiOperation(value = "向微信平台添加设备", notes = "向微信平台添加设备", response = ResultVO.class)
    public ResultVO addDeviceToWx(@RequestBody AddDeviceToWxParam addDeviceToWxParam) {
        AddDeviceParam addDeviceParam = new AddDeviceParam();
        BeanUtil.copyProperties(addDeviceToWxParam, addDeviceParam);
        return ResultVO.ok().putData("data", weChatService.addDeviceToWx(addDeviceParam));
    }

    @PostMapping("/group/createToWx")
    @ApiOperation(value = "向微信平台添加设备组", notes = "向微信平台添加设备组", response = ResultVO.class)
    public ResultVO createToWx(@RequestBody CreateGroupParam createGroupParam) {
        SchoolEntity schoolEntity = schoolMapperImpl.getSchoolNameById(createGroupParam.getSchoolId());
        return ResultVO.ok().putData("data",weChatService.createGroupToWx(createGroupParam,null, schoolEntity));
    }
    @PostMapping("/group/delete")
    @ApiOperation(value = "删除微信平台设备", notes = "删除微信平台设备", response = ResultVO.class)
    public ResultVO delete(@RequestBody AddDeviceToWxParam addDeviceToWxParam) {
        DeleteDeviceParam deleteDeviceParam = new DeleteDeviceParam();
        BeanUtil.copyProperties(addDeviceToWxParam, deleteDeviceParam);
        weChatService.deleteDeviceToWx(deleteDeviceParam);
        return ResultVO.ok();
    }

    @GetMapping("/getSnTicket")
    @ApiOperation(value = "获取票据", notes = "获取票据", response = ResultVO.class)
    public ResultVO getSnTicket(String sn,String modelId,@RequestHeader(required = false) String appid) {
        return ResultVO.ok().putData("ticket",weChatService.getSnTicket(sn,modelId,appid));
    }

    @PostMapping("/addWMPFDevice")
    @ApiOperation(value = "运行小程序硬件框架 (WMPF) 的设备需要先通过本接口注册设备 ID ", notes = "运行小程序硬件框架 (WMPF) 的设备需要先通过本接口注册设备 ID  ", response = ResultVO.class)
    public ResultVO addWMPFDevice(@RequestBody AddWMPFDeviceParam addWMPFDeviceParam) {
        return ResultVO.ok(weChatService.addWMPFDevice(addWMPFDeviceParam));
    }

    @PostMapping("/createDeviceSignature")
    @ApiOperation(value = "生成设备签名", notes = "生成设备签名", response = ResultVO.class)
    public ResultVO createDeviceSignature(@RequestBody CreateDeviceSignatureParam createDeviceSignatureParam) {
        return ResultVO.ok(weChatService.createDeviceSignature(createDeviceSignatureParam));
    }

    @GetMapping("/loadGroupIdBySn")
    @ApiOperation(value = "根据sn获取groupId", notes = "根据sn获取groupId", response = ResultVO.class)
    public ResultVO loadGroupIdBySn(String sn) {
        return ResultVO.ok(weChatService.loadGroupIdBySn(sn));
    }

    @PostMapping("/delSn")
    @ApiOperation(value = "直接删除SN", notes = "直接删除SN", response = ResultVO.class)
    public ResultVO delSn(String sn) {
        return ResultVO.ok(weChatService.delSn(sn));
    }
}
