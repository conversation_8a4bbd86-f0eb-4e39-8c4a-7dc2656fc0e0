package com.joinus.wx.videophone.controller;

import com.joinus.common.common.BaseController;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.OperatorVO;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.EmptyResult;
import com.joinus.wx.videophone.model.param.*;
import com.joinus.wx.videophone.model.response.WxAddDeviceResponse;
import com.joinus.wx.videophone.service.DoorKeeperService;
import com.joinus.wx.videophone.service.WxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Api(tags = "网关调用")
@Slf4j
@RestController
@RequestMapping("/doorkeeper")
public class DoorKeeperController extends BaseController {

    @Resource
    private DoorKeeperService doorKeeperService;

    @PostMapping("/sn")
    @ApiOperation(value = "设备上传sn，向微信平台添加设备", notes = "设备上传sn，向微信平台添加设备", response = ResultVO.class)
    public ApiResult<EmptyResult> addWxDevice(HttpServletRequest request, @RequestBody AddDeviceParam addDeviceParam) {
        OperatorVO operatorVO = getUserType(request);
        log.info(addDeviceParam.toString());
        WxAddDeviceResponse wxAddDeviceResponse = doorKeeperService.addGroupOrDevice(addDeviceParam, operatorVO);
        if(wxAddDeviceResponse == null){
            return ApiResult.success();
        }
        return ApiResult.failed("绑定失败");
    }
}
