package com.joinus.wx.videophone.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.joinus.common.matcher.PatternMatcher;
import com.joinus.common.matcher.impl.ServletPathMatcher;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.wx.videophone.model.entity.WxUser;
import com.joinus.wx.videophone.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@EnableApolloConfig
public class LoginInterceptor implements HandlerInterceptor {

    protected PatternMatcher pathMatcher = ServletPathMatcher.getInstance();

    @Value("${interceptor.excluded.urls:}")
    public String whiteUriList;
    /**
     * 在请求处理之前调用（Controller）方法之前
     */
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        Map<String, String> headerMap = ServletUtil.getHeaderMap(request);
        String openId = MapUtil.getStr(headerMap, "token");
        String authentication = MapUtil.getStr(headerMap, "authentication");
        //不为空并且值为wx_videophone代表从安卓设备端请求
        if(StrUtil.isNotEmpty(authentication)){
            if("wx_videophone".equals(authentication)){
                return true;
            }
        }

        JSONArray jsonArray = new JSONArray(whiteUriList);
        List<JSONObject> whiteUriList = jsonArray.toList(JSONObject.class);
        if (this.isExclusion(request.getRequestURI(),whiteUriList)) {
            return true;
        }

        WxUser principalUser = UserUtils.getMiniAppUser(openId);
        CommonResponse.ERROR.assertNotNull(principalUser, "用户未登录");
        return true;
    }

    /**
     * 判断请求是否在白名单中
     *
     * @param requestURI   请求的URI地址
     * @param whiteUriList 白名单地址
     * @return true 不拦截
     */
    public boolean isExclusion(String requestURI, List<JSONObject> whiteUriList) {
        if (CollUtil.isNotEmpty(whiteUriList)) {
            for (JSONObject json : whiteUriList) {
                if (this.pathMatcher.matches((String) json.get("uri"), requestURI)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 请求处理之后进行调用，但是在视图被渲染之前（Controller方法调用之后）
     */
    @Override
    public void postHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    /**
     * 整个请求结束之后被调用，也就是在DispatcherServlet渲染了对应的视图之后执行（主要是用于进行资源清理工作）
     */
    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
