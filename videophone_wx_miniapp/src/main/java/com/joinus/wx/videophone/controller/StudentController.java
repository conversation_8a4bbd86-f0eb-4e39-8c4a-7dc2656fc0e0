package com.joinus.wx.videophone.controller;

import com.joinus.common.model.response.ApiResult;
import com.joinus.wx.videophone.model.dto.StudentDTO;
import com.joinus.wx.videophone.model.enums.MiniAppTypeEnum;
import com.joinus.wx.videophone.model.param.StudentParam;
import com.joinus.wx.videophone.model.response.StudentListResponse;
import com.joinus.wx.videophone.service.StudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 学生相关接口
 *
 * <AUTHOR> anpy
 * @create 2023/6/28 14:29
 */
@RestController
@RequestMapping("/student")
@Validated
@Api(value = "/student", tags = "学生相关", description = "学生相关")
public class StudentController {

    @Resource
    private StudentService studentService;

    /**
     * 学生列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "学生列表", notes = "学生列表，返回列表和设备授权结果", response = StudentDTO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "phone", value = "phone", dataType = "String", required = true, example = ""),
    })
    public ApiResult<StudentListResponse> studentList(@Valid StudentParam studentParam, @RequestHeader(value = "token", required = false) String openId, @RequestHeader(value = "miniAppType", required = false) MiniAppTypeEnum miniAppType) {
        List<StudentDTO> studentEntityList = studentService.loadStudentListByPhone(studentParam, openId, miniAppType);
        return ApiResult.success(StudentListResponse.builder().list(studentEntityList).build());
    }

}
