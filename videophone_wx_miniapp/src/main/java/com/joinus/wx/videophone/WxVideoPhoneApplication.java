package com.joinus.wx.videophone;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.joinus.wx.videophone.config.CustomBeanNameGenerator;
import com.joinus.wx.videophone.config.WxMaProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

@MapperScan(basePackages = {"com.joinus.wx.videophone.mapper","com.joinus.common.mapper","com.joinus.dao.mapper"},nameGenerator = CustomBeanNameGenerator.class)
@ComponentScan(basePackages = {"com.joinus.wx.videophone","com.joinus.common"})
@EnableApolloConfig
//@Configuration
@SpringBootApplication
@EnableConfigurationProperties(WxMaProperties.class)
public class WxVideoPhoneApplication {

    public static void main(String[] args) {
        SpringApplication.run(WxVideoPhoneApplication.class, args);
    }

}
