package com.joinus.wx.videophone.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.enums.ApiResultCode;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.PagingResult;
import com.joinus.wx.videophone.model.enums.MessageBoardReadStateEnum;
import com.joinus.wx.videophone.model.param.message.MessageBoardDetailListParam;
import com.joinus.wx.videophone.model.param.message.SendMessageBoardParam;
import com.joinus.wx.videophone.model.response.CabinetMsgResponse;
import com.joinus.wx.videophone.model.response.MessageBoardData;
import com.joinus.wx.videophone.model.response.MessageBoardListResponse;
import com.joinus.wx.videophone.service.MessageBoardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备端接口
 *
 * <AUTHOR> anpy
 * @create 2023/7/3 11:15
 */
@RestController
@RequestMapping("/device")
@Api(value = "/device", tags = "设备端接口")
public class DeviceController {

    @Resource
    private MessageBoardService messageBoardService;

    @GetMapping(value = "/message-board/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "留言列表(分页)", response = ResultVO.class)
    @ResponseBody
    public ApiResult<PagingResult<MessageBoardListResponse>> list(PageParam param, Long schoolId) {
        return ApiResult.success(messageBoardService.pagingList(param, schoolId));
    }

    @GetMapping(value = "/message-board/detail/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "留言详情列表")
    @ResponseBody
    public ApiResult<PagingResult<MessageBoardData>> detailList(PageParam param, MessageBoardDetailListParam messageBoardDetailListParam) {
        PagingResult<MessageBoardData> pagingResult = messageBoardService.detailList(param, messageBoardDetailListParam);
        return ApiResult.success(pagingResult);
    }

    @PostMapping(value = "/send", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "发送留言")
    @ResponseBody
    public ApiResult<String> sendMessageBoard(@RequestHeader(required = false) Integer userType, @RequestBody SendMessageBoardParam sendMessageBoardParam) {
        if (userType == null) {
            userType = 22;
        }
        ApiResult commonResult = messageBoardService.sendMessageBoard(sendMessageBoardParam, userType);
        if (commonResult.getCode() == ApiResultCode.FAIL.getCode()) {
            return ApiResult.failed(commonResult.getMsg());
        }
        return ApiResult.success();
    }

    @PutMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "更新留言")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "messageBoardId", value = "留言id", dataType = "Long", required = true, example = "1"),
            @ApiImplicitParam(paramType = "query", name = "readState", value = "留言状态", dataType = "String", required = true, example = "READ/REVOCATION"),
    })
    @ResponseBody
    public ApiResult<String> updateMessage(Long messageBoardId, MessageBoardReadStateEnum readState) {
        String result = messageBoardService.updateMessage(messageBoardId, readState);
        if (StrUtil.isNotEmpty(result)) {
            return ApiResult.failed(result);
        }
        return ApiResult.success();
    }

    @GetMapping(value = "/cabinetMsg")
    public ApiResult<List<CabinetMsgResponse>> getCabinetMsg(@RequestParam Long studentId) {
        return ApiResult.success(messageBoardService.getCabinetMsg(studentId));
    }
    @GetMapping(value = "/studentsMsg")
    public ApiResult<PagingResult<MessageBoardListResponse>> getStudentsMsg(PageParam param, Long schoolId) {
        return ApiResult.success(messageBoardService.getStudentsMsg(param, schoolId));
    }

    @GetMapping(value = "/classStudentsMsg")
    public ApiResult<PagingResult<MessageBoardListResponse>> getClassStudentsMsg(PageParam param, Long classId) {
        return ApiResult.success(messageBoardService.getClassStudentsMsg(param, classId));
    }
}
