package com.joinus.wx.videophone.controller;

import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.enums.ApiResultCode;
import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.model.response.PagingResult;
import com.joinus.wx.videophone.model.param.UpdateMessageBoardParam;
import com.joinus.wx.videophone.model.param.message.MessageBoardDetailListParam;
import com.joinus.wx.videophone.model.param.message.SendMessageBoardParam;
import com.joinus.wx.videophone.model.response.MessageBoardData;
import com.joinus.wx.videophone.model.response.MessageBoardHomeResponse;
import com.joinus.wx.videophone.service.MessageBoardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 留言板相关接口
 *
 * <AUTHOR> anpy
 * @create 2023/6/30 14:15
 */
@Slf4j
@RestController
@RequestMapping("/message-board")
@Api(value = "/message-board", tags = "留言板相关接口")
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "header", name = "token", value = "token", dataType = "String", required = true, example = "xxx"),
})
public class MessageBoardController {

    @Resource
    private MessageBoardService messageBoardService;

    @GetMapping(value = "/detail/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "留言详情列表", response = ResultVO.class)
    @ResponseBody
    public ApiResult<PagingResult<MessageBoardData>> detailList(PageParam param, MessageBoardDetailListParam messageBoardDetailListParam) {
        PagingResult<MessageBoardData> pagingResult = messageBoardService.detailList(param, messageBoardDetailListParam);
        return ApiResult.success(pagingResult);
    }

    @PostMapping(value = "/send", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "发送留言")
    @ResponseBody
    public ApiResult<String> sendMessageBoard(@RequestHeader(required = false) Integer userType, @RequestBody SendMessageBoardParam sendMessageBoardParam) {
        if (userType == null) {
            userType = 22;
        }
        ApiResult commonResult = messageBoardService.sendMessageBoard(sendMessageBoardParam, userType);
        if (commonResult.getCode() == ApiResultCode.FAIL.getCode()) {
            return ApiResult.failed(commonResult.getMsg());
        }
        return ApiResult.success();
    }

    @PutMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "更新留言")
    @ResponseBody
    public ApiResult<String> updateMessage(@RequestBody UpdateMessageBoardParam updateMessageBoardParam) {
        String result = messageBoardService.updateMessage(updateMessageBoardParam.getMessageBoardId(), updateMessageBoardParam.getReadState());
        if (StrUtil.isNotEmpty(result)) {
            return ApiResult.failed(result);
        }
        return ApiResult.success();
    }


    @GetMapping(value = "/home/<USER>", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "家长端首页留言列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "parentId", value = "家长id", dataType = "Long", required = true, example = "1"),
    })
    @ResponseBody
    public ApiResult<List<MessageBoardHomeResponse>> messageBoardListByParent(@RequestHeader(required = false) Integer userType, Long parentId) {
        CommonResponse.ERROR.assertNotNull(parentId, "家长id不能为空");
        List<MessageBoardHomeResponse> messageBoardHomeResponses = messageBoardService.messageBoardListByParent(parentId, userType);
        return ApiResult.success(messageBoardHomeResponses);
    }

}
