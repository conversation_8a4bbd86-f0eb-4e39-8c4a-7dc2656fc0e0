package com.joinus.wx.videophone.controller;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;
import com.joinus.common.common.BaseController;
import com.joinus.common.common.ResultVO;
import com.joinus.wx.videophone.model.param.PhoneLoginParam;
import com.joinus.wx.videophone.model.param.WxDecipherRequest;
import com.joinus.wx.videophone.model.param.WxFreeLoginParam;
import com.joinus.wx.videophone.model.param.WxUserRequest;
import com.joinus.wx.videophone.model.response.LoginResponse;
import com.joinus.wx.videophone.model.response.WxDecipherResponse;
import com.joinus.wx.videophone.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "用户相关接口")
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {

    @Resource
    private UserService userService;

    @ApiOperation(value = "获取openId")
    @PostMapping("/login/loadOpenId")
    public ResultVO loadOpenId(String code, @RequestHeader(required = false) String appid) {
        log.info("loadOpenId. appid:{}", appid);
        WxMaJscode2SessionResult wxMaJscode2SessionResult = userService.loadOpenId(code, appid);
        if (wxMaJscode2SessionResult.getOpenid() != null) {
            return ResultVO.ok().putData("openId", wxMaJscode2SessionResult.getOpenid()).putData("unionId", wxMaJscode2SessionResult.getUnionid());
        }
        return ResultVO.error("未获取到openId");
    }

    @ApiOperation(value = "小程序免密登录")
    @PostMapping("/login/freeLogin")
    public ResultVO freeLogin(@ApiParam(value = "小程序类型，固定22") @RequestHeader(required = false) Integer userType,
                              @RequestBody WxFreeLoginParam loginParam) {
        LoginResponse sessionResult = userService.freeLogin(userType, loginParam);
        return ResultVO.ok().putData("data", sessionResult);
    }

    @ApiOperation(value = "小程序用户微信登录")
    @PostMapping("/login/wx")
    public ResultVO loginByWx(@ApiParam(value = "小程序类型，家音视讯22，小安通讯23") @RequestHeader(required = false) Integer userType, @RequestHeader(required = false) String appid,
                              @RequestBody WxUserRequest wxUserRequest) {
        log.info("小程序用户微信登录. appid:{}", appid);
        LoginResponse sessionResult = userService.loginByWx(userType, wxUserRequest, appid);
        return ResultVO.ok().putData("data", sessionResult);
    }

    @ApiOperation(value = "小程序用户手机号登录")
    @PostMapping("/login/phone")
    public ResultVO loginByPhone(@RequestHeader(required = false) Integer userType, @RequestHeader(required = false) String appid,
                                 @RequestBody PhoneLoginParam phoneLoginParam) {
        log.info("微信可视电话小程序用户手机号登录. 手机号码:{}", phoneLoginParam.getPhone());
        LoginResponse sessionResult = userService.loginByPhone(userType, phoneLoginParam, appid);
        return ResultVO.ok().putData("data", sessionResult);
    }

    @ApiOperation(value = "小程序用户信息解密")
    @PostMapping("/decipher")
    public ResultVO decipherUserInfo(@RequestHeader(required = false) String appid,
                                     @RequestBody WxDecipherRequest wxDecipherRequest) {
        log.info("小程序用户信息解密. appid:{}", appid);
        WxDecipherResponse response = userService.decipherUserInfo(appid, wxDecipherRequest);
        return ResultVO.ok().putData("data", response);
    }

    @ApiOperation(value = "根据手机号码获取openid")
    @GetMapping("/loadOpenIdByPhone")
    public ResultVO loadOpenIdByPhone(@RequestHeader(required = false) Integer userType, String phone) {
        log.info("根据手机号码获取openid phone:{},userType:{}", phone, userType);
        if (userType == null) {
            userType = 22;
        }
        String openId = userService.loadOpenIdByPhone(phone, userType);
        return ResultVO.ok().putData("openId", openId);
    }
}
