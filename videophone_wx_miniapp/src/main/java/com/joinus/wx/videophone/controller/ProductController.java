package com.joinus.wx.videophone.controller;

import cn.hutool.core.collection.CollUtil;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.enmus.PaySourceEnum;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.wx.videophone.mapper.impl.BusinessSelfMapperImpl;
import com.joinus.wx.videophone.mapper.impl.YktSchoolMapperImpl;
import com.joinus.wx.videophone.model.response.ParentSignStatusResult;
import com.joinus.wx.videophone.service.BusinessSelfService;
import com.joinus.wx.videophone.service.ProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Api(tags = "套餐")
@Slf4j
@RestController
@RequestMapping("/product")
@ApiImplicitParams({
        @ApiImplicitParam(paramType = "header", name = "token", value = "token", dataType = "String", required = true, example = "xxx"),
})
public class ProductController {

    @Resource
    private ProductService productService;
    @Resource
    private YktSchoolMapperImpl yktSchoolMapperImpl;
    @Resource
    private BusinessSelfService businessSelfService;
    @Resource
    private BusinessSelfMapperImpl businessSelfMapperImpl;

    /**
     * 获取微信token
     *
     * @return token
     */
    @GetMapping("/loadGivenDuration")
    @ApiOperation(value = "小程序获取套餐分钟数", notes = "小程序获取套餐分钟数", response = ResultVO.class)
    public ResultVO loadGivenDuration(Long schoolId, Long gradeId) {
        List<String> givenDuration = productService.loadGivenDuration(schoolId, gradeId);
        if (CollUtil.isEmpty(givenDuration)) {
            return ResultVO.error("该学校未配置套餐");
        }
        if (CollUtil.isNotEmpty(givenDuration) && givenDuration.size() > 1) {
            CommonResponse.assertError("查询套餐出错，请确认学校套餐配置");
        }
        return ResultVO.ok().putData("givenDuration", givenDuration.get(0));
    }

    @GetMapping("/productPrice")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "schoolId", value = "学校id", required = true, paramType = "query", example = "29660"),
            @ApiImplicitParam(name = "studentId", value = "学生id", required = true, paramType = "query", example = "1"),
            @ApiImplicitParam(name = "parentId", value = "家长id", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "gradeId", value = "年级id", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "phoneNum", value = "家长手机号码", required = true, paramType = "query", example = "185xxxxxx"),
    })
    @ApiOperation(value = "小程序签约弹窗", notes = "小程序签约弹窗", response = ResultVO.class)
    public ResultVO loadProductPrice(Long schoolId, Long studentId, Long parentId, Long gradeId, String phoneNum) {
        CommonResponse.ERROR.assertNotNull(schoolId, "学校ID不能为空");
        CommonResponse.ERROR.assertNotNull(studentId, "学生ID不能为空");
        List<Integer> paySourceList = yktSchoolMapperImpl.loadPaySourceBySchoolId(schoolId);
        if (CollUtil.isNotEmpty(paySourceList)) {
            Integer paySource = paySourceList.get(0);
            if (Objects.equals(paySource, PaySourceEnum.SOURCE_YIKESHI.getPaySource()) || Objects.equals(paySource, PaySourceEnum.SOURCE_YIKESHI_YUN.getPaySource())) {
                //是一卡通学校并且是易科士厂家，判断是否开启自动续费
                List<Integer> signingStatus = businessSelfService.getStudentSigningStatus(studentId, parentId);
                if (CollUtil.isEmpty(signingStatus)) {
                    Long signCount = businessSelfMapperImpl.queryParentSignStatus(phoneNum, studentId, schoolId);
                    if (signCount == 0) {
                        //没有自动续费的套餐则返回套餐价格
                        Integer productMinutePrice = productService.getProductMinutePrice(schoolId, gradeId);
                        return ResultVO.ok()
                                .putData("dialogStatus", true)
                                .putData("price", productMinutePrice);
                    }
                }
            }
        }
        return ResultVO.ok().putData("dialogStatus", false);
    }

    @GetMapping(value = "/student/status", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "学生签约状态", response = ResultVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "phone", value = "家长手机号", dataType = "String", required = true, example = "***********"),
            @ApiImplicitParam(paramType = "query", name = "parentId", value = "家长id", dataType = "Long", required = true, example = "1"),
            @ApiImplicitParam(paramType = "query", name = "studentId", value = "学生id", dataType = "Long", required = true, example = "3837370"),
            @ApiImplicitParam(paramType = "query", name = "schoolId", value = "学校id", dataType = "Long", required = true, example = "29660"),
    })
    @ResponseBody
    public ApiResult<ParentSignStatusResult> studentSignStatus(String phone, Long parentId, Long studentId, Long schoolId) {
        ParentSignStatusResult result = productService.studentSignStatus(phone, parentId, studentId, schoolId);
        return ApiResult.success(result);
    }
}
