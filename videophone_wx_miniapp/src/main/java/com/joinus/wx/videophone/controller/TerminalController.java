package com.joinus.wx.videophone.controller;

import com.joinus.common.common.BaseController;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.OperatorVO;
import com.joinus.common.model.TerminalDTO;
import com.joinus.common.model.UploadAuthResultParam;
import com.joinus.wx.videophone.model.param.DeleteDeviceParam;
import com.joinus.wx.videophone.model.param.TerminalParam;
import com.joinus.wx.videophone.model.param.VerifyTerminalToWxParam;
import com.joinus.wx.videophone.model.response.ShowGroupResponse;
import com.joinus.wx.videophone.service.TerminalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

@RestController
@RequestMapping("/terminal")
@Api(value = "/terminal", tags = "设备相关", description = "设备相关")
public class TerminalController extends BaseController {

    @Resource
    private TerminalService terminalService;

    /**
     * 设备列表
     */
    @GetMapping("/wxTerminalList")
    @ApiOperation(value = "以微信平台为主设备列表", notes = "设备列表，返回列表和设备授权结果", response = TerminalDTO.class)
    public ResultVO wxTerminalList(TerminalParam terminalParam) {
        List<ShowGroupResponse.DeviceListDTO> terminalEntityList = terminalService.wxTerminalList(terminalParam);
        return ResultVO.ok().putData("list", terminalEntityList);
    }

    /**
     * 设备列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "设备列表", notes = "设备列表，返回列表和设备授权结果", response = TerminalDTO.class)
    public ResultVO terminalList(TerminalParam terminalParam) {
        List<TerminalDTO> terminalEntityList = terminalService.terminalListByParam(terminalParam);
        return ResultVO.ok().putData("list", terminalEntityList);
    }

    /**
     * 上传授权结果
     */
    @PostMapping("/uploadAuthResult")
    @ApiOperation(value = "上传授权结果", notes = "保存或更新设备授权结果", response = ResultVO.class)
    public ResultVO uploadAuthResult(@RequestBody UploadAuthResultParam param) {
        terminalService.uploadAuthResult(param);
        return ResultVO.ok("授权成功");
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除设备", notes = "删除设备", response = ResultVO.class)
    public ResultVO delete(HttpServletRequest request, @RequestBody DeleteDeviceParam deleteDeviceParam) {
        OperatorVO operatorVO = getUserType(request);
        terminalService.deleteTerminal(operatorVO, deleteDeviceParam);
        return ResultVO.ok("授权成功");
    }

    @PostMapping("/verify")
    public void verify(HttpServletRequest request, @RequestBody VerifyTerminalToWxParam verifyTerminalToWxParam) {
        OperatorVO operatorVO = getUserType(request);
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                terminalService.verifyTerminalWithWx(verifyTerminalToWxParam, operatorVO);
            }
        }, 5000);
    }
}
