package com.joinus.wx.videophone.service;

import com.joinus.common.model.param.PageParam;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.PagingResult;
import com.joinus.wx.videophone.model.enums.MessageBoardReadStateEnum;
import com.joinus.wx.videophone.model.param.message.MessageBoardDetailListParam;
import com.joinus.wx.videophone.model.param.message.SendMessageBoardParam;
import com.joinus.wx.videophone.model.response.CabinetMsgResponse;
import com.joinus.wx.videophone.model.response.MessageBoardData;
import com.joinus.wx.videophone.model.response.MessageBoardHomeResponse;
import com.joinus.wx.videophone.model.response.MessageBoardListResponse;

import java.util.List;

/**
 * <AUTHOR> anpy
 * @create 2023/7/3 13:42
 */
public interface MessageBoardService {

    /**
     * 留言详情列表
     *
     * @param param
     * @param messageBoardDetailListParam 参数
     *                                    studentId 学生id
     *                                    parentId 家长id
     */
    PagingResult<MessageBoardData> detailList(PageParam param, MessageBoardDetailListParam messageBoardDetailListParam);

    /**
     * 发起留言
     *
     * @param sendMessageBoardParam 参数
     *                              message 文本消息
     *                              mediaUrl 媒体url
     * @param userType
     */
    ApiResult sendMessageBoard(SendMessageBoardParam sendMessageBoardParam, Integer userType);

    /**
     * 更新留言
     *
     * @param messageBoardId 留言id
     * @param readState 消息已读状态
     */
    String updateMessage(Long messageBoardId, MessageBoardReadStateEnum readState);

    /**
     * 设备端分页列表
     * @param param 分页参数
     * @param studentId 学生id
     */
    PagingResult<MessageBoardListResponse> pagingList(PageParam param, Long studentId);

    /**
     * 小程序首页根据家长id获取家长和学生的消息列表
     *
     * @param parentId 家长id
     * @param userType
     * @return
     */
    List<MessageBoardHomeResponse> messageBoardListByParent(Long parentId, Integer userType);

    List<CabinetMsgResponse> getCabinetMsg(Long studentId);

    /**
     * 获取学生留言列表-学校
     * @param param
     * @param schoolId
     * @return
     */
    PagingResult<MessageBoardListResponse> getStudentsMsg(PageParam param, java.lang.Long schoolId);

    /**
     * 获取班级学生留言列表
     * @param param
     * @param classId
     * @return
     */
    PagingResult<MessageBoardListResponse> getClassStudentsMsg(PageParam param, Long classId);
}
