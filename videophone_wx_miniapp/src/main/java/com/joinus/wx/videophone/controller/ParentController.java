package com.joinus.wx.videophone.controller;

import cn.hutool.core.collection.CollUtil;
import com.joinus.common.common.ResultVO;
import com.joinus.common.model.UploadAuthResultParam;
import com.joinus.common.model.enums.ApiResultCode;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.common.model.response.EmptyResult;
import com.joinus.wx.videophone.model.BasicApiResponse;
import com.joinus.wx.videophone.model.dto.ParentDTO;
import com.joinus.wx.videophone.model.enums.ParentSignStatusEnum;
import com.joinus.wx.videophone.model.param.AddParentParam;
import com.joinus.wx.videophone.model.param.DeleteParentParam;
import com.joinus.wx.videophone.model.param.UpdateParentParam;
import com.joinus.wx.videophone.model.response.ParentListResponse;
import com.joinus.wx.videophone.model.response.ParentStatisticsResponse;
import com.joinus.wx.videophone.service.ParentService;
import com.joinus.wx.videophone.service.TerminalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/parent")
@Api(value = "/parent", tags = "家长相关")
public class ParentController {

    @Resource
    private ParentService parentService;
    @Resource
    private TerminalService terminalService;

    @PostMapping("/add")
    @ApiOperation(value = "新增家长", notes = "新增家长", response = ResultVO.class)
    public ResultVO addParent(@RequestBody AddParentParam addParentParam) {
        return parentService.addParent(addParentParam);
    }

    /**
     * 更新家长
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新家长", notes = "更新家长", response = ResultVO.class)
    public ResultVO updateParent(@RequestBody UpdateParentParam updateParentParam) {
        BasicApiResponse response = parentService.updateParent(updateParentParam);
        if (response.getCode() == 200) {
            return ResultVO.ok("家长修改成功");
        }
        return ResultVO.error(response.getMessage());
    }

    /**
     * 更新家长
     */
    @PostMapping("/update/v2")
    @ApiOperation(value = "更新家长", notes = "更新家长", response = ResultVO.class)
    public ApiResult<EmptyResult> updateParentV2(@RequestBody UpdateParentParam updateParentParam) {
        BasicApiResponse response = parentService.updateParent(updateParentParam);
        if (response.getCode() == ApiResultCode.SUCCESS.getCode()) {
            return ApiResult.success();
        }
        return ApiResult.failed(response.getMsg());
    }

    /**
     * 家长列表
     */
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "schoolId", value = "学校id", dataType = "Long", required = true, example = "1"),
            @ApiImplicitParam(paramType = "query", name = "studentId", value = "学生id", dataType = "Long", required = true, example = "1"),
    })
    @ApiOperation(value = "家长列表(三期修改)", notes = "家长列表")
    public ApiResult<ParentListResponse> parentList(Long schoolId, Long studentId) {
        CommonResponse.ERROR.assertNotNull(schoolId, "学校id不能为空");
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        List<ParentDTO> parentList = parentService.parentList(schoolId, studentId);
        return ApiResult.success(ParentListResponse.builder().list(parentList).build());
    }

    /**
     * 家长统计
     */
    @GetMapping("/statistics")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "schoolId", value = "学校id", dataType = "Long", required = true, example = "1"),
            @ApiImplicitParam(paramType = "query", name = "studentId", value = "学生id", dataType = "Long", required = true, example = "1"),
    })
    @ApiOperation(value = "家长统计")
    public ApiResult<ParentStatisticsResponse> statistics(Long schoolId, Long studentId) {
        CommonResponse.ERROR.assertNotNull(schoolId, "学校id不能为空");
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空");
        List<ParentDTO> parentList = parentService.parentList(schoolId, studentId);
        List<ParentDTO> noSignParentList = parentList.stream().filter(parentDTO -> parentDTO.getSignStatus() == ParentSignStatusEnum.NO_SIGN).collect(Collectors.toList());
        return ApiResult.success(
                ParentStatisticsResponse
                        .builder()
                        .parentNum(parentList.size())
                        .noSignNum(CollUtil.isEmpty(noSignParentList) ? 0 : noSignParentList.size())
                        .build());
    }

    @PostMapping("/unbind")
    @ApiOperation(value = "解绑学生", notes = "解绑学生", response = ResultVO.class)
    public ResultVO unbind(@RequestBody UpdateParentParam parentParam) {
        BasicApiResponse response = parentService.unbind(parentParam);
        if (response.getCode() == ApiResultCode.SUCCESS.getCode()) {
            return ResultVO.ok("解除绑定成功");
        }
        return ResultVO.error(response.getMessage());
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除家长", notes = "删除家长", response = ResultVO.class)
    public ApiResult<EmptyResult> delete(@RequestBody DeleteParentParam parentParam) {
        BasicApiResponse response = parentService.delete(parentParam);
        if (response.getCode() == ApiResultCode.SUCCESS.getCode()) {
            return ApiResult.success();
        }

        return ApiResult.failed(response.getMsg());
    }
}
