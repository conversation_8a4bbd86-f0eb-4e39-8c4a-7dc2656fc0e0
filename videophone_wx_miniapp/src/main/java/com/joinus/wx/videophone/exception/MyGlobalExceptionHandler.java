package com.joinus.wx.videophone.exception;

import com.joinus.common.common.ResultVO;
import com.joinus.common.exception.BaseException;
import com.joinus.common.exception.IResponseException;
import com.joinus.common.model.response.ResponseMessage;
import com.joinus.common.model.response.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

@RestControllerAdvice
public class MyGlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(MyGlobalExceptionHandler.class);

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(value = BaseException.class)
    public ResultVO bizExceptionHandler(BaseException e) {
        IResponseException responseException = e.getResponseException();
        log.error(e.getMessage(), e);
        return ResultVO.error(responseException.getCode(),responseException.getMsg());
//        return ResultVO.error("服务器异常,请联系管理员");
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(value = Exception.class)
    public ResultVO exceptionHandler(Exception e) {
        if (e instanceof BindException) {
            e.printStackTrace();//将异常打印出来
            BindException ex = (BindException) e;
            List<ObjectError> allErrors = ex.getAllErrors();
            ObjectError objectError = allErrors.get(0);
            String ms = objectError.getDefaultMessage();
            return ResultVO.error(ms);
        }
        log.error(e.getMessage(), e);
        return ResultVO.error("服务器异常,请联系管理员");

    }
}
