<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.WxUserMapper">
    <select id="listSubscribeUsers"
            resultType="com.joinus.io.executor.ijxadmin.student.model.WxSubscribeDbResult">
        select t.id "studentId",
        twu.user_type "userType"
        from t_student t
        inner join t_student_parent tsp on tsp.student_id = t.id
        inner join t_parent tp on tp.id = tsp.parent_id and tp.isactive = 1
        inner join t_wx_user twu on twu.phone = tp.tel_num
        where twu.user_type in (2,8,11,13)
        and t.isactive in
        <foreach collection="activeList" open="(" close=")" separator="," item="active">
            #{active}
        </foreach>
        and t.school_id in
        <foreach collection="schoolIdList" open="(" close=")" separator="," item="schoolId">
            #{schoolId}
        </foreach>
    </select>
</mapper>
