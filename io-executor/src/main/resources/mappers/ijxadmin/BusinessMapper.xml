<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.BusinessMapper">

    <!--查询所有非移动业务-->
    <select id="getBizForUngo" resultType="com.joinus.io.executor.ijxadmin.student.model.BizForUngoDbResult" >
        select
                case when t.status = 2 then '试用开通' else p.pname end || '(' || to_char(b.START_TIME,'yyyy-mm-dd')|| '--' || to_char(b.END_TIME,'yyyy-mm-dd') || ')' "productFee",
                b.ID "id",
                b.ungo_id as "ungoId",
                to_char(b.start_time,'yyyy-mm-dd') as "startTime",
                to_char(b.end_time,'yyyy-mm-dd') as "endTime",
                to_char(t.status) as "status",
                p.pname as "productName",
                t.student_id as "studentId",
                p.fee "fee"
        from t_business t
                 left join T_BUSINESS_UNGO b on b.UUID = t.UUID
                 left join t_product p on p.id = b.mobile_id
        where t.STUDENT_ID = #{studentId} and t.TELNUM = #{telNum} and b.ISACTIVE = 1 and t.STATUS in (2,4)
        union all
        select
                p.PRODUCT_NAME || '(' || to_char(t.START_TIME,'yyyy-mm-dd')|| '--' || to_char(t.END_TIME,'yyyy-mm-dd') || ')' "productFee",
                t.ID "id",
                t.product_id as "ungoId",
                to_char(t.start_time,'yyyy-mm-dd') as "startTime",
                to_char(t.end_time,'yyyy-mm-dd') as "endTime",
                '' as status,
                p.product_name as "productName",
                t.student_id as "studentId",
                to_char(tpss.CURRENT_FEE / 100, 'FM9990.00') "fee"
        from t_business_self t
                 left join t_product_self p on t.PRODUCT_ID = p.ID
                 left join t_product_self_sub tpss on t.SUB_PRODUCT_ID = tpss.id
        where t.BUSINESS_TYPE = 0 and t.STUDENT_ID = #{studentId} and t.PHONE_NUM = #{telNum}
    </select>
    <select id="listBusinessAndUngo"
            resultType="com.joinus.io.executor.ijxadmin.student.model.BusinessDbResult">
        select st.id || bu.telnum "stuIdAndMobile",
        bu.product_code,
        bu.status,
        bu.product_code  || ',' || bu.status "productCodeAndStatus",
        tbu.mobile_code "ungoMobileCode",
        tbu.start_time,
        bu.STATUS_TIME "statusTime",
        tbu.end_time
        from t_business bu
        left join t_business_ungo tbu on tbu.uuid = bu.uuid
        left join t_student st on st.id = bu.student_id and st.isactive in
        <foreach collection="activeList" open="(" close=")" separator="," item="isactive">
            #{isactive}
        </foreach>
        where st.school_id in
        <foreach collection="schoolIdList" open="(" close=")" separator="," item="schoolId">
            #{schoolId}
        </foreach>
    </select>
</mapper>
