<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.SwipingCardMapper">

    <select id="getSwipingCardTodayInfoCount" resultType="long">
        select count(1)
        from (
        Select "ID","SCHOOLID","TERMINALTYPE","TERMINALNUM","CARDNUM","CARDTIME","DIRECTION","SAVETIME",SIGN_TYPE "SIGN_TYPE", null "health_flag" From t_Card_Signin_Record3
        Union All
        Select "ID","SCHOOL_ID" AS "SCHOOLID","TERMINAL_TYPE" AS "TERMINALTYPE","TERMINAL_NUM" AS "TERMINALNUM","CARD_NUM" AS "CARDNUM",
        "CARD_TIME" AS "CARDTIME","DIRECTION","SAVE_TIME" AS "SAVETIME", "SIGN_TYPE", health_flag as "health_flag" From t_Card_Signin_Record6
        ) a
        left join t_school b on b.id = a.schoolid
        left join t_student c on c.CARDCODE = a.CARDNUM and c.isactive=1
        left join t_class e on e.id = c.class_id
        left join sys_dict f on f.dict_name = a.terminaltype and  dict_id = 5
        where a.CARDTIME >= to_date(#{dateBeginTime},'yyyy-mm-dd hh24:mi:ss')
        and    <![CDATA[ a.CARDTIME <= to_date(#{dateEndTime},'yyyy-mm-dd hh24:mi:ss') ]]>
        <if test="schoolId != null">
            <![CDATA[and a.SCHOOLID = #{schoolId} ]]>
        </if>
        <if test="cardNum != null and cardNum != ''">
            <![CDATA[and a.CARDNUM = #{cardNum} ]]>
        </if>
        <if test="terminalNum != null and terminalNum != ''">
            <![CDATA[and a.terminalnum = #{terminalNum} ]]>
        </if>
        <if test="signType != null">
            and a.SIGN_TYPE = #{signType}
        </if>
        <if test="direction != null">
            and a.DIRECTION = #{direction}
        </if>
    </select>

    <select id="getSwipingCardTodayInfo" resultType="com.joinus.io.executor.ijxadmin.swiping.model.SwipingCardResult">
        select a.id, f.dict_desc terminaltype,a.terminalnum,a.cardnum,to_char(a.CARDTIME, 'yyyy-mm-dd hh24:mi:ss') CARDTIME,a.schoolid,a.THERMOMETER,t.TERMINAL_NAME,
        a.direction,to_char(a.savetime, 'yyyy-mm-dd hh24:mi:ss') SAVETIME,b.school_name,c.student_name,c.isdorm,b.iswhite,e.class_name,a.IMAGE_URL,fr.REASON,
        a.ADDRESS,a.SIGN_TYPE,a.SUB_TERMINAL_NUM, a.health_flag "healthFlag", a.health_info "healthInfo"
        from (
        Select "ID","SCHOOLID","TERMINALTYPE","TERMINALNUM","CARDNUM","CARDTIME","DIRECTION","SAVETIME",null
        "THERMOMETER",null IMAGE_URL,null "REASON_CODE",null "ADDRESS",SIGN_TYPE "SIGN_TYPE",null "SUB_TERMINAL_NUM",
        null health_flag, null health_info From t_Card_Signin_Record3
        Union All
        Select "ID","SCHOOL_ID" AS "SCHOOLID","TERMINAL_TYPE" AS "TERMINALTYPE","TERMINAL_NUM" AS
        "TERMINALNUM","CARD_NUM" AS "CARDNUM",
        "CARD_TIME" AS "CARDTIME","DIRECTION","SAVE_TIME" AS "SAVETIME",THERMOMETER AS "THERMOMETER", IMAGE_URL,
        REASON_CODE, ADDRESS, SIGN_TYPE, SUB_TERMINAL_NUM,
        health_flag, health_info
        From t_Card_Signin_Record6
        ) a
        left join t_school b on b.id = a.schoolid
        left join t_student c on c.CARDCODE = a.CARDNUM and c.isactive=1
        left join t_class e on e.id = c.class_id
        left join sys_dict f on f.dict_name = a.terminaltype and dict_id = 5
        left join t_terminal t on a.TERMINALNUM = t.TERMINAL_NUM
        left join t_signin_forbidden_reason fr on a.REASON_CODE = fr.CODE
        where a.CARDTIME >= to_date(#{param.dateBeginTime},'yyyy-mm-dd hh24:mi:ss')
        and    <![CDATA[ a.CARDTIME <= to_date(#{param.dateEndTime},'yyyy-mm-dd hh24:mi:ss') ]]>
        <if test="param.schoolId != null ">
            <![CDATA[and a.SCHOOLID = #{param.schoolId} ]]>
        </if>
        <if test="param.cardNum != null and param.cardNum != ''">
            <![CDATA[and a.CARDNUM = #{param.cardNum} ]]>
        </if>
        <if test="param.terminalNum != null and param.terminalNum != ''">
            <![CDATA[and a.terminalnum = #{param.terminalNum} ]]>
        </if>
        <if test="param.signType != null">
            and a.SIGN_TYPE = #{param.signType}
        </if>
        <if test="param.direction != null">
            and a.DIRECTION = #{param.direction}
        </if>
        order by a.CARDTIME desc
    </select>

    <select id="getSwipingCardHistoryInfoCount" resultType="Long">
        <![CDATA[
		select
        	count(1)
        from ${tableName} a
        left join t_school b on b.id = a.schoolid
	    left join t_student c on  c.CARDCODE = a.cardnum  and c.isactive = 1
		left join t_class e on e.id = c.class_id
	    left join sys_dict f on f.dict_name = a.terminaltype and  dict_id = 5
		]]>
        where a.CARDTIME >= to_date(#{dateBeginTime},'yyyy-mm-dd hh24:mi:ss')
        and    <![CDATA[ a.CARDTIME <= to_date(#{dateEndTime},'yyyy-mm-dd hh24:mi:ss') ]]>
        <if test="schoolId != null ">
            <![CDATA[and a.SCHOOLID = #{schoolId} ]]>
        </if>
        <if test="cardNum != null and cardNum != ''">
            <![CDATA[and a.CARDNUM = #{cardNum} ]]>
        </if>
        <if test="terminalNum != null and terminalNum != ''">
            <![CDATA[and a.terminalnum = #{terminalNum} ]]>
        </if>
        <if test="signType != null">
            and a.SIGN_TYPE = #{signType}
        </if>
        <if test="direction != null">
            and a.DIRECTION = #{direction}
        </if>
    </select>

    <select id="getSwipingCardHistoryInfo" resultType="com.joinus.io.executor.ijxadmin.swiping.model.SwipingCardResult">
        <![CDATA[
		select
        	a.id, f.dict_desc terminaltype,a.terminalnum,a.cardnum,to_char(a.CARDTIME, 'yyyy-mm-dd hh24:mi:ss') CARDTIME,a.schoolid,t.TERMINAL_NAME,
        a.direction,to_char(a.savetime, 'yyyy-mm-dd hh24:mi:ss') SAVETIME,b.school_name,c.student_name,c.isdorm,b.iswhite,e.class_name,a.THERMOMETER,
        a.IMAGE_URL,fr.REASON,a.ADDRESS,a.SIGN_TYPE,a.SUB_TERMINAL_NUM, a.health_flag "healthFlag", a.health_info "healthInfo"
        from ${param.tableName} a
        left join t_school b on b.id = a.schoolid
	    left join t_student c on  c.CARDCODE = a.cardnum and c.isactive = 1
		left join t_class e on e.id = c.class_id
	    left join sys_dict f on f.dict_name = a.terminaltype and  dict_id = 5
	    left join t_terminal t on a.terminalnum = t.terminal_num
		left join t_signin_forbidden_reason fr on a.REASON_CODE = fr.CODE
		]]>
        where a.CARDTIME >= to_date(#{param.dateBeginTime},'yyyy-mm-dd hh24:mi:ss')
        and    <![CDATA[ a.CARDTIME <= to_date(#{param.dateEndTime},'yyyy-mm-dd hh24:mi:ss') ]]>
        <if test="param.schoolId != null">
            <![CDATA[and a.SCHOOLID = #{param.schoolId} ]]>
        </if>
        <if test="param.cardNum != null and param.cardNum != ''">
            <![CDATA[and a.CARDNUM = #{param.cardNum} ]]>
        </if>
        <if test="param.terminalNum != null and param.terminalNum != ''">
            <![CDATA[and a.terminalnum = #{param.terminalNum} ]]>
        </if>
        <if test="param.signType != null">
            and a.SIGN_TYPE = #{param.signType}
        </if>
        <if test="param.direction != null">
            and a.DIRECTION = #{param.direction}
        </if>
        order by a.CARDTIME desc
    </select>

</mapper>