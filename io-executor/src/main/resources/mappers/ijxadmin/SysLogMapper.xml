<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.SysLogMapper">

    <insert id="saveSysLog">
        insert into sys_log (id,
                             content,
                             log_desc,
                             isactive,
                             operator,
                             log_time,
                             log_type,
                             school_id,
                             school_name,
                             ip_address,
                             operator_id,
                             operator_type,
                             user_id,
                             group_name,
                             region_name,
                             source)
        values (seq_sys_log.nextval,
                #{content},
                #{logDesc},
                #{isactive},
                #{operator},
                #{logTime},
                #{logType},
                #{schoolId},
                #{schoolName},
                #{ipAddress},
                #{operatorId},
                #{operatorType},
                #{userId},
                #{groupName},
                #{regionName},
                #{source})
    </insert>
</mapper>
