<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.ProductMapper">

    <select id="getProductList" resultType="com.joinus.io.executor.model.entity.Product">
        select
            t.PNAME "productName",
            t.ID "productId",
            t.code "code",
            t.ID || t.code "uid",
            t.PRODUCT_DESC "productDesc"
        from t_product t where t.ISACTIVE = 1 and t.ISSPECIAL = 0
        union all
        select
            t1.PRODUCT_NAME "productName",
            t1.ID "productId",
            '0' "code",
            t1.ID || 'SELF' "uid",
            t1.PRODUCT_DESC "productDesc"
        from t_product_self t1 where t1.STATE = 1 and t1.PRODUCT_TYPE = 0
    </select>
    <select id="listProductAndProductSelf"
            resultType="com.joinus.io.executor.ijxadmin.student.model.ProductDbResult">
        select
            t.pname as "productName",
            t.id as "productId",
            t.code as "productCode",
            t.id || t.code as "uid",
            t.product_desc as  "productDesc"
        from t_product t where t.isactive = 1 and t.isspecial = 0
    </select>
</mapper>
