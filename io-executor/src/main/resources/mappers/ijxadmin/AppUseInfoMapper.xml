<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.AppUseInfoMapper">
    <select id="getQylUserInfoCount" resultType="java.lang.Long" >
            SELECT
             count(1)
            FROM
            (
            SELECT
            al.id id,
            rn.super_region_name region,
            rn.region_name regionName,
            js.school_name schoolName,
            decode( al.user_type, 0, '教师', 1, '家长' ) userType,
            decode( al.user_type, 0, ( tt.teacher_name ), 1, ( tp.parent_name ) ) userName,
            al.USER_ID userId,
            decode( al.user_type, 1, ( tsp.student_id ) ) studentId,
            al.tel_num telNum,
            to_char( al.login_time, 'yyyy-mm-dd hh24:mi:ss' ) loginTime,
            decode( al.first_login, 1, '是', 0, '否' ) firstLogin,
            decode( al.platform_type, '0', 'android平台', '1', 'ios平台' ) platformType,
            al.version version,
            decode( al.user_type, 0, al.school_id, 1, ( SELECT t.school_id FROM t_student t WHERE t.id = tsp.student_id ) ) schoolId,
            rn.region_id regionId,
            rn.super_region_id superRegionId,
            decode( al.login_type, 0, '密码登录', 1, '验证码登录' , 2, '微信登录' , 10 , '切换登录' ) loginType
            FROM
            t_qyl_app_login al
            LEFT JOIN T_TEACHER tt ON tt.id = al.user_id
            LEFT JOIN T_PARENT tp ON tp.id = al.user_id
            LEFT JOIN t_school js ON al.school_id = js.id
            LEFT JOIN sys_region_new rn ON js.new_region_id = rn.region_id
            LEFT JOIN ( SELECT max( sp.student_id ) student_id, sp.parent_id FROM T_STUDENT_PARENT sp GROUP BY sp.parent_id ORDER BY sp.student_id DESC ) tsp ON al.user_id = tsp.parent_id
            where 1=1
                <if test="null != userType">
                    and  <![CDATA[ al.user_type= #{userType} ]]>
                </if>
                <if test="null != firstLoginFlag">
                    and  <![CDATA[ al.first_login= #{firstLoginFlag} ]]>
                </if>
                <if test="null != telNum">
                    and  <![CDATA[ al.tel_num = #{telNum}]]>
                </if>
                <if test="null != version">
                    and  <![CDATA[ al.version = #{version}]]>
                </if>
                <if test="null != loginType">
                    and  <![CDATA[ al.login_type = #{loginType}]]>
                </if>
                <if test="null != startTime">
                    and  <![CDATA[ al.login_time >= to_date(#{startTime},'yyyy-mm-dd')]]>
                </if>
                <if test="null != endTime">
                    and  <![CDATA[ al.login_time <= to_date(#{endTime},'yyyy-mm-dd')+1]]>
                </if>
            ) qyl
            LEFT JOIN T_STUDENT st ON qyl.studentId = st.ID
            LEFT JOIN T_SCHOOL ts ON st.school_id = ts.ID
            LEFT JOIN sys_region_new srn ON ts.new_region_id = srn.region_id
            where 1=1
            <if test="null != schoolId" >
                and  <![CDATA[ qyl.schoolId= #{schoolId}  ]]>
            </if>

            <if test="null != regionId">
                 and
                <![CDATA[  ((qyl.regionId like '${regionId}%' or  qyl.superRegionId like  '${regionId}%')  or
                                        (srn.region_id like '${regionId}%' or srn.super_region_id like '${regionId}%' )) ]]>
            </if>
            ORDER BY
            qyl.id DESC
    </select>

    <select id="getQylUserInfo"  resultType="com.joinus.io.executor.ijxadmin.appuseinfo.model.AppUseInfoExportDbResult" >
        SELECT
        DECODE( studentId, NULL, ( region ), srn.super_region_name ) "region",
        DECODE( studentId, NULL, ( regionName ), srn.region_name ) "regionName",
        DECODE( studentId, NULL, ( schoolName ), ts.school_name ) "schoolName",
        userType "userType",
        userName "userName",
        telNum "telNum",
        loginTime "loginTime",
        firstLogin "firstLogin",
        platformType "platformType",
        version "version",
        '青于蓝' "appType",
        loginType "loginType"
        FROM
        (
        SELECT
        al.id id,
        rn.super_region_name region,
        rn.region_name regionName,
        js.school_name schoolName,
        decode( al.user_type, 0, '教师', 1, '家长' ) userType,
        decode( al.user_type, 0, ( tt.teacher_name ), 1, ( tp.parent_name ) ) userName,
        al.USER_ID userId,
        decode( al.user_type, 1, ( tsp.student_id ) ) studentId,
        al.tel_num telNum,
        to_char( al.login_time, 'yyyy-mm-dd hh24:mi:ss' ) loginTime,
        decode( al.first_login, 1, '是', 0, '否' ) firstLogin,
        decode( al.platform_type, '0', 'android平台', '1', 'ios平台' ) platformType,
        al.version version,
        decode( al.user_type, 0, al.school_id, 1, ( SELECT t.school_id FROM t_student t WHERE t.id = tsp.student_id ) ) schoolId,
        rn.region_id regionId,
        rn.super_region_id superRegionId,
        decode( al.login_type, 0, '密码登录', 1, '验证码登录' , 2, '微信登录' , 10 , '切换登录' ) loginType
        FROM
        t_qyl_app_login al
        LEFT JOIN T_TEACHER tt ON tt.id = al.user_id
        LEFT JOIN T_PARENT tp ON tp.id = al.user_id
        LEFT JOIN t_school js ON al.school_id = js.id
        LEFT JOIN sys_region_new rn ON js.new_region_id = rn.region_id
        LEFT JOIN ( SELECT max( sp.student_id ) student_id, sp.parent_id FROM T_STUDENT_PARENT sp GROUP BY sp.parent_id ORDER BY sp.student_id DESC ) tsp ON al.user_id = tsp.parent_id
        where 1=1
        <if test="null != param.userType">
            and  <![CDATA[ al.user_type= #{param.userType} ]]>
        </if>
        <if test="null != param.firstLoginFlag">
            and  <![CDATA[ al.first_login= #{param.firstLoginFlag} ]]>
        </if>
        <if test="null != param.telNum">
            and  <![CDATA[ al.tel_num = #{param.telNum}]]>
        </if>
        <if test="null != param.version">
            and  <![CDATA[ al.version = #{param.version}]]>
        </if>
        <if test="null != param.loginType">
            and  <![CDATA[ al.login_type = #{param.loginType}]]>
        </if>
        <if test="null != param.startTime">
            and  <![CDATA[ al.login_time >= to_date(#{param.startTime},'yyyy-mm-dd')]]>
        </if>
        <if test="null != param.endTime">
            and  <![CDATA[ al.login_time <= to_date(#{param.endTime},'yyyy-mm-dd')+1]]>
        </if>
        ) qyl
        LEFT JOIN T_STUDENT st ON qyl.studentId = st.ID
        LEFT JOIN T_SCHOOL ts ON st.school_id = ts.ID
        LEFT JOIN sys_region_new srn ON ts.new_region_id = srn.region_id
        where 1=1
        <if test="null != param.schoolId" >
            and  <![CDATA[ qyl.schoolId= #{param.schoolId}  ]]>
        </if>

        <if test="null != param.regionId">
            and
            <![CDATA[  ((qyl.regionId like '${param.regionId}%' or  qyl.superRegionId like'${param.regionId}%')  or
                                        (srn.region_id like'${param.regionId}%' or srn.super_region_id like'${param.regionId}%' )) ]]>
        </if>
        ORDER BY
        qyl.id DESC
    </select>

    <select id="getXdaUserInfoCount" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM
        (
        SELECT
        al.id id,
        rn.super_region_name region,
        rn.region_name regionName,
        js.school_name schoolName,
        decode( al.user_type, 0, '教师', 1, '家长' ) userType,
        decode( al.user_type, 0, ( tt.teacher_name ), 1, ( tp.parent_name ) ) userName,
        al.USER_ID userId,
        decode( al.user_type, 1, ( tsp.student_id ) ) studentId,
        al.tel_num telNum,
        to_char( al.login_time, 'yyyy-mm-dd hh24:mi:ss' ) loginTime,
        decode( al.first_login, 1, '是', 0, '否' ) firstLogin,
        decode( al.platform_type, '0', 'android平台', '1', 'ios平台' ) platformType,
        al.version version,
        decode( al.user_type, 0, al.school_id, 1, ( SELECT t.school_id FROM t_student t WHERE t.id = tsp.student_id ) )
        schoolId,
        rn.region_id regionId,
        rn.super_region_id superRegionId,
        decode( al.login_type, 0, '密码登录', 1, '验证码登录' , 2, '微信登录' , 10 , '切换登录' ) loginType
        FROM
        t_xda_app_login al
        LEFT JOIN T_TEACHER tt ON tt.id = al.user_id
        LEFT JOIN T_PARENT tp ON tp.id = al.user_id
        LEFT JOIN t_school js ON al.school_id = js.id
        LEFT JOIN sys_region_new rn ON js.new_region_id = rn.region_id
        LEFT JOIN ( SELECT max( sp.student_id ) student_id, sp.parent_id FROM T_STUDENT_PARENT sp GROUP BY sp.parent_id
        ORDER BY sp.student_id DESC ) tsp ON al.user_id = tsp.parent_id

        where 1=1

        <if test="null != userType">
            and  <![CDATA[ al.user_type= #{userType} ]]>
        </if>
        <if test="null != firstLoginFlag">
            and  <![CDATA[ al.first_login= #{firstLoginFlag} ]]>
        </if>
        <if test="null != telNum">
            and  <![CDATA[ al.tel_num = #{telNum}]]>
        </if>
        <if test="null != version">
            and  <![CDATA[ al.version = #{version}]]>
        </if>
        <if test="null != loginType">
            and  <![CDATA[ al.login_type = #{loginType}]]>
        </if>
        <if test="null != startTime">
            and  <![CDATA[ al.login_time >= to_date(#{startTime},'yyyy-mm-dd')]]>
        </if>
        <if test="null != endTime">
            and  <![CDATA[ al.login_time <= to_date(#{endTime},'yyyy-mm-dd')+1]]>
        </if>
        ) xda
        LEFT JOIN T_STUDENT st ON xda.studentId = st.ID
        LEFT JOIN T_SCHOOL ts ON st.school_id = ts.ID
        LEFT JOIN sys_region_new srn ON ts.new_region_id = srn.region_id
        where 1=1
        <if test="null != schoolId">
            and  <![CDATA[ qyl.schoolId= #{schoolId}  ]]>
        </if>

        <if test="null != regionId">
            and
            <![CDATA[  ((qyl.regionId like  '${regionId}%' or  qyl.superRegionId like '${regionId}%')  or
                                        (srn.region_id like '${regionId}%' or srn.super_region_id like '${regionId}%' )) ]]>
        </if>
        ORDER BY
        xda.id DESC
    </select>
    <select id="getXdaUserInfo"  resultType="com.joinus.io.executor.ijxadmin.appuseinfo.model.AppUseInfoExportDbResult" >
        SELECT
        DECODE( studentId, NULL, ( region ), srn.super_region_name ) "region",
        DECODE( studentId, NULL, ( regionName ), srn.region_name ) "regionName",
        DECODE( studentId, NULL, ( schoolName ), ts.school_name ) "schoolName",
        userType "userType",
        userName "userName",
        telNum "telNum",
        loginTime "loginTime",
        firstLogin "firstLogin",
        platformType "platformType",
        version "version",
        '讯达安' "appType",
        loginType "loginType"
        FROM
        (
        SELECT
        al.id id,
        rn.super_region_name region,
        rn.region_name regionName,
        js.school_name schoolName,
        decode( al.user_type, 0, '教师', 1, '家长' ) userType,
        decode( al.user_type, 0, ( tt.teacher_name ), 1, ( tp.parent_name ) ) userName,
        al.USER_ID userId,
        decode( al.user_type, 1, ( tsp.student_id ) ) studentId,
        al.tel_num telNum,
        to_char( al.login_time, 'yyyy-mm-dd hh24:mi:ss' ) loginTime,
        decode( al.first_login, 1, '是', 0, '否' ) firstLogin,
        decode( al.platform_type, '0', 'android平台', '1', 'ios平台' ) platformType,
        al.version version,
        decode( al.user_type, 0, al.school_id, 1, ( SELECT t.school_id FROM t_student t WHERE t.id = tsp.student_id ) )
        schoolId,
        rn.region_id regionId,
        rn.super_region_id superRegionId,
        decode( al.login_type, 0, '密码登录', 1, '验证码登录' , 2, '微信登录' , 10 , '切换登录' ) loginType
        FROM
        t_xda_app_login al
        LEFT JOIN T_TEACHER tt ON tt.id = al.user_id
        LEFT JOIN T_PARENT tp ON tp.id = al.user_id
        LEFT JOIN t_school js ON al.school_id = js.id
        LEFT JOIN sys_region_new rn ON js.new_region_id = rn.region_id
        LEFT JOIN ( SELECT max( sp.student_id ) student_id, sp.parent_id FROM T_STUDENT_PARENT sp GROUP BY sp.parent_id
        ORDER BY sp.student_id DESC ) tsp ON al.user_id = tsp.parent_id

        where 1=1

        <if test="null != param.userType">
            and  <![CDATA[ al.user_type= #{param.userType} ]]>
        </if>
        <if test="null != param.firstLoginFlag">
            and  <![CDATA[ al.first_login= #{param.firstLoginFlag} ]]>
        </if>
        <if test="null != param.telNum">
            and  <![CDATA[ al.tel_num = #{param.telNum}]]>
        </if>
        <if test="null != param.version">
            and  <![CDATA[ al.version = #{param.version}]]>
        </if>
        <if test="null != param.loginType">
            and  <![CDATA[ al.login_type = #{param.loginType}]]>
        </if>
        <if test="null != param.startTime">
            and  <![CDATA[ al.login_time >= to_date(#{param.startTime},'yyyy-mm-dd')]]>
        </if>
        <if test="null != param.endTime">
            and  <![CDATA[ al.login_time <= to_date(#{param.endTime},'yyyy-mm-dd')+1]]>
        </if>
        ) xda
        LEFT JOIN T_STUDENT st ON xda.studentId = st.ID
        LEFT JOIN T_SCHOOL ts ON st.school_id = ts.ID
        LEFT JOIN sys_region_new srn ON ts.new_region_id = srn.region_id
        where 1=1
        <if test="null != param.schoolId">
            and  <![CDATA[ qyl.schoolId= #{param.schoolId}  ]]>
        </if>

        <if test="null != param.regionId">
            and
            <![CDATA[  ((qyl.regionId like '${param.regionId}%' or  qyl.superRegionId like'${param.regionId}%')  or
                                        (srn.region_id like'${param.regionId}%' or srn.super_region_id like'${param.regionId}%' )) ]]>
        </if>
        ORDER BY
        xda.id DESC
    </select>
</mapper>