<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.TeacherMapper">

    <resultMap id="BaseResultMap" type="com.joinus.io.executor.model.entity.Teacher">
        <id property="id" column="ID" jdbcType="DECIMAL"/>
        <result property="address" column="ADDRESS" jdbcType="VARCHAR"/>
        <result property="buka" column="BUKA" jdbcType="DECIMAL"/>
        <result property="cardcode" column="CARDCODE" jdbcType="VARCHAR"/>
        <result property="identity" column="IDENTITY" jdbcType="VARCHAR"/>
        <result property="isactive" column="ISACTIVE" jdbcType="DECIMAL"/>
        <result property="loginIp" column="LOGIN_IP" jdbcType="VARCHAR"/>
        <result property="loginTime" column="LOGIN_TIME" jdbcType="TIMESTAMP"/>
        <result property="num" column="NUM" jdbcType="DECIMAL"/>
        <result property="password" column="PASSWORD" jdbcType="VARCHAR"/>
        <result property="sex" column="SEX" jdbcType="DECIMAL"/>
        <result property="subjectids" column="SUBJECTIDS" jdbcType="VARCHAR"/>
        <result property="subjectnames" column="SUBJECTNAMES" jdbcType="VARCHAR"/>
        <result property="teacherName" column="TEACHER_NAME" jdbcType="VARCHAR"/>
        <result property="telNum" column="TEL_NUM" jdbcType="VARCHAR"/>
        <result property="schoolId" column="SCHOOL_ID" jdbcType="DECIMAL"/>
        <result property="departmentId" column="DEPARTMENT_ID" jdbcType="DECIMAL"/>
        <result property="typeId" column="TYPE_ID" jdbcType="DECIMAL"/>
        <result property="nationId" column="NATION_ID" jdbcType="DECIMAL"/>
        <result property="isVirtualAccount" column="IS_VIRTUAL_ACCOUNT" jdbcType="DECIMAL"/>
        <result property="subjectId" column="SUBJECT_ID" jdbcType="DECIMAL"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="cookie" column="COOKIE" jdbcType="VARCHAR"/>
        <result property="confirmcode" column="CONFIRMCODE" jdbcType="VARCHAR"/>
        <result property="confirmtime" column="CONFIRMTIME" jdbcType="TIMESTAMP"/>
        <result property="teacherCode" column="TEACHER_CODE" jdbcType="VARCHAR"/>
        <result property="roleId" column="ROLE_ID" jdbcType="DECIMAL"/>
        <result property="teacherImg" column="TEACHER_IMG" jdbcType="VARCHAR"/>
        <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
        <result property="isEmchat" column="IS_EMCHAT" jdbcType="CHAR"/>
        <result property="isPrincipal" column="IS_PRINCIPAL" jdbcType="CHAR"/>
        <result property="introduction" column="INTRODUCTION" jdbcType="VARCHAR"/>
        <result property="headUploadTime" column="HEAD_UPLOAD_TIME" jdbcType="TIMESTAMP"/>
        <result property="creator" column="CREATOR" jdbcType="VARCHAR"/>
        <result property="isDisplay" column="IS_DISPLAY" jdbcType="CHAR"/>
        <result property="oaPersonId" column="OA_PERSON_ID" jdbcType="VARCHAR"/>
        <result property="oaPostId" column="OA_POST_ID" jdbcType="VARCHAR"/>
        <result property="appLoginSms" column="APP_LOGIN_SMS" jdbcType="DECIMAL"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="wUid" column="W_UID" jdbcType="DECIMAL"/>
        <result property="wxName" column="WX_NAME" jdbcType="VARCHAR"/>
        <result property="openid" column="OPENID" jdbcType="VARCHAR"/>
        <result property="unionId" column="UNION_ID" jdbcType="VARCHAR"/>
        <result property="qqNickName" column="QQ_NICK_NAME" jdbcType="VARCHAR"/>
        <result property="qqOpenid" column="QQ_OPENID" jdbcType="VARCHAR"/>
        <result property="thelead" column="THELEAD" jdbcType="VARCHAR"/>
        <result property="afterlead" column="AFTERLEAD" jdbcType="VARCHAR"/>
        <result property="imei" column="IMEI" jdbcType="VARCHAR"/>
        <result property="appTimes" column="APP_TIMES" jdbcType="DECIMAL"/>
        <result property="viewCreditMall" column="VIEW_CREDIT_MALL" jdbcType="DECIMAL"/>
        <result property="wechatUserId" column="WECHAT_USER_ID" jdbcType="VARCHAR"/>
        <result property="birthday" column="BIRTHDAY" jdbcType="TIMESTAMP"/>
        <result property="region" column="REGION" jdbcType="VARCHAR"/>
        <result property="wxVsOpenid" column="WX_VS_OPENID" jdbcType="VARCHAR"/>
        <result property="reserveIsDefault" column="RESERVE_IS_DEFAULT" jdbcType="DECIMAL"/>
        <result property="leaveIsDefault" column="LEAVE_IS_DEFAULT" jdbcType="DECIMAL"/>
        <result property="wxVsPassword" column="WX_VS_PASSWORD" jdbcType="VARCHAR"/>
        <result property="isJKai" column="IS_J_KAI" jdbcType="DECIMAL"/>
        <result property="deviceUniqueId" column="DEVICE_UNIQUE_ID" jdbcType="VARCHAR"/>
        <result property="birthdayType" column="BIRTHDAY_TYPE" jdbcType="DECIMAL"/>
        <result property="chineseBirthday" column="CHINESE_BIRTHDAY" jdbcType="VARCHAR"/>
        <result property="teacherTitle" column="TEACHER_TITLE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ADDRESS,BUKA,
        CARDCODE,IDENTITY,ISACTIVE,
        LOGIN_IP,LOGIN_TIME,NUM,
        PASSWORD,SEX,SUBJECTIDS,
        SUBJECTNAMES,TEACHER_NAME,TEL_NUM,
        SCHOOL_ID,DEPARTMENT_ID,TYPE_ID,
        NATION_ID,IS_VIRTUAL_ACCOUNT,SUBJECT_ID,
        CREATE_TIME,COOKIE,CONFIRMCODE,
        CONFIRMTIME,TEACHER_CODE,ROLE_ID,
        TEACHER_IMG,EMAIL,IS_EMCHAT,
        IS_PRINCIPAL,INTRODUCTION,HEAD_UPLOAD_TIME,
        CREATOR,IS_DISPLAY,OA_PERSON_ID,
        OA_POST_ID,APP_LOGIN_SMS,UPDATE_TIME,
        W_UID,WX_NAME,OPENID,
        UNION_ID,QQ_NICK_NAME,QQ_OPENID,
        THELEAD,AFTERLEAD,IMEI,
        APP_TIMES,VIEW_CREDIT_MALL,WECHAT_USER_ID,
        BIRTHDAY,REGION,WX_VS_OPENID,
        RESERVE_IS_DEFAULT,LEAVE_IS_DEFAULT,WX_VS_PASSWORD,
        IS_J_KAI,DEVICE_UNIQUE_ID,BIRTHDAY_TYPE,
        CHINESE_BIRTHDAY,TEACHER_TITLE
    </sql>

    <select id="querySchoolRoleManagerList" resultType="com.joinus.io.executor.ijxadmin.school.model.SchoolRoleManagerPO">
        select area.super_region_name "superRegionName",
        area.region_name "regionName",
        s.school_name "schoolName",
        t.teacher_name "teacherName",
        t.tel_num "telNum"
        from t_teacher t
        left join t_role r on t.role_id = r.id
        left join t_school s on t.school_id = s.id
        left join sys_region_new area on area.region_id = s.new_region_id
        where r.role_type = 1
        and s.isactive = 1
        and t.isactive = 1
        <if test="regionId != null">
            and s.new_region_id in
            (select q.region_id
            from sys_region_new q
            where q.isactive = 1
            start with q.region_id = #{regionId}
            connect by prior q.region_id = q.super_region_id)
        </if>
        order by area.super_region_id,area.region_id,s.school_name
    </select>

</mapper>
