<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.CardInventoryMapper">

    <!-- //把周转卡的状态置为未使用状态 -->
    <update id="updateOldCardType">
        update T_CARD_INVENTORY c
        set c.isused = 0
        where c.id = (select r.id
                      from (select *
                            from T_CARD_INVENTORY s
                            where s.cardcode = #{oldCardCode}
                            order by s.ID) r
                      where rownum = 1)
    </update>

    <select id="checkCardExist" resultType="int">
        SELECT COUNT(*)
        FROM T_CARD_INVENTORY A
        WHERE A.CARDCODE = #{cardCode}
          AND (A.SCHOOL_ID = #{schoolId} OR A.SCHOOL_ID = 0 OR
               <PERSON>.SCHOOL_ID IS NULL);
    </select>

</mapper>