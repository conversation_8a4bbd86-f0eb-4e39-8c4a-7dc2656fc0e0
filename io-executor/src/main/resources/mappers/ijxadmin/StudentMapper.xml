<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.StudentMapper">

    <select id="listBaseInfo"
            resultType="com.joinus.io.executor.ijxadmin.student.model.StudentInfoDbResult">

        select t.id,
               t.student_name,
               t.identity,
               t.student_code,
               t.make_card,
               t.cardcode as cardCode,
               t.isdorm,
               nvl(t.isbus, 2) as isBus,
               t.sex,
               t.tel_num,
               t.address,
               t.nation_id,
               t.student_img,
               nvl2(t.face_feature, 1, 0) as faceFeatureFlag,
               nvl2(t.bus_feature, 1, 0) as busFeatureFlag,
               t.call_duration "callDuration",
               t.used_call_duration "usedCallDuration",
               t.isactive,
               tc.id as klassId,
               tc.class_name as klassName,
               tg.id as gradeId,
               tg.grade_name,
               ts.id as schoolId,
               ts.school_name
        from t_student t
                 inner join t_class tc on t.class_id = tc.id
                 inner join t_grade tg on tc.grade_id = tg.id
                 inner join t_school ts on t.school_id = ts.id
        where ts.isactive = 1 and tg.isactive = 1 and tc.isactive = 1
            <choose>
              <when test="null != activeList and activeList.size > 0">
                  and t.isactive in
                  <foreach collection="activeList" open="(" close=")" separator="," item="isactive">
                      #{isactive}
                  </foreach>
              </when>
              <otherwise>
                  and t.isactive = 1
              </otherwise>
            </choose>

            <choose>
                <when test="null != schoolIdList and schoolIdList.size > 0">
                    and t.school_id in
                    <foreach collection="schoolIdList" open="(" close=")" separator="," item="schoolId">
                        #{schoolId}
                    </foreach>
                </when>
                <otherwise>
                    and t.school_id = #{schoolId}
                </otherwise>
            </choose>

            <if test="null != gradeId">
                and tg.id = #{gradeId}
            </if>
            <if test="null != classId">
                and tc.id = #{classId}
            </if>
        order by t.id asc
    </select>

    <select id="listMonthlyBusiness"
            resultType="com.joinus.io.executor.ijxadmin.student.model.MonthlyBusinessDbResult">
        select  t.id as "studentId",
                listagg(tqa.sign_platform_type, ',') within group (order by tqa.sign_platform_type) as "monthlyType"
        from t_student t
            inner join t_qyl_auto_renew_info tqa on t.id = tqa.student_id
        where tqa.sign_state = 1
            and t.isactive in
            <foreach collection="activeList" open="(" close=")" separator="," item="isactive">
                #{isactive}
            </foreach>
            and t.school_id in
            <foreach collection="schoolIdList" open="(" close=")" separator="," item="schoolId">
                #{schoolId}
            </foreach>
        group by  t.id
    </select>
    <select id="getQinPhoneByStudentNameAndSchoolId"
            resultType="String">
        select pa.tel_num
        from T_STUDENT t,T_STUDENT_PARENT sp, T_PARENT pa
        where
           t.student_name =#{studentName}
          and t.school_id=#{schoolId}
          and  sp.student_id = t.ID
          and pa.id = sp.parent_id
          and pa.isactive = 1
          and t.isactive=1
          and pa.TEL_NUM is not null
    </select>

    <select id="getStudentBySchoolIdCount" resultType="java.lang.Long">
        SELECT count(distinct st.id)
        FROM t_student st
        inner join t_school sc on st.school_id = sc.id and sc.id in
        <foreach item="schoolId" collection="param.schoolIdList" open="(" separator="," close=")">
            #{schoolId}
        </foreach>
        where st.isactive in
        <foreach item="isActive" collection="param.isActiveList" open="(" separator="," close=")">
            #{isActive}
        </foreach>
    </select>

    <select id="getStudentBySchoolId"
            resultType="com.joinus.io.executor.ijxadmin.student.model.StudentExtractDbResult">
        select a.* from (
        SELECT sc.school_name "schoolName",gr.grade_name "gradeName",cl.class_name "klassName",st.student_name
        "studentName",st.isdorm "isDorm",
        st.make_card "makeCard",st.cardcode "cardCode",st.sex "sex",
        st.identity "identity",st.id ,st.id "temStudentId",
        case when st.face_feature is not null then 1 else 0 end "faceFeatureFlag", st.student_img "studentImg",
        case when st.bus_feature is not null then 1 else 0 end "busFeatureFlag",
        (select listagg(tqa.sign_platform_type, ',') within group (order by tqa.sign_platform_type)
        from t_qyl_auto_renew_info tqa
        where tqa.student_id = st.id and tqa.sign_state = 1) as "monthlyType",st.student_code "studentCode"
        FROM t_student st
        inner join t_school sc on st.school_id = sc.id
        left join t_class cl on cl.id = st.class_id
        left join t_grade gr on gr.id = cl.grade_id
        where st.isactive in
        <foreach item="isActive" collection="param.isActiveList" open="(" separator="," close=")">
            #{isActive}
        </foreach>
        and sc.id in
        <foreach item="schoolId" collection="param.schoolIdList" open="(" separator="," close=")">
            #{schoolId}
        </foreach>
        order by sc.SCHOOL_NAME,gr.GRADE_NAME,cl.CLASS_NAME,
        lower(GETHZPY.GETHZFULLPY(st.student_name)),st.id nulls LAST
        ) a
    </select>

    <select id="selectStudentImages" resultType="com.joinus.io.executor.ijxadmin.student.model.StudentImgDbResult">
        SELECT DISTINCT t.id, t.student_name, c.class_name AS class_name, g.grade_name, t.student_img, t.class_id,
        t.cardcode, s.school_name, t.MAKE_CARD AS student_code, t.ISDORM,t.sex
        FROM t_student t
        LEFT JOIN t_class c ON c.id = t.class_id AND c.isactive = 1
        LEFT JOIN t_grade g ON g.id = c.grade_id AND g.isactive = 1
        LEFT JOIN t_school s ON s.id = t.school_id AND s.isactive = 1
        LEFT JOIN t_business b ON b.student_id = t.id
        WHERE t.isactive = 1
        AND t.school_id = #{params.schoolId}
        AND t.class_id IN (SELECT id FROM t_class WHERE grade_id = #{params.gradeId})
        <if test="params.classId !=null">
            AND t.class_id = #{params.classId}
        </if>
        <if test="params.openCmccBusiness !=null">
            AND b.status = 1
        </if>
        <if test="params.imgAuditPass !=null">
            AND t.audit_status = 1
        </if>
        <if test="params.studentIdList != null">
            AND t.id IN
            <foreach item="studentId" collection="params.studentIdList" open="(" separator="," close=")">
                #{studentId}
            </foreach>
        </if>
        ORDER BY t.class_id, t.id
    </select>

    <select id="getStudentByParam"
            resultType="com.joinus.io.executor.ijxadmin.student.model.StudentInfoDbResult">
        select a.* from t_student a
        inner join t_class b on b.id = a.class_id
        inner join t_grade c on c.id = b.grade_id
        inner join t_school e on e.id = a.school_id
        where a.isactive = 1
        <if test="null != studentName">
          and  trim(a.student_name)=#{studentName}
        </if>
        <if test="null != className">
          and   trim(b.class_name)=#{className}
        </if>
        <if test="null != studentName">
           and  trim(c.grade_name)=#{gradeName}
        </if>
        <if test="null != schoolId">
           and  trim(e.id)=#{schoolId}
        </if>

    </select>



    <select id="selectStudentsBySchoolIdAndGradeIdAndStudentName"
            resultType="com.joinus.io.executor.model.entity.Student">
        select a.*
        from t_student a
                 inner join t_class b on b.id = a.class_id
                 inner join t_grade c on c.id = b.grade_id
        where a.isactive = 1
          and a.school_id = #{schoolId}
          and c.id = #{gradeId}
          and a.student_name = #{studentName}

    </select>
</mapper>