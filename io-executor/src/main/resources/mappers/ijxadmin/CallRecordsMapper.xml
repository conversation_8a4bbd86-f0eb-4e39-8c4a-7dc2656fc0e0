<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.CallRecordsMapper">

    <select id="getTodayCallRecordsCount" resultType="java.lang.Long">
        select
        count(1)
        from T_QIN_PHONE_DETAIL qp
        left join T_STUDENT ts on qp.STUDENT_ID = TS.ID
        left join T_SCHOOL sc on sc.id = ts.SCHOOL_ID
        left join SYS_REGION_NEW srn on srn.REGION_ID = sc.NEW_REGION_ID
        left join T_TERMINAL tt on tt.TERMINAL_NUM = qp.TERMINAL_NUM
        left join T_CLASS c on c.id = TS.CLASS_ID
        left join T_GRADE tg on TG.ID = c.GRADE_ID
        <where>
            <if test="param.schoolId != null and param.schoolId != ''">
                AND sc.id = #{param.schoolId}
            </if>
            <if test="param.terminalNum != null and param.terminalNum != ''">
                AND tt.TERMINAL_NUM = #{param.terminalNum}
            </if>
            <if test="param.terminalName != null and param.terminalName != ''">
                AND tt.TERMINAL_NAME = #{param.terminalName}
            </if>
            <if test="param.gradeId != null and param.gradeId != ''">
                AND tg.id = #{param.gradeId}
            </if>
            <if test="param.classId != null and param.classId != ''">
                AND c.id = #{param.classId}
            </if>
            <if test="param.studentName != null and param.studentName != ''">
                AND ts.STUDENT_NAME =  #{param.studentName}
            </if>
            <if test="param.studentList != null and param.studentList.size() > 0">
                AND ts.id IN
                <foreach item="studentId" collection="param.studentList" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
            <if test="param.cardCode != null and param.cardCode != ''">
                AND qp.CARDCODE = #{param.cardCode}
            </if>
            <if test="param.phone != null and param.phone != ''">
                AND qp.PHONE = #{param.phone}
            </if>
            <if test="param.regionId != null and param.regionId != ''">
                AND srn.region_id LIKE #{param.regionId} || '%'
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND qp.START_TIME >= to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND qp.START_TIME &lt; to_date(#{param.endTime}, 'yyyy-MM-dd HH24:mi:ss') + 1
            </if>
            <if test="param.chargeType != null and param.chargeType != ''">
                <choose>
                    <when test="param.chargeType == 1">
                        AND (qp.charge_type is null or qp.charge_type = 1)
                    </when>
                    <when test="param.chargeType == 2">
                        AND qp.charge_type = 2
                    </when>
                    <when test="param.chargeType == 3">
                        AND qp.charge_type = 3
                    </when>
                    <when test="param.chargeType == 4">
                        AND charge_type = 4
                    </when>
                </choose>
            </if>
            <if test="param.answerState != null ">
                <choose>
                    <when test="param.answerState == 1">
                        AND qp.PHONE_TYPE = 1
                    </when>
                    <otherwise>
                        AND (qp.PHONE_TYPE is null OR qp.PHONE_TYPE = 0)
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>


    <select id="getCallRecordsCount" resultType="java.lang.Long">
        select count(1) from (
        select
        1
        from (select * from ${param.tableName}
        <where>
            <if test="param.terminalNum != null and param.terminalNum !=''">
                AND TERMINAL_NUM = #{param.terminalNum}
            </if>
            <if test="param.phone != null and param.phone != ''">
                AND PHONE = #{param.phone}
            </if>
            <if test="param.startTime != null  and param.startTime!= '' ">
                AND LOGDATE >= to_date(#{param.startTime}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="param.endTime != null  and param.endTime != '' ">
                AND LOGDATE &lt; to_date(#{param.endTime}, 'yyyy-mm-dd hh24:mi:ss') + 1
            </if>
            <if test="param.answerState != null ">
                <choose>
                    <when test="param.answerState == 1">
                        AND PHONE_TYPE = 1
                    </when>
                    <otherwise>
                        AND (PHONE_TYPE is null OR PHONE_TYPE = 0)
                    </otherwise>
                </choose>
            </if>
            <if test="param.chargeType != null and param.chargeType != ''">
                <choose>
                    <when test="param.chargeType == 1">
                        AND (charge_type is null or charge_type = 1)
                    </when>
                    <when test="param.chargeType == 2">
                        AND charge_type = 2
                    </when>
                    <when test="param.chargeType == 3">
                        AND charge_type = 3
                    </when>
                    <when test="param.chargeType == 4">
                        AND charge_type = 4
                    </when>
                </choose>
            </if>
        </where>
        ) qp
        left join T_STUDENT ts on qp.STUDENT_ID = TS.ID
        left join T_SCHOOL sc on sc.id = ts.SCHOOL_ID
        left join SYS_REGION_NEW srn on srn.REGION_ID = sc.NEW_REGION_ID
        left join T_TERMINAL tt on tt.TERMINAL_NUM = qp.TERMINAL_NUM
        left join T_CLASS c on c.id = TS.CLASS_ID
        left join T_GRADE tg on TG.ID = c.GRADE_ID
        <where>
            <if test="param.schoolId != null  and param.schoolId != ''">
                AND sc.id = #{param.schoolId}
            </if>
            <if test="param.terminalName != null  and param.terminalName != ''">
                AND tt.TERMINAL_NAME = #{param.terminalName}
            </if>
            <if test="param.gradeId != null and param.gradeId!= ''">
                AND tg.id = #{param.gradeId}
            </if>
            <if test="param.classId != null and param.classId!= '' ">
                AND c.id = #{param.classId}
            </if>
            <if test="param.studentName != null and param.studentName != '' ">
                AND ts.STUDENT_NAME =  #{param.studentName}
            </if>
            <if test="param.studentList != null and param.studentList.size() > 0">
                AND ts.id IN
                <foreach item="studentId" collection="param.studentList" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
            <if test="param.cardCode != null  and param.cardCode != ''">
                AND qp.CARDCODE = #{param.cardCode}
            </if>
            <if test="param.regionId != null and param.regionId != ''">
                AND srn.REGION_ID LIKE #{param.regionId} || '%'
            </if>
        </where>
        union all
        select
        1
        from T_QIN_PHONE_DETAIL qp
        left join T_STUDENT ts on qp.STUDENT_ID = TS.ID
        left join T_SCHOOL sc on sc.id = ts.SCHOOL_ID
        left join SYS_REGION_NEW srn on srn.REGION_ID = sc.NEW_REGION_ID
        left join T_TERMINAL tt on tt.TERMINAL_NUM = qp.TERMINAL_NUM
        left join T_CLASS c on c.id = TS.CLASS_ID
        left join T_GRADE tg on TG.ID = c.GRADE_ID
        <where>
            <if test="param.schoolId != null and param.schoolId != ''">
                AND sc.id = #{param.schoolId}
            </if>
            <if test="param.terminalNum != null and param.terminalNum != ''">
                AND tt.TERMINAL_NUM = #{param.terminalNum}
            </if>
            <if test="param.terminalName != null and param.terminalName != ''">
                AND tt.TERMINAL_NAME = #{param.terminalName}
            </if>
            <if test="param.gradeId != null and param.gradeId != ''">
                AND tg.id = #{param.gradeId}
            </if>
            <if test="param.classId != null and param.classId != ''">
                AND c.id = #{param.classId}
            </if>
            <if test="param.studentName != null and param.studentName != ''">
                AND ts.STUDENT_NAME =  #{param.studentName}
            </if>
            <if test="param.studentList != null and param.studentList.size() > 0">
                AND ts.id IN
                <foreach item="studentId" collection="param.studentList" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
            <if test="param.cardCode != null and param.cardCode != ''">
                AND qp.CARDCODE = #{param.cardCode}
            </if>
            <if test="param.phone != null and param.phone != ''">
                AND qp.PHONE = #{param.phone}
            </if>
            <if test="param.regionId != null and param.regionId != ''">
                AND srn.region_id LIKE #{param.regionId} || '%'
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND qp.START_TIME >= to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND qp.START_TIME &lt; to_date(#{param.endTime}, 'yyyy-MM-dd HH24:mi:ss') + 1
            </if>
            <if test="param.chargeType != null and param.chargeType != ''">
                <choose>
                    <when test="param.chargeType == 1">
                        AND (qp.charge_type is null or qp.charge_type = 1)
                    </when>
                    <when test="param.chargeType == 2">
                        AND qp.charge_type = 2
                    </when>
                    <when test="param.chargeType == 3">
                        AND qp.charge_type = 3
                    </when>
                    <when test="param.chargeType == 4">
                        AND charge_type = 4
                    </when>
                </choose>
            </if>
            <if test="param.answerState != null ">
                <choose>
                    <when test="param.answerState == 1">
                        AND qp.PHONE_TYPE = 1
                    </when>
                    <otherwise>
                        AND (qp.PHONE_TYPE is null OR qp.PHONE_TYPE = 0)
                    </otherwise>
                </choose>
            </if>
        </where>
        ) total

    </select>

    <select id="getTodayCallRecords"
            resultType="com.joinus.io.executor.ijxadmin.callrecords.model.CallRecordsExportDbResult">
        select * from (
        select
        nvl(sc.SCHOOL_NAME, tts.school_name) "schoolName",
        tt.TERMINAL_NAME "terminalName",
        qp.TERMINAL_NUM "terminalNum",
        tt.SIM_NUM "simNum",
        tt.INSTALL_ADDRESS "installAddress",
        tg.GRADE_NAME "gradeName",
        c.CLASS_NAME "className",
        ts.STUDENT_NAME "studentName",
        qp.CARDCODE "cardCode",
        qp.START_TIME "startTime",
        qp.PHONE "phone",
        qp.TALK_LENGTH "talkLength",
        nvl(srn.SUPER_REGION_NAME,srn1.SUPER_REGION_NAME) "superRegionName",
        nvl(srn.REGION_NAME,srn1.REGION_NAME) "regionName",
        nvl(qp.PHONE_TYPE,0) "answerState",
        nvl(qp.charge_type,1) "chargeType",
        nvl(qp.call_type,4) "callType",
        nvl(qp.wait_seconds, 0) "waitSeconds",
        nvl(qp.is_login_app,2) "isLoginApp"
        from T_QIN_PHONE_DETAIL qp
        left join T_STUDENT ts on qp.STUDENT_ID = TS.ID
        left join T_SCHOOL sc on sc.id = qp.SCHOOL_ID
        left join SYS_REGION_NEW srn on srn.REGION_ID = sc.NEW_REGION_ID
        left join T_TERMINAL tt on tt.TERMINAL_NUM = qp.TERMINAL_NUM
        left join T_SCHOOL tts on tt.school_id = tts.id and tts.isactive = 1
        left join SYS_REGION_NEW srn1 on srn1.REGION_ID = tts.NEW_REGION_ID
        left join T_CLASS c on c.id = TS.CLASS_ID
        left join T_GRADE tg on TG.ID = c.GRADE_ID
        <where>
            <if test="param.schoolId != null and param.schoolId != ''">
                AND sc.id = #{param.schoolId}
            </if>
            <if test="param.terminalNum != null and param.terminalNum != ''">
                AND tt.TERMINAL_NUM = #{param.terminalNum}
            </if>
            <if test="param.terminalName != null and param.terminalName != ''">
                AND tt.TERMINAL_NAME = #{param.terminalName}
            </if>
            <if test="param.gradeId != null and param.gradeId != ''">
                AND tg.id = #{param.gradeId}
            </if>
            <if test="param.classId != null and param.classId != ''">
                AND c.id = #{param.classId}
            </if>
            <if test="param.studentName != null and param.studentName != ''">
                AND ts.STUDENT_NAME =  #{param.studentName}
            </if>
            <if test="param.studentList != null and param.studentList.size() > 0">
                AND ts.id IN
                <foreach item="studentId" collection="param.studentList" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
            <if test="param.cardCode != null and param.cardCode != ''">
                AND qp.CARDCODE = #{param.cardCode}
            </if>
            <if test="param.phone != null and param.phone != ''">
                AND qp.PHONE = #{param.phone}
            </if>
            <if test="param.regionId != null and param.regionId != ''">
                AND srn.region_id LIKE #{param.regionId} || '%'
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND qp.START_TIME >= to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND qp.START_TIME &lt; to_date(#{param.endTime}, 'yyyy-MM-dd HH24:mi:ss') + 1
            </if>
            <if test="param.answerState != null ">
                <choose>
                    <when test="param.answerState == 1">
                        AND qp.PHONE_TYPE = 1
                    </when>
                    <otherwise>
                        AND (qp.PHONE_TYPE is null OR qp.PHONE_TYPE = 0)
                    </otherwise>
                </choose>
            </if>
            <if test="param.chargeType != null and param.chargeType != ''">
                <choose>
                    <when test="param.chargeType == 1">
                        AND (qp.charge_type is null or qp.charge_type = 1)
                    </when>
                    <when test="param.chargeType == 2">
                        AND qp.charge_type = 2
                    </when>
                    <when test="param.chargeType == 3">
                        AND qp.charge_type = 3
                    </when>
                    <when test="param.chargeType == 4">
                        AND charge_type = 4
                    </when>
                </choose>
            </if>
        </where>
        ) total
        order by total."startTime" desc
    </select>

    <select id="getCallRecords" resultType="com.joinus.io.executor.ijxadmin.callrecords.model.CallRecordsExportDbResult">
        select * from (
        select
        nvl(sc.SCHOOL_NAME, tts.school_name) "schoolName",
        tt.TERMINAL_NAME "terminalName",
        qp.TERMINAL_NUM "terminalNum",
        tt.SIM_NUM "simNum",
        tt.INSTALL_ADDRESS "installAddress",
        tg.GRADE_NAME "gradeName",
        c.CLASS_NAME "className",
        ts.STUDENT_NAME "studentName",
        qp.CARDCODE "cardCode",
        qp.START_TIME "startTime",
        qp.PHONE "phone",
        qp.TALK_LENGTH "talkLength",
        nvl(srn.SUPER_REGION_NAME,srn1.SUPER_REGION_NAME) "superRegionName",
        nvl(srn.REGION_NAME,srn1.REGION_NAME) "regionName",
        nvl(qp.PHONE_TYPE,0)  "answerState",
        nvl(qp.charge_type,1) "chargeType",
        nvl(qp.call_type,4) "callType",
        nvl(qp.wait_seconds, 0) "waitSeconds",
        nvl(qp.is_login_app,2)  "isLoginApp"
        from (select * from ${param.tableName} qb
        <where>
            <if test="param.terminalNum != null and param.terminalNum !=''">
                AND TERMINAL_NUM = #{param.terminalNum}
            </if>
            <if test="param.phone != null and param.phone != ''">
                AND PHONE = #{param.phone}
            </if>
            <if test="param.startTime != null  and param.startTime!= '' ">
                AND LOGDATE >= to_date(#{param.startTime}, 'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="param.endTime != null  and param.endTime != '' ">
                AND LOGDATE &lt; to_date(#{param.endTime}, 'yyyy-mm-dd hh24:mi:ss') + 1
            </if>
            <if test="param.answerState != null ">
                <choose>
                    <when test="param.answerState == 1">
                        AND PHONE_TYPE = 1
                    </when>
                    <otherwise>
                        AND (PHONE_TYPE is null OR PHONE_TYPE = 0)
                    </otherwise>
                </choose>
            </if>
            <if test="param.chargeType != null and param.chargeType != ''">
                <choose>
                    <when test="param.chargeType == 1">
                        AND (charge_type is null or charge_type = 1)
                    </when>
                    <when test="param.chargeType == 2">
                        AND charge_type = 2
                    </when>
                    <when test="param.chargeType == 3">
                        AND charge_type = 3
                    </when>
                    <when test="param.chargeType == 4">
                        AND charge_type = 4
                    </when>
                </choose>
            </if>
        </where>
        ) qp
        left join T_STUDENT ts on qp.STUDENT_ID = TS.ID
        left join T_SCHOOL sc on sc.id = qp.SCHOOL_ID
        left join SYS_REGION_NEW srn on srn.REGION_ID = sc.NEW_REGION_ID
        left join T_TERMINAL tt on tt.TERMINAL_NUM = qp.TERMINAL_NUM
        left join T_SCHOOL tts on tt.school_id = tts.id and tts.isactive = 1
        left join SYS_REGION_NEW srn1 on srn1.REGION_ID = tts.NEW_REGION_ID
        left join T_CLASS c on c.id = TS.CLASS_ID
        left join T_GRADE tg on TG.ID = c.GRADE_ID
        <where>
            <if test="param.schoolId != null  and param.schoolId != ''">
                AND sc.id = #{param.schoolId}
            </if>
            <if test="param.terminalName != null  and param.terminalName != ''">
                AND tt.TERMINAL_NAME = #{param.terminalName}
            </if>
            <if test="param.gradeId != null and param.gradeId!= ''">
                AND tg.id = #{param.gradeId}
            </if>
            <if test="param.classId != null and param.classId!= '' ">
                AND c.id = #{param.classId}
            </if>
            <if test="param.studentName != null and param.studentName != '' ">
                AND ts.STUDENT_NAME =  #{param.studentName}
            </if>
            <if test="param.studentList != null and param.studentList.size() > 0">
                AND ts.id IN
                <foreach item="studentId" collection="param.studentList" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
            <if test="param.cardCode != null  and param.cardCode != ''">
                AND qp.CARDCODE = #{param.cardCode}
            </if>
            <if test="param.regionId != null and param.regionId != ''">
                AND srn.REGION_ID LIKE #{param.regionId} || '%'
            </if>
        </where>
        union
        select
        nvl(sc.SCHOOL_NAME, tts.school_name) "schoolName",
        tt.TERMINAL_NAME "terminalName",
        qp.TERMINAL_NUM "terminalNum",
        tt.SIM_NUM "simNum",
        tt.INSTALL_ADDRESS "installAddress",
        tg.GRADE_NAME "gradeName",
        c.CLASS_NAME "className",
        ts.STUDENT_NAME "studentName",
        qp.CARDCODE "cardCode",
        qp.START_TIME "startTime",
        qp.PHONE "phone",
        qp.TALK_LENGTH  "talkLength",
        nvl(srn.SUPER_REGION_NAME,srn1.SUPER_REGION_NAME) "superRegionName",
        nvl(srn.REGION_NAME,srn1.REGION_NAME) "regionName",
        nvl(qp.PHONE_TYPE,0)  "answerState",
        nvl(qp.charge_type,1) "chargeType",
        nvl(qp.call_type,4) "callType",
        nvl(qp.wait_seconds, 0) "waitSeconds",
        nvl(qp.is_login_app,2)  "isLoginApp"
        from T_QIN_PHONE_DETAIL qp
        left join T_STUDENT ts on qp.STUDENT_ID = TS.ID
        left join T_SCHOOL sc on sc.id = qp.SCHOOL_ID
        left join SYS_REGION_NEW srn on srn.REGION_ID = sc.NEW_REGION_ID
        left join T_TERMINAL tt on tt.TERMINAL_NUM = qp.TERMINAL_NUM
        left join T_SCHOOL tts on tt.school_id = tts.id and tts.isactive = 1
        left join SYS_REGION_NEW srn1 on srn1.REGION_ID = tts.NEW_REGION_ID
        left join T_CLASS c on c.id = TS.CLASS_ID
        left join T_GRADE tg on TG.ID = c.GRADE_ID
        <where>
            <if test="param.schoolId != null and param.schoolId != ''">
                AND sc.id = #{param.schoolId}
            </if>
            <if test="param.terminalNum != null and param.terminalNum != ''">
                AND tt.TERMINAL_NUM = #{param.terminalNum}
            </if>
            <if test="param.terminalName != null and param.terminalName != ''">
                AND tt.TERMINAL_NAME = #{param.terminalName}
            </if>
            <if test="param.gradeId != null and param.gradeId != ''">
                AND tg.id = #{param.gradeId}
            </if>
            <if test="param.classId != null and param.classId != ''">
                AND c.id = #{param.classId}
            </if>
            <if test="param.studentName != null and param.studentName != ''">
                AND ts.STUDENT_NAME =  #{param.studentName}
            </if>
            <if test="param.studentList != null and param.studentList.size() > 0">
                AND ts.id IN
                <foreach item="studentId" collection="param.studentList" open="(" separator="," close=")">
                    #{studentId}
                </foreach>
            </if>
            <if test="param.cardCode != null and param.cardCode != ''">
                AND qp.CARDCODE = #{param.cardCode}
            </if>
            <if test="param.phone != null and param.phone != ''">
                AND qp.PHONE = #{param.phone}
            </if>
            <if test="param.regionId != null and param.regionId != ''">
                AND srn.region_id LIKE #{param.regionId} || '%'
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND qp.START_TIME >= to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND qp.START_TIME &lt; to_date(#{param.endTime}, 'yyyy-MM-dd HH24:mi:ss') + 1
            </if>
            <if test="param.answerState != null ">
                <choose>
                    <when test="param.answerState == 1">
                        AND qp.PHONE_TYPE = 1
                    </when>
                    <otherwise>
                        AND (qp.PHONE_TYPE is null OR qp.PHONE_TYPE = 0)
                    </otherwise>
                </choose>
            </if>
            <if test="param.chargeType != null and param.chargeType != ''">
                <choose>
                    <when test="param.chargeType == 1">
                        AND (qp.charge_type is null or qp.charge_type = 1)
                    </when>
                    <when test="param.chargeType == 2">
                        AND qp.charge_type = 2
                    </when>
                    <when test="param.chargeType == 3">
                        AND qp.charge_type = 3
                    </when>
                    <when test="param.chargeType == 4">
                        AND charge_type = 4
                    </when>
                </choose>
            </if>
        </where>
        ) total
        order by total."startTime" desc
    </select>

</mapper>