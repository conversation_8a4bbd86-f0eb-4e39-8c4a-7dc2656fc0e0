<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.ParentMapper">

    <select id="getParentDbResultByStudentId" resultType="com.joinus.io.executor.ijxadmin.student.model.ParentDbResult">
        select sp.STUDENT_ID     studentId,
               sp.PARENT_ID      parentId,
               p.PARENT_NAME     parentName,
               p.TEL_NUM         phoneNum,
               p.IDENTITY        identity,
			   sp.CHILD_RELATION relationName,
			   sp.LIVE_TOGETHER  liveTogether
        from T_PARENT p,
            T_STUDENT_PARENT sp
        where p.ID = sp.PARENT_ID
          and p.ISACTIVE = 1
          and sp.STUDENT_ID = #{studentId}
        order by p.id
    </select>
</mapper>
