<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.IoExecutorTerminalMapper">

    <select id="listTerminalInfo" resultType="com.joinus.io.executor.ijxadmin.terminal.model.TerminalInfoExportDbResult">
        select
            s.id                                                                                        "id",
            sr.SUPER_REGION_NAME                                                                        "city",
            sr.REGION_NAME                                                                              "county",
            sr.SUPER_REGION_ID                                                                          "cityId",
            sr.REGION_ID                                                                                "countyId",
            s.terminal_num                                                                              "terminalNum",
            s.terminal_name                                                                             "terminalName",
            s.terminal_type                                                                             "terminalType",
            sd2.dict_desc                                                                               "terminalTypeName",
            s.type_id                                                                                   "typeId",
            s.type_name ||(select '[' || t.dict_desc || ']' from sys_dict t where t.dict_id = 5 and t.id = s.type_id) "typeName",
            s.sim_num                                                                                   "simNum",
            sd.DICT_DESC                                                                                "simStatus",
            s.LAST_CALL_TIME                                                                            "lastCallTime",
            tc.school_name                                                                              "schoolName",
            s.school_id                                                                                 "schoolId",
            (select sd.DICT_NAME from SYS_DICT sd where tc.SCHOOL_TYPE_ID = sd.ID and sd.dict_id = 1)   "schoolType",
            tc.business_flag                                                                            "businessFlag",
            s.install_address                                                                          "installAddress",
            s.soft_version                                                                              "softVersion",
            s.hard_version                                                                              "hardVersion",
            s.last_ip                                                                                   "lastIp",
            CASE
                WHEN s.white_num_flag IS NULL OR s.white_num_flag = 1 THEN '不一致'
                WHEN s.white_num_flag = 0 THEN '一致'
                WHEN s.white_num_flag > 1 THEN TO_CHAR(s.white_num_flag) END                            "whiteNumFlag",
            s.white_num_time                                                                            "whiteNumTime",
            s.white_num                                                                                 "whiteNum",
            s.is_login                                                                                  "isLogin",
            s.last_time                                                                                 "lastTime",
            s.terminal_status                                                                           "terminalStatus",
            s.install_time                                                                              "installTime",
            s.imei                                                                                      "IMEI",
            s.imsi                                                                                      "IMSI",
            s.isactive                                                                                  "isActive",
            s.IMGNUM                                                                                    "imgNum",
            s.INSTALL_TYPE                                                                              "installType",
            s.REMARK                                                                                    "remark",
            s.USER_NAMES                                                                                "userNames",
            s.PARENT_TERMINAL_ID                                                                        "parentTerminalId",
            s.EXIST_CHILD                                                                               "folder",
            s.sn                                                                                        "SN",
            to_char(s.deactivated_at,'yyyy-mm-dd HH:mi:ss')                                             "deactivatedAt",
            nvl(ttsi.total_costs, 0)                                                                    "totalCosts",
            nvl(ttsi.current_month_expense, 0)                                                          "currentMonthExpense",
            nvl(ttsi.discount_offers, 0)                                                                "discountOffers"
        FROM t_terminal s
        LEFT JOIN T_SCHOOL tc ON tc.ID = s.SCHOOL_ID
        LEFT JOIN SYS_REGION_NEW sr ON sr.REGION_ID = tc.NEW_REGION_ID
        left join SYS_DICT sd ON sd.DICT_NAME = s.SIM_STATUS and sd.dict_id=24
        left join sys_dict sd2 on s.terminal_type = sd2.id and sd2.dict_id=26
        left join t_terminal_sim_info ttsi on ttsi.terminal_id = s.id
        where s.id in (
            select whole.id
            from t_terminal whole
            start with whole.id in (
                select root.id
                from t_terminal root
                where root.parent_terminal_id = -1
                start with root.id in (
                    select currentnode.id
                    from t_terminal currentnode
                    left join t_school tc on tc.id = currentnode.school_id
                    left join sys_region_new sr on sr.region_id = tc.new_region_id
                    left join sys_dict sd on sd.dict_name = currentnode.sim_status
                    left join sys_dict sd2 on currentnode.terminal_type = sd2.id
                        <if test="null != isOverdrawn and isOverdrawn == 1">
                        inner join T_TERMINAL_SIM_INFO tsi on currentNode.id = tsi.terminal_id and tsi.is_overdrawn = 1
                        </if>
                    <where>
                        <if test="null != schoolId">
                            and currentNode.school_id in(#{schoolId})
                        </if>
                        <if test="null != softVersion and '' != softVersion">
                            and currentNode.soft_version = #{softVersion}
                        </if>
                        <if test="null != terminalType">
                            and currentNode.terminal_type = #{terminalType}
                        </if>
                        <if test="null != typeName and '' != typeName">
                            and currentNode.type_name = #{typeName}
                        </if>
                        <if test="null != imei and '' != imei">
                            and currentNode.imei = #{imei}
                        </if>
                        <if test="null != terminalName and '' != terminalName">
                            and currentNode.terminal_name like '%' || #{terminalName} || '%'
                        </if>
                        <if test="null != simNum and '' != simNum">
                            and currentNode.sim_num like '%' || #{simNum} || '%'
                        </if>
                        <if test="null != terminalNum and '' != terminalNum">
                            and currentNode.terminal_num like '%' || #{terminalNum} || '%'
                        </if>
                        <!-- 管理员用户地区查询 -->
                        <if test="null != regionCity">
                            and currentNode.school_id in (select h.id from t_school h where h.new_region_id in (select n.region_id from sys_region_new n  where n.super_region_id=#{regionCity} and n.region_level=3 and n.isactive=1))
                        </if>
                        <if test="null != regionCounty">
                            and currentNode.school_id in (select h.id from t_school h where h.new_region_id in (select n.region_id from sys_region_new n  where n.region_id=#{regionCounty} and n.region_level=3 and n.isactive=1))
                        </if>
                        <if test="null != regionProvince">
                            and currentNode.school_id in (select h.id from t_school h where h.new_region_id in (select n.region_id from sys_region_new n  where n.region_id like concat(#{regionProvince},'%')  and n.isactive=1))
                        </if>
                        <!-- 普通用户id查询对应所属权限的地区 -->
                        <if test="null != regionId">
                            and currentNode.school_id in (
                            select * from (
                            select s.ID from T_SCHOOL s  where s.new_region_id LIKE #{regionId}||'%'
                            union all
                            select s.ID from T_SCHOOL s, (
                            select srn.REGION_ID, srn4.REGION_ID "REGION_ID2" from SYS_REGION_NEW srn
                            left join SYS_REGION_NEW srn2 on srn.SUPER_REGION_ID = srn2.REGION_ID
                            left join SYS_REGION_NEW srn3 on srn2.SUPER_REGION_ID = srn3.REGION_ID
                            left join SYS_REGION_NEW srn4 on srn4.SUPER_REGION_ID = srn.REGION_ID,
                            SYS_USER_REGION sur
                            where (
                            srn.REGION_ID = sur.NEW_REGION_ID
                            or srn2.REGION_ID = sur.NEW_REGION_ID
                            or srn3.REGION_ID = sur.NEW_REGION_ID
                            or srn4.REGION_ID = sur.NEW_REGION_ID
                            )
                            ${regionAnd}
                            and sur.USER_ID = #{userId} ) temp
                            where s.NEW_REGION_ID = temp.REGION_ID or s.NEW_REGION_ID = temp.REGION_ID2
                            ) temp2
                            group by id
                            )
                        </if>
                        <if test="null != isLogin">
                            and currentNode.is_login = #{isLogin}
                        </if>
                        <if test="null != simStatusList and simStatusList.size > 0">
                            and sd.DICT_NAME in
                            <foreach collection="simStatusList" open="(" close=")" separator="," item="simStatus">
                                #{simStatus}
                            </foreach>
                        </if>
                        <if test="null != terminalStatusList and terminalStatusList.size > 0">
                            and currentNode.terminal_status in
                            <foreach collection="terminalStatusList" open="(" close=")" separator="," item="terminalStatus">
                                #{terminalStatus}
                            </foreach>
                        </if>
                        <if test="null != businessFlagList and businessFlagList.size > 0">
                            and tc.business_flag in
                            <foreach collection="businessFlagList" open="(" close=")" separator="," item="businessFlag">
                                #{businessFlag}
                            </foreach>
                        </if>
                        <if test="null != whiteNumFlag">
                            <if test="whiteNumFlag == 0">
                                and currentNode.white_num_flag = #{whiteNumFlag}
                            </if>
                            <if test="whiteNumFlag == 1">
                                and (currentNode.white_num_flag = #{whiteNumFlag} or currentNode.white_num_flag is null)
                            </if>
                        </if>
                        <if test="null != isActive">
                            and currentNode.isactive = #{isActive}
                        </if>
                        <if test="null != beginDate">
                            and currentNode.last_time &gt;= #{beginDate}
                        </if>
                        <if test="null != endDate">
                            and currentNode.last_time &lt;= #{endDate}
                        </if>
                        <if test="null != beginInstallDate">
                            and currentNode.INSTALL_TIME &gt;= #{beginInstallDate}
                        </if>
                        <if test="null != endInstallDate">
                            and currentNode.INSTALL_TIME &lt;= #{endInstallDate}
                        </if>
                        <if test="null != oprcode and '' != oprcode">
                            and n.oprcode = #{oprcode}
                        </if>
                        <if test="null != installP">
                            and currentNode.USER_NAMES like '%'||#{installP}||'%'
                        </if>
                        <if test="null != id">
                            and currentNode.parent_terminal_id = #{id}
                        </if>
                    </where>
                    )
                CONNECT BY PRIOR root.PARENT_TERMINAL_ID = root.ID
                )
            CONNECT BY PRIOR whole.ID = whole.PARENT_TERMINAL_ID
        )
        <if test="null != sn and '' != sn ">
            and  s.sn like '%' || #{sn} || '%'
        </if>
        order by s.terminal_num
    </select>
</mapper>