<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.StudentParentMapper">

    <!--根据学生Id获取亲情号码-->
    <select id="getQinPhoneByStudentId" resultType="java.lang.String" >
        select  pa.tel_num
        from T_STUDENT_PARENT sp,T_PARENT pa  where sp.student_id = #{studentId} and pa.id = sp.parent_id and pa.isactive=1 and pa.TEL_NUM is not null order by  PARENT_SORT
    </select>


    <!--根据学生Id获取亲情号码-->
    <select id="getQinPhoneDbResultByStudentId" resultType="com.joinus.io.executor.ijxadmin.student.model.QinPhoneDbResult"  >
        select pa.id, pa.TEL_NUM, sp.STUDENT_ID, sp.PARENT_SORT, sp.CHILD_RELATION "relationName",pa.id "parentId"
        from T_STUDENT_PARENT sp,
             T_PARENT pa,
             T_STUDENT s
        where sp.PARENT_ID = pa.id
          and s.ID = sp.STUDENT_ID
          and sp.STUDENT_ID = #{studentId}
          and pa.TEL_NUM is not null
          AND pa.ISACTIVE = 1
        order by sp.parent_sort  NULLS LAST
    </select>

    <update id="mergeStudentParent">
        MERGE INTO t_student_parent tsp
            USING (SELECT #{studentId}  AS student_id,
                          #{parentId}   AS parent_id,
                          #{parentSort} AS parent_sort
                   FROM dual) src
            ON (
                        tsp.student_id = src.student_id AND
                        tsp.parent_id = src.parent_id
                )
            WHEN MATCHED THEN
                UPDATE SET tsp.parent_sort = src.parent_sort
            WHEN NOT MATCHED THEN
                INSERT (student_id, parent_id, parent_sort)
                    VALUES (src.student_id, src.parent_id, src.parent_sort)
    </update>

    <select id="listQinPhone" resultType="com.joinus.io.executor.model.entity.QinPhone">
        select ts.id studentId,
        MAX(CASE WHEN tsp.parent_sort = 1 THEN tp.tel_num END) AS phone1,
        MAX(CASE WHEN tsp.parent_sort = 2 THEN tp.tel_num END) AS phone2,
        MAX(CASE WHEN tsp.parent_sort = 3 THEN tp.tel_num END) AS phone3,
        MAX(CASE WHEN tsp.parent_sort = 4 THEN tp.tel_num END) AS phone4,
        MAX(CASE WHEN tsp.parent_sort = 5 THEN tp.tel_num END) AS phone5,
        MAX(CASE WHEN tsp.parent_sort = 6 THEN tp.tel_num END) AS phone6,
        MAX(CASE WHEN tsp.parent_sort = 7 THEN tp.tel_num END) AS phone7,
        MAX(CASE WHEN tsp.parent_sort = 8 THEN tp.tel_num END) AS phone8,
        MAX(CASE WHEN tsp.parent_sort = 9 THEN tp.tel_num END) AS phone9,
        MAX(CASE WHEN tsp.parent_sort = 10 THEN tp.tel_num END) AS phone10
        FROM t_student ts
        left join t_student_parent tsp on tsp.student_id = ts.id
        left join t_parent tp on tp.id = tsp.parent_id and tp.isactive = 1
        where ts.school_id in
        <foreach collection="schoolIdList" open="(" close=")" separator="," item="schoolId">
            #{schoolId}
        </foreach>
        and ts.isactive in
        <foreach collection="isActiveList" open="(" close=")" separator="," item="active">
            #{active}
        </foreach>
        group by ts.id
    </select>

</mapper>
