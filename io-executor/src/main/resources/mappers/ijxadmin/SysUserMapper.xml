<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.SysUserMapper">

    <select id="listRegionsByUserId" resultType="java.lang.String">
        select u.new_region_id "regionId"
        from sys_region_new t
                 inner join sys_user_region u on t.region_id = u.new_region_id
        where u.user_id = #{userId}
        order by u.new_region_id
    </select>


    <select id="listSchoolIdByUserId" resultType="java.lang.Long">
        select t.school_id
        from sys_user_school t
        where t.user_id= #{userId}
    </select>

    <select id="getSysRegionNewByAuth" resultType="com.joinus.io.executor.model.entity.RegionNew">
        SELECT DISTINCT region_id,
                        super_region_id,
                        region_name,
                        super_region_name,
                        full_name,
                        isactive,
                        region_code,
                        isleaf,
                        otherid,
                        region_level
        FROM sys_region_new t
        WHERE t.isactive = 1
            START
        WITH region_id IN <foreach item="id" collection="ids" open ="(" separator="," close =")">
            #{id} </foreach>
        CONNECT BY PRIOR t.super_region_id = region_id

        UNION

        SELECT DISTINCT region_id,
                        super_region_id,
                        region_name,
                        super_region_name,
                        full_name,
                        isactive,
                        region_code,
                        isleaf,
                        otherid,
                        region_level
        FROM sys_region_new t
        WHERE t.isactive = 1
            START
        WITH region_id IN <foreach item="id" collection="ids" open ="(" separator="," close =")">
            #{id} </foreach>
        CONNECT BY PRIOR t.region_id = super_region_id
    </select>

</mapper>