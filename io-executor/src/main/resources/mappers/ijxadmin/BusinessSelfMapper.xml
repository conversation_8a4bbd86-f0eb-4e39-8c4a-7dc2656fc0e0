<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.BusinessSelfMapper">

    <select id="listBusinessSelf"
            resultType="com.joinus.io.executor.ijxadmin.student.model.BusinessDbResult">
        select ts.id,
            ts.id || t.phone_num "stuIdAndMobile",
            p.product_name,
            t.start_time,
            t.end_time
        from t_business_self t
        left join t_product_self p on t.product_id = p.id
        left join t_student ts on ts.id = t.student_id and ts.isactive in
            <foreach collection="activeList" open="(" close=")" separator="," item="isactive">
                #{isactive}
            </foreach>
        where ts.school_id in
            <foreach collection="schoolIdList" open="(" close=")" separator="," item="schoolId">
                #{schoolId}
            </foreach>
    </select>


    <!-- 查询开通的自推广业务 -->
    <select id="getBusinessSelf" resultType="com.joinus.io.executor.ijxadmin.student.model.BusinessSelfDbResult">
        select p.product_name || '(' || to_char(t.start_time, 'yyyy-mm-dd') || '---' ||
               to_char(t.end_time, 'yyyy-mm-dd') || ')' "businessName",
               t.id                                     "businessId",
               p.product_name                           "productName",
               to_char(t.start_time, 'yyyy-mm-dd')      "startTime",
               to_char(t.end_time, 'yyyy-mm-dd')        "endTime",
               tqa.sign_platform_type                   "signPlatformType"
        from t_business_self t
                 left join t_product_self p on t.product_id = p.ID
                 left join t_parent tp on tp.tel_num = t.phone_num and tp.isactive = 1
                 left join t_qyl_auto_renew_info tqa on tqa.student_id = t.student_id and tqa.parent_id = tp.id
            and tqa.sign_state = 1
        where t.BUSINESS_TYPE = #{type}
          and t.STUDENT_ID = #{studentId}
          and t.PHONE_NUM = #{telNum}
    </select>
</mapper>
