<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.OutreachSchoolRecordMapper">


    <select id="getTodaySchoolOutreachTerminalSnapShotDetectionRecordsByOutreachId"
            resultType="com.joinus.io.executor.model.outreach.OutreachTerminalSnapshotResult">
        select * from ( select tots.*,
        sd.dict_desc terminalTypeDesc,
        nvl2(nvl(tqpd.start_time, tcsr.save_time), '有', '无') hasdetectionrecords,
        nvl2(nvl(tqpd.start_time, tcsr.save_time), su.user_name, '-') detectionusername,
        nvl2(nvl(tqpd.start_time, tcsr.save_time),
        to_char(nvl(tqpd.start_time, tcsr.save_time),
        'yyyy-mm-dd hh24:mi:ss'),
        '-') detectiontime
        from t_outreach_school_record tosr
        inner join t_outreach_terminal_snapshot tots
        on tosr.id = tots.outreach_id
        left join sys_user su
        on tosr.visit_user_id = su.id
        left join t_student ts
        on su.identity = ts.identity
        and ts.school_id=tosr.organization_id
        left join (select phone, terminal_num, max(start_time) start_time
        from t_qin_phone_detail
        group by phone, terminal_num) tqpd
        on su.telnum = tqpd.phone
        and tots.terminal_num = tqpd.terminal_num
        left join (select card_num, terminal_num, max(CARD_TIME) save_time
        from t_card_signin_record6
        where CARD_TIME>= TRUNC(sysdate) and CARD_TIME &lt; TRUNC(sysdate+1)
        group by card_num, terminal_num) tcsr
        on ts.cardcode = tcsr.card_num
        and tots.terminal_num = tcsr.terminal_num
        left join (SELECT t.id,t.dict_desc FROM sys_dict t where t.dict_id=26 ) sd
        on tots.terminal_type=sd.id
        where tosr.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ) order by id, hasdetectionrecords desc ,detectiontime desc,terminalTypeDesc
    </select>


    <select id="getSchoolOutreachTerminalSnapShotDetectionRecordsByOutreachId" resultType="com.joinus.io.executor.model.outreach.OutreachTerminalSnapshotResult">
        select * from (  select tots.*,
         sd.dict_desc  terminalTypeDesc,
        nvl2(nvl(tqpd.start_time, tcsr.save_time), '有', '无') hasdetectionrecords,
        nvl2(nvl(tqpd.start_time, tcsr.save_time), su.user_name, '-') detectionusername,
        nvl2(nvl(tqpd.start_time, tcsr.save_time),
        to_char(nvl(tqpd.start_time, tcsr.save_time),
        'yyyy-mm-dd hh24:mi:ss'),
        '-') detectiontime
        from t_outreach_school_record tosr
        inner join t_outreach_terminal_snapshot tots
        on tosr.id = tots.outreach_id
        left join sys_user su
        on tosr.visit_user_id = su.id
        left join t_student ts
        on su.identity = ts.identity
        and ts.school_id=tosr.organization_id
        left join (select phone, terminal_num, max(start_time) start_time
        from t_qin_phone_dtl_his  where LOGDATE >= TRUNC(#{param.outreachStartDate}) and  LOGDATE &lt; TRUNC(#{param.outreachEndDate})
        group by phone, terminal_num) tqpd
        on su.telnum = tqpd.phone
        and tots.terminal_num = tqpd.terminal_num
        left join (select CARDNUM, TERMINALNUM, max(CARDTIME) save_time
        from T_CARD_SIGNIN_RECORD_HIS  where  CARDTIME>= TRUNC(#{param.outreachStartDate}) and  CARDTIME &lt; TRUNC(#{param.outreachEndDate})
        group by CARDNUM, TERMINALNUM) tcsr
        on ts.cardcode = tcsr.CARDNUM
        and tots.terminal_num = tcsr.TERMINALNUM
        left join (SELECT t.id,t.dict_desc FROM sys_dict t where t.dict_id=26 ) sd
        on tots.terminal_type=sd.id
        where tosr.id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        ) order by id, hasdetectionrecords desc ,detectiontime desc,terminalTypeDesc
    </select>
</mapper>