<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.io.executor.mapper.SchoolMapper">
    <select id="listSchoolUnion" resultType="com.joinus.io.executor.ijxadmin.school.model.SchoolInfoExportDbResult">
        <![CDATA[
        SELECT
            a.id "schoolId",
            a.charge_time,
            a.pay_method,
            a.dorm_time,
            a.groupid,
            a.isactive,
            a.iskai,
            a.isunify,
            a.isverify,
            a.iswhite,
            a.menwei_msg_flag,
            a.msgport,
            a.prodid,
            a.school_code,
            a.school_desc,
            a.school_name,
            a.status,
            a.summer_end,
            a.summer_start,
            a.winter_end,
            a.winter_start,
            a.school_type_id,
            a.parent_id,
            a.region_id,
            a.ismanager,
            a.school_name_qp,
            a.school_name_jp,
            a.add_time,
            a.org_id,
            a.new_region_id,
            a.oa_domain_id,
            a.pay_method,
            a.wm_teacher_groupid,
            a.wm_parent_groupid,
            a.ten_qinphone,
            a.non_mobile_support,
            a.view_credit_mall,
            a.m1kbcardquota,
            a.g24kbcardquota,
            a.fill_card,
            a.fill_card_telnum,
            a.school_address,
            a.business_flag,
            a.custom_level,
            a.card_hardtype,
            a.card_type,
            r.full_name "regionName",
               (select to_char (substr (wmsys.wm_concat (user_name), 1, 1000))
				                  user_name
				          from sys_role e
				               inner join sys_group d on d.role_id = e.id
				               inner join sys_user_group c on c.group_id = d.id
				               inner join sys_user b on b.id = c.user_id
				               inner join sys_user_school f on f.user_id = b.id
				         where f.school_id = a.id and e.id in (6,215))
				          as market_manager,
				       (select to_char (substr (wmsys.wm_concat (telnum), 1, 1000)) telnum
				          from sys_role e
				               inner join sys_group d on d.role_id = e.id
				               inner join sys_user_group c on c.group_id = d.id
				               inner join sys_user b on b.id = c.user_id
				               inner join sys_user_school f on f.user_id = b.id
				         where f.school_id = a.id and e.id in (6,215))
				          as market_telnum,
				          (select to_char (substr (wmsys.wm_concat (user_name ||'('||telnum||')'), 1, 1000))
			       		serviceManager
			          from sys_role e
			               inner join sys_group d on d.role_id = e.id
			               inner join sys_user_group c on c.group_id = d.id
			               inner join sys_user b on b.id = c.user_id
			               inner join sys_user_school f on f.user_id = b.id
			         where f.school_id = a.id and e.id in (6,215))
			          as service_manager,
			            (select to_char (substr (wmsys.wm_concat (user_name ||'('||telnum||')'), 1, 1000))
			       		serviceAttache
			          from sys_role e
			               inner join sys_group d on d.role_id = e.id
			               inner join sys_user_group c on c.group_id = d.id
			               inner join sys_user b on b.id = c.user_id
			               inner join sys_user_school f on f.user_id = b.id
			         where f.school_id = a.id and e.id in (334,454))
			          as service_attache,
				    (select hc.cardtype_name
                 		from t_hardtype_cardtype hc
                 		where a.card_hardtype = hc.id)
                 		as cardhardtype_name,
              		(select hc.cardtype_name
                 		from t_hardtype_cardtype hc
                 		where a.card_type =hc.id)
                 		as cardtype_name,
            NVL(a.contract_number, 0) AS contractNumber,
            a.service_type,
			a.card_category
        FROM t_school a
        LEFT JOIN sys_region_new r ON a.new_region_id = r.region_id
        ]]>
        <if test="selectType != null">
            LEFT JOIN sys_product_school s ON s.SCHOOL_ID = a.id
            <if test="selectType == 'hn'">
                LEFT JOIN t_product p ON p.id = s.product_id
            </if>
        </if>
        WHERE a.isactive = 1
        <if test="schoolId != null">
            AND a.id = #{schoolId}
        </if>
        <if test="regionId != null and regionId != ''">
            AND EXISTS (
            SELECT 1 FROM (
            REGION_ID FROM SYS_REGION_NEW r
            START WITH r.REGION_ID = #{regionId}
            CONNECT BY PRIOR r.REGION_ID = r.SUPER_REGION_ID
            ) b WHERE a.new_region_id = b.REGION_ID
            )
        </if>
        <if test="regionIds != null and regionIds.size() > 0">
            AND (
            <foreach collection="regionIds" item="id" separator="OR">
                a.new_region_id = #{id}
            </foreach>
            )
        </if>
        <if test="serviceType != null and  serviceType.length >0">
            AND
            <foreach item="flag" collection="serviceType" open="(" separator="or " close=")">
                a.service_type  like '%' ||	  #{flag}  || '%'
            </foreach>
        </if>

        <if test="cardHardtype != null">
            AND a.card_hardtype = #{cardHardtype}
        </if>

        <if test="cardCategory != null">
            AND a.card_category like '%'|| #{cardCategory} ||'%'
        </if>

        <if test="selectType != null">
            <choose>
                <when test="selectType == 'self'">
                    AND (s.product_id = #{segmentType} AND s.product_type = 1)
                </when>
                <when test="selectType == 'hn'">
                    AND (p.code = #{segmentType} AND s.product_type = 0)
                </when>
            </choose>
        </if>

        <if test="isMember != null and isMember == '0'">
            AND a.groupid IS NULL
        </if>

        <if test="customLevel != null and customLevel != ''">
            AND a.custom_level = #{customLevel}
        </if>
        <if test="regionIds != null and regionIds.size() > 0">
            AND (
            <foreach collection="regionIds" item="id" separator="OR">
                a.new_region_id = #{id}
            </foreach>
            )
        </if>
        <if test="businessFlags != null and businessFlags.length  > 0">
            AND (
            <foreach collection="businessFlags" item="businessFlag" separator="OR">
                a.business_flag = #{businessFlag}
            </foreach>
            )
        </if>

        <![CDATA[
        UNION
        SELECT
           a.id "schoolId",
				       a.charge_time,
				       a.pay_method,
				       a.dorm_time,
				       a.groupid,
				       a.isactive,
				       a.iskai,
				       a.isunify,
				       a.isverify,
				       a.iswhite,
				       a.menwei_msg_flag,
				       a.msgport,
				       a.prodid,
				       a.school_code,
				       a.school_desc,
				       a.school_name,
				       a.status,
				       a.summer_end,
				       a.summer_start,
				       a.winter_end,
				       a.winter_start,
				       a.school_type_id,
				       a.parent_id,
				       a.region_id,
				       a.ismanager,
				       a.school_name_qp,
				       a.school_name_jp,
				       a.add_time,
				       a.org_id,
				       a.new_region_id,
				       a.oa_domain_id,
				       a.pay_method,
				       a.wm_teacher_groupid,
        		   	   a.wm_parent_groupid,
        		   	   a.ten_qinphone,
				       a.non_mobile_support,
        		   	   a.view_credit_mall,
				       a.m1kbcardquota,a.g24kbcardquota,a.fill_card,a.fill_card_telnum,a.school_address,
				       a.business_flag,a.custom_level,a.card_hardtype,a.card_type,
				       (select to_char (substr (wmsys.wm_concat (user_name), 1, 1000))
				                  user_name
				          from sys_role e
				               inner join sys_group d on d.role_id = e.id
				               inner join sys_user_group c on c.group_id = d.id
				               inner join sys_user b on b.id = c.user_id
				               inner join sys_user_school f on f.user_id = b.id
				         where f.school_id = a.id and e.id in (6,215))
				          as market_manager,
				       (select to_char (substr (wmsys.wm_concat (telnum), 1, 1000)) telnum
				          from sys_role e
				               inner join sys_group d on d.role_id = e.id
				               inner join sys_user_group c on c.group_id = d.id
				               inner join sys_user b on b.id = c.user_id
				               inner join sys_user_school f on f.user_id = b.id
				         where f.school_id = a.id and e.id in (6,215))
				          as market_telnum,
				          (select to_char (substr (wmsys.wm_concat (user_name ||'('||telnum||')'), 1, 1000))
			       		serviceManager
			          from sys_role e
			               inner join sys_group d on d.role_id = e.id
			               inner join sys_user_group c on c.group_id = d.id
			               inner join sys_user b on b.id = c.user_id
			               inner join sys_user_school f on f.user_id = b.id
			         where f.school_id = a.id and e.id in (6,215))
			          as service_manager,
			            (select to_char (substr (wmsys.wm_concat (user_name ||'('||telnum||')'), 1, 1000))
			       		serviceAttache
			          from sys_role e
			               inner join sys_group d on d.role_id = e.id
			               inner join sys_user_group c on c.group_id = d.id
			               inner join sys_user b on b.id = c.user_id
			               inner join sys_user_school f on f.user_id = b.id
			         where f.school_id = a.id and e.id in (334,454))
			          as service_attache,
				    (select hc.cardtype_name
                 		from t_hardtype_cardtype hc
                 		where a.card_hardtype = hc.id)
                 		as cardhardtype_name,
              		(select hc.cardtype_name
                 		from t_hardtype_cardtype hc
                 		where a.card_type =hc.id)
                 		as cardtype_name,
			  	       r.full_name "regionName",
                 		nvl(a.contract_number,0) contractNumber,
            a.service_type,
			a.card_category
        FROM t_school a
        LEFT JOIN sys_region_new r ON a.new_region_id = r.region_id
        ]]>
        <if test="selectType != null">
            LEFT JOIN sys_product_school s ON s.SCHOOL_ID = a.id
            <if test="selectType == 'hn'">
                LEFT JOIN t_product p ON p.id = s.product_id
            </if>
        </if>
        WHERE a.isactive = 1
        <if test="schoolId != null">
            AND a.id = #{schoolId}
        </if>
        <if test="regionId != null and regionId != ''">
            AND EXISTS (
            SELECT 1 FROM (
            REGION_ID FROM SYS_REGION_NEW
            START WITH r.REGION_ID = #{regionId}
            CONNECT BY PRIOR r.REGION_ID = r.SUPER_REGION_ID
            ) b WHERE a.new_region_id = b.REGION_ID
            )
        </if>
        <if test="regionIds != null and regionIds.size() > 0">
            AND (
            <foreach collection="regionIds" item="id" separator="OR">
                a.new_region_id = #{id}
            </foreach>
            )
        </if>
        <if test="serviceType != null and  serviceType.length >0">
            AND
            <foreach item="flag" collection="serviceType" open="(" separator="or " close=")">
                a.service_type  like '%' ||	  #{flag}  || '%'
            </foreach>
        </if>
        <if test="cardHardtype != null">
            AND a.card_hardtype= #{cardHardtype}
        </if>
        <if test="cardCategory != null">
            AND a.card_category like '%'|| #{cardCategory} ||'%'
        </if>
        <if test="selectType != null">
            <choose>
                <when test="selectType == 'self'">
                    AND (s.product_id = #{segmentType} and s.product_type = 1)
                </when>
                <when test="selectType == 'hn'">
                    AND (p.code = #{segmentType} and s.product_type = 0)
                </when>
            </choose>
        </if>
        <if test="isMember == 0">
            AND a.groupid is null
        </if>
        <if test="customLevel != null and customLevel != ''">
            AND a.custom_level= #{customLevel}
        </if>
        <if test="businessFlags != null and businessFlags.length  > 0">
            AND (
            <foreach collection="businessFlags" item="businessFlag" separator="OR">
                a.business_flag = #{businessFlag}
            </foreach>
            )
        </if>
    </select>

    <select id="listSchool" resultType="com.joinus.io.executor.ijxadmin.school.model.SchoolInfoExportDbResult">
        select a.id "schoolId",
        a.charge_time,
        a.pay_method,
        a.dorm_time,
        a.groupid,
        a.isactive,
        a.iskai,
        a.isunify,
        a.isverify,
        a.iswhite,
        a.menwei_msg_flag,
        a.msgport,
        a.prodid,
        a.school_code,
        a.school_desc,
        a.school_name,
        a.status,
        a.summer_end,
        a.summer_start,
        a.winter_end,
        a.winter_start,
        a.school_type_id,
        a.parent_id,
        a.region_id,
        a.ismanager,
        a.school_name_qp,
        a.school_name_jp,
        a.add_time,
        a.org_id,
        a.new_region_id,
        a.oa_domain_id,
        a.pay_method,
        a.wm_teacher_groupid,
        a.wm_parent_groupid,
        a.ten_qinphone,
        a.non_mobile_support,
        a.view_credit_mall,
        a.m1kbcardquota,a.g24kbcardquota,a.fill_card,a.fill_card_telnum,a.school_address,
        a.business_flag,a.custom_level,a.card_hardtype,a.card_type,
        (select to_char (substr (wmsys.wm_concat (user_name), 1, 1000))
        user_name
        from sys_role e
        inner join sys_group d on d.role_id = e.id
        inner join sys_user_group c on c.group_id = d.id
        inner join sys_user b on b.id = c.user_id
        inner join sys_user_school f on f.user_id = b.id
        where f.school_id = a.id and e.id in (6,215))
        as market_manager,
        (select to_char (substr (wmsys.wm_concat (telnum), 1, 1000)) telnum
        from sys_role e
        inner join sys_group d on d.role_id = e.id
        inner join sys_user_group c on c.group_id = d.id
        inner join sys_user b on b.id = c.user_id
        inner join sys_user_school f on f.user_id = b.id
        where f.school_id = a.id and e.id in (6,215))
        as market_telnum,
        (select to_char (substr (wmsys.wm_concat (user_name ||'('||telnum||')'), 1, 1000))
        serviceManager
        from sys_role e
        inner join sys_group d on d.role_id = e.id
        inner join sys_user_group c on c.group_id = d.id
        inner join sys_user b on b.id = c.user_id
        inner join sys_user_school f on f.user_id = b.id
        where f.school_id = a.id and e.id in (6,215))
        as service_manager,
        (select to_char (substr (wmsys.wm_concat (user_name ||'('||telnum||')'), 1, 1000))
        serviceAttache
        from sys_role e
        inner join sys_group d on d.role_id = e.id
        inner join sys_user_group c on c.group_id = d.id
        inner join sys_user b on b.id = c.user_id
        inner join sys_user_school f on f.user_id = b.id
        where f.school_id = a.id and e.id in (334,454))
        as service_attache,
        (select hc.cardtype_name
        from t_hardtype_cardtype hc
        where a.card_hardtype = hc.id)
        as cardhardtype_name,
        (select hc.cardtype_name
        from t_hardtype_cardtype hc
        where a.card_type =hc.id)
        as cardtype_name,
        r.full_name "regionName",
        nvl(a.contract_number,0) contractNumber,
        a.service_type,
        a.card_category
        FROM t_school a
        LEFT JOIN sys_region_new r ON a.new_region_id = r.region_id

        <if test="selectType != null">
            LEFT JOIN sys_product_school s ON s.SCHOOL_ID = a.id
            <if test="selectType == 'hn'">
                LEFT JOIN t_product p ON p.id = s.product_id
            </if>
        </if>
        WHERE a.isactive = 1
        <if test="schoolId != null">
            AND a.id = #{schoolId}
        </if>
        <if test="regionId != null and regionId != '' ">
            AND exists (select * from (select REGION_ID from SYS_REGION_NEW r
            start with r.REGION_ID =  #{regionId}
            connect by prior r.REGION_ID = r.SUPER_REGION_ID) b where a.new_region_id =b.REGION_ID)
        </if>
        <if test="regionIds != null and regionIds.size()> 0">
            AND (
            <foreach collection="regionIds" item="id" separator="OR">
                a.new_region_id = #{id}
            </foreach>
            )
        </if>

        <if test="schoolIds != null and schoolIds.size()> 0">
            AND a.id IN
            <foreach item="schoolId" collection="schoolIds" open="(" separator="," close=")">
                #{schoolId}
            </foreach>
        </if>
        <if test="serviceType != null and  serviceType.length >0">
            AND
            <foreach item="flag" collection="serviceType" open="(" separator="or " close=")">
                a.service_type  like '%' ||	  #{flag}  || '%'
            </foreach>
        </if>
        <if test="cardHardtype != null">
            AND a.card_hardtype = #{cardHardtype}
        </if>
        <if test="cardCategory != null">
            AND  a.card_category like '%'|| #{cardCategory} ||'%'
        </if>
          
        <if test="selectType != null">
            <choose>
                <when test="selectType == 'self'">
                    AND (s.product_id = #{segmentType} and s.product_type = 1)
                </when>
                <when test="selectType == 'hn'">
                    AND (p.code = #{segmentType} and s.product_type = 0)
                </when>
            </choose>
        </if>

        <if test="isMember == 0">
            AND a.groupid is null
        </if>
        <if test="customLevel != null and customLevel != ''">
            AND a.custom_level= #{customLevel}
        </if>

        <if test="businessFlags != null and businessFlags.length  > 0">
            AND (
            <foreach collection="businessFlags" item="businessFlag" separator="OR">
                a.business_flag = #{businessFlag}
            </foreach>
            )
        </if>
    </select>

    <!-- 批量统计学校的相关数量信息 -->
    <select id="getStatisticalSchoolBatch" resultType="com.joinus.io.executor.ijxadmin.school.model.StatisticalDbSchool" >
        <!-- 查詢符合條件的學校 -->
        with school_ids as (
        select id from t_school t where isactive=1
        and t.id in
        <foreach item="schoolId" collection="schoolIdList" open="(" separator="," close=")">
            #{schoolId}
        </foreach>
        )

        <![CDATA[
		select school.school_id schoolId,nvl(grade.con,0) gradeNumber,nvl(class.con,0) classNumber,nvl(student.con,0) studentNumber,nvl(residence.con,0) residenceNumber,
			nvl(dayNumber.con,0) dayNumber,nvl(business.con,0) businessNumber,nvl(businessStudent.con,0) businessStudentNumber,nvl(useBusiness.con,0) useBusinessNumber,nvl(notbusiness.con,0) notbusinessNumber
		from
			(select id school_id from t_school where isactive=1 and id in (select * from (school_ids) group by id)) school left join
			(select count(0) con,school_id from t_grade where isactive = 1 and school_id in (select * from (school_ids)) group by school_id) grade
				on  school.school_id = grade.school_id  left join
			(select count(0) con,school_id from t_class where isactive = 1 and school_id in (select * from (school_ids)) group by school_id) class
				on school.school_id = class.school_id left join
			(select count(0) con,school_id from t_student where  isactive = 1 and school_id in (select * from (school_ids)) group by school_id) student
				on school.school_id = student.school_id left join
			(select count(0) con,school_id from t_student where  isactive = 1 and isdorm=1 and school_id in (select * from (school_ids)) group by school_id) residence
				on school.school_id = residence.school_id left join
			(select count(0) con,school_id from t_student where isactive = 1 and isdorm=0 and school_id in (select * from (school_ids)) group by school_id) dayNumber
				on school.school_id = dayNumber.school_id left join
			(SELECT COUNT(b.student_id) con,b.school_id FROM t_business b inner join t_student s on s.id=b.student_id WHERE (b.status=1 or b.status=6) and b.product_code <> 'X' and s.isactive = 1 and b.school_id in (select * from (school_ids))  group by b.school_id) business
				on school.school_id = business.school_id left join
			(SELECT COUNT(distinct b.student_id) con,b.school_id FROM t_business b inner join t_student s on s.id=b.student_id WHERE (b.status=1 or b.status=6) and b.product_code <> 'X' and s.isactive = 1 and b.school_id in (select * from (school_ids))  group by b.school_id) businessStudent
				on school.school_id = businessStudent.school_id left join
			(SELECT COUNT(b.student_id) con,b.school_id FROM t_business b inner join t_student s on s.id=b.student_id WHERE b.status = 2 and s.isactive = 1 and b.school_id in (select * from (school_ids)) group by b.school_id) useBusiness
				on school.school_id = useBusiness.school_id left join
			(select count(1) con,s.school_id from t_student s where not exists(select 1 from t_business b where s.id=b.student_id and (b.status=1 or b.status=2 or b.status=5)) and s.isactive=1 and s.school_id in (select * from (school_ids)) group by s.school_id) notbusiness
				on school.school_id = notbusiness.school_id
		]]>
    </select>

</mapper>
