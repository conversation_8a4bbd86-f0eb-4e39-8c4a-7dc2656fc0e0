app:
  id: snake-io-executor
server:
  port: 9099
spring:
  profiles:
    active: dev
  kafka:
    bootstrap-servers: ***************:9092
    consumer:
      properties:
        group:
          id: snake-io-executor
      enable-auto-commit: false
    listener:
      ack-mode: manual_immediate
  application:
    name: snake-io-executor
  redis:
    host: ***************  # Redis服务器的主机名或IP地址
    port: 6379            # Redis服务器的端口
    password: ijxuat-huaweiyun  # 如果设置了密码验证，则需要提供密码
    database: 0           # 使用的数据库索引，默认通常是0
    timeout: 200           # 命令超时时间（秒）
  datasource:
    dynamic:
      primary: oracle
      strict: false
      datasource:
        oracle:
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ${jdbc.url:*************************************}
          username: ${ORACLE_USERNAME:ijx}
          password: ${ORACLE_PWD:ijxuat9671-hwy}
apollo:
  bootstrap:
    enabled: true
    namespaces: application,basic.common.database
    eagerLoad:
      enabled: true
mybatis-plus:
  mapper-locations: classpath*:/mappers/**/*Mapper.xml,classpath*:/mapper/*.xml
  global-config:
    db-config:
      id-type: input
      insert-strategy: not_null
  configuration:
    jdbc-type-for-null: "null"
logging:
  level:
    com.joinus.io.executor: INFO
    org.apache: INFO
    org.apache.kafka.clients: ERROR
swagger:
  enable: false
