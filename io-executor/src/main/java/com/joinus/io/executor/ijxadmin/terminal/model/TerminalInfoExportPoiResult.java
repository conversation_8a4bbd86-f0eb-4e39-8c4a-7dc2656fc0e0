package com.joinus.io.executor.ijxadmin.terminal.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.joinus.io.executor.ijxadmin.terminal.enums.*;
import com.joinus.io.executor.model.BasePoiResult;
import com.joinus.io.executor.utils.EasyExcelEnumConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TerminalInfoExportPoiResult extends BasePoiResult {

    @ExcelProperty("级别")
    private String levelDesc;

    public String getLevelDesc() {
        return ExcelLevelColorEnum.getLevelDescByLevel(level);
    }

    @ExcelProperty("地市")
    private String city;

    @ExcelProperty("县区")
    private String county;

    @ExcelProperty("终端号")
    private String terminalNum;

    @ExcelProperty("终端名称")
    private String terminalName;

    @ExcelProperty("终端类型")
    private String terminalTypeName;

    @ExcelProperty("功能分类")
    private String typeName;

    @ExcelProperty("SIM卡号")
    private String simNum;


    @ExcelProperty("SIM卡状态")
    private String simStatus;

    @ExcelProperty(value = "SIM卡维护状态", converter = EasyExcelEnumConverter.class)
    private TerminalOprCodeEnum oprCode;

    @ExcelProperty(value = "SIM卡套餐编号", converter = EasyExcelEnumConverter.class)
    private SimPriceEnum price;

    @ExcelProperty("最后呼叫时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date lastCallTime;

    @ExcelProperty("终端所属学校")
    private String schoolName;

    @ExcelProperty("学段")
    private String schoolType;

    @ExcelProperty(value = "业务标识", converter = EasyExcelEnumConverter.class)
    private TerminalBusinessFlagEnum businessFlag;

    @ExcelProperty("安装位置")
    private String installAddress;

    @ExcelProperty("软件")
    private String softVersion;

    @ExcelProperty("硬件")
    private String hardVersion;

    @ExcelProperty("网关通道")
    private String lastIp;

    @ExcelProperty("通告")
    private String whiteNumFlag;

    @ExcelProperty("通告时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date whiteNumTime;

    @ExcelProperty("白名单数量")
    private String whiteNum;

    @ExcelProperty(value = "状态", converter = EasyExcelEnumConverter.class)
    private TerminalLoginEnum isLogin;

    @ExcelProperty("最后在线")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(20)
    private Date lastTime;

    @ExcelProperty(value = "终端使用状态", converter = EasyExcelEnumConverter.class)
    private TerminalStatusEnum terminalStatus;

    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty("初始安装时间")
    @ContentStyle
    private Date installTime;

    @ExcelProperty("IMEI")
    private String IMEI;

    @ExcelProperty("SN")
    private String SN;

    @ExcelProperty("IMSI")
    private String IMSI;

    @ExcelProperty(value = "启用状态", converter = EasyExcelEnumConverter.class)
    private TerminalIsactiveEnum isActive;

    @ExcelProperty("失效时间")
    private String deactivatedAt;

    @ExcelProperty("合计费用")
    private BigDecimal simCosts;

    @ExcelProperty("进/远景照")
    private String imgNum;

    @ExcelProperty("安装类型")
    private String installType;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("安装人员")
    private String userNames;

    @ExcelIgnore
    private Long parentTerminalId;

    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private Integer level;
}
