package com.joinus.io.executor.ijxadmin.terminal.model;

import com.joinus.io.executor.model.BaseParamValid;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class TerminalExportParam extends BaseParamValid {


    private Long id;
    /*
     * 学校id
     */
    private Long schoolId;
    /*
     * 终端编号
     */
    private String terminalNum;
    /*
     * SIM卡号
     */
    private String simNum;
    /*
     * 终端类型
     */
    private Integer terminalType;
    /*
     * 功能分类
     */
    private String typeName;

    @Schema(title = "终端使用状态 0 1 2 3 4")
    private String terminalStatus;
    /*
     * 区域类型
     */
    private Integer regionType;
    /*
     * 区域
     */
    private String region;
    /*
     * 学校业务标识
     */
    private String businessFlag;
    /*
     * 终端名称
     */
    private String terminalName;
    /*
     * 终端启用状态
     */
    private Integer isActive;
    /*
     * SIM卡状态
     */
    private String simStatus;
    /*
     * SIM卡维护状态
     */
    private String oprcode;
    /*
     * 终端是否在线
     */
    private Integer isLogin;
    /*
     * 白名单数量通告标志
     */
    private Integer whiteNumFlag;
    /*
     * 安装开始时间
     */
    private Date beginInstallDate;
    /*
     * 安装结束时间
     */
    private Date endInstallDate;
    /*
     * 最后在线开始时间
     */
    private Date beginDate;
    /*
     * 最后在线结束时间
     */
    private Date endDate;
    /*
     * 安装人员
     */
    private String installP;
    /*
     * 软件版本号
     */
    private String softVersion;
    /*
     * IMEI
     */
    private String imei;

    private Integer isOverdrawn;

    private String sn;


}
