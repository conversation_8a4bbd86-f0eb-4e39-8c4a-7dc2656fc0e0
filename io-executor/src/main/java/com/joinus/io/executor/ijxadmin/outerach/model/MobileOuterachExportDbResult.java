package com.joinus.io.executor.ijxadmin.outerach.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.joinus.io.executor.model.BaseDbResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MobileOuterachExportDbResult extends BaseDbResult {

    private Long id ;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date outreachDate;

    private String organizationId;

    private String organizationName;

    private String visitPeople;

    private String workReport;

    private String customerDemand;

    private String businessOpportunities;

    private String cityManagerFeedback;

    private String issues;

    private String growthOpportunities;


    private Long visitUserId;


    private String visitUserName;

    private String visitUserPhone;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
