package com.joinus.io.executor.ijxadmin.appuseinfo.model;

import com.joinus.io.executor.model.BaseParamValid;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class AppUseInfoExportParam extends BaseParamValid {

    /*
     * 学校id
     */
    private Long schoolId;

    /*
     * 区域id
     */
    private String regionId;

    /*
     * 角色 0教师 1家长
     */
    private String userType;

    /*
     * 是否首次登录
     */
    private Long firstLoginFlag;

    /*
     * 开始时间
     */
    private String startTime;

    /*
     * 结束时间
     */
    private String endTime;


    /*
     *  app类型
     */
    private String appType;

    /*
     * 版本
     */
    private String version;


    /*
     *  登录类型
     */
    private Integer loginType;

    /*
     * 电话
     */
    private String telNum;





    /*
     * 页码
     */
    private int pageNo;


    /**
     * 每页条数
     */
    private int pageSize;


}
