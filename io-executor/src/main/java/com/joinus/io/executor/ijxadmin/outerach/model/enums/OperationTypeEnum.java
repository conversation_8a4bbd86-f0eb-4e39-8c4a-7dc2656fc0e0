package com.joinus.io.executor.ijxadmin.outerach.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> anpy
 * @create 2024/4/8 14:51
 */
@Getter
@AllArgsConstructor
public enum OperationTypeEnum {

    PREREPAIR(1, "报修"),
    REPAIR(2, "维修"),
    REMOVE(3, "拆机"),
    ADDINSTALL(4, "加装"),
    ADDINSTALLAPPLY(5, "加装申请");

    String des;
    Integer code;

    OperationTypeEnum(Integer code, String des) {
        this.code = code;
        this.des = des;
    }
}
