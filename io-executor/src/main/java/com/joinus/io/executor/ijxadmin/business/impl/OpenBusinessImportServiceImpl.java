package com.joinus.io.executor.ijxadmin.business.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.io.executor.enums.BusinessApplyOpreateEnum;
import com.joinus.io.executor.enums.BusinessApplyResEnum;
import com.joinus.io.executor.enums.BusinessApplyStepEnum;
import com.joinus.io.executor.ijxadmin.business.model.OpenBusinessApply;
import com.joinus.io.executor.ijxadmin.business.model.OpenBusinessImportDTO;
import com.joinus.io.executor.ijxadmin.business.model.ProductSchool;
import com.joinus.io.executor.ijxadmin.student.model.QinPhoneDbResult;
import com.joinus.io.executor.kafka.KafkaProducer;
import com.joinus.io.executor.model.BaseImportCommonParam;
import com.joinus.io.executor.model.PoiImportDetail;
import com.joinus.io.executor.model.entity.*;
import com.joinus.io.executor.param.PoiTask;
import com.joinus.io.executor.service.*;
import com.joinus.io.executor.utils.ParamCheckUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service("openBusinessImportServiceImpl")
@AllArgsConstructor
@Slf4j
public class OpenBusinessImportServiceImpl extends AbstractImportBizService {


    @Resource
    private ISchoolService schoolService;
    @Resource
    private IGradeService gradeService;
    @Resource
    private IKlassService klassService;
    @Resource
    private IStudentService studentService;
    @Resource
    private IStudentParentService studentParentService;
    @Resource
    private KafkaProducer kafkaProducer;
    @Resource
    private IBusinessService businessService;

    @Resource
    private IBusinessApplyService businessApplyService;

    @Resource
    private IBusinessSelfService businessSelfService;
    /**
     * 河北业务ijx平台套餐标识 --包月
     */
    public static final String HEBEI_BUSINESS_CODE_MONTH = "JI";
    /**
     * 河北业务ijx平台套餐标识 --包年
     */
    public static final String HEBEI_BUSINESS_CODE_YEAR = "JY";


    @Override
    public BaseImportCommonParam buildCommonParam(PoiTask task, List<PoiImportDetail> poiImportDetailList) {

        return BaseImportCommonParam.builder()
               .build();
    }

    @Override
    public String dealImportDetail(PoiTask poiTask, PoiImportDetail importDetail) {

        log.info("task info {}", JSONUtil.toJsonStr(poiTask));
        log.info("importDetail info {}", JSONUtil.toJsonStr(importDetail));

        OpenBusinessImportDTO openBusinessImportDTO = JSONUtil.toBean(importDetail.getContent(),
                OpenBusinessImportDTO.class);
        //属性合法性校验
        String invalidMsg = ParamCheckUtil.checkParam(openBusinessImportDTO);
        if (StrUtil.isNotBlank(invalidMsg)) {
            return invalidMsg;
        }
        JSONObject jsonObject = JSONUtil.parseObj(poiTask.getQueryParams());
        Long schoolId = jsonObject.getLong("schoolId");

        if (null == schoolId) {
            return "学校Id不能为空";
        }
        String productSchools  =jsonObject.getStr("productSchools");
        List<ProductSchool> productSchoolList = JSONUtil.toList(productSchools, ProductSchool.class);
        if (CollUtil.isEmpty(productSchoolList)) {
            return "学校开通套餐信息不能为空";
        }
        List<School> schoolList = schoolService.listById(schoolId);
        if (CollUtil.isEmpty(schoolList)) {
            return "查询不到学校信息";
        }
         School school=schoolList.get(0);
        if (StrUtil.isBlank(school.getGroupid())) {
            return "学校集团id为空";
        }

        List<Grade> gradeList = gradeService.getGradeBySchoolIdAndGradeName(schoolId, openBusinessImportDTO.getGradeName());
        if (CollUtil.isEmpty(gradeList)) {
            return "没有此年级年级信息";
        }

        Long gradeId = gradeList.get(0).getId();
        Klass klass = klassService.getKlassBySchoolIdAndGradeIdAndClassName(schoolId, gradeId,openBusinessImportDTO.getClassName());
        if (ObjectUtil.isEmpty(klass)) {
            return "没有此班级信息";
        }
        List<Student> validStudentList = null;
        List<Student> studentList = studentService.selectStudentsBySchoolIdAndGradeIdAndStudentName(schoolId, gradeId, openBusinessImportDTO.getStudentName());
        if (CollUtil.isNotEmpty(studentList)) {
            validStudentList = studentList.stream().filter(student -> klass.getId().equals(student.getClassId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(validStudentList)) {
                return "未找到该学生";
            }
        } else {
            return "未找到该学生";
        }
        Student student = null;
        for (Student stu : validStudentList) {
            List<QinPhoneDbResult> qinPhoneDbResultList = studentParentService.getQinPhoneDbResultByStudentId(stu.getId());
            if (qinPhoneDbResultList.stream()
                    .anyMatch(result -> result.getTelNum().contains(openBusinessImportDTO.getParentTelNum()))) {
                student=stu;
                break;
            }
        }
        if (null == student) {
            return validStudentList.size() == 1 ? "该学生没有此亲情号" : "未找到有该亲情号的此学生";
        }
        ProductSchool productSchool;
        if (productSchoolList.size() > 1) {
            List<ProductSchool> productList = productSchoolList.stream().filter(product -> gradeId.equals(product.getGradeId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(productList)) {
                return gradeList.get(0).getGradeName() + "未选择套餐";
            }
            productSchool = productList.get(0);
        }else {
            productSchool = productSchoolList.get(0);
        }

        OpenBusinessApply businessApply = OpenBusinessApply
                .builder()
                .managerTelnum(poiTask.getOperatorMobile())
                .schoolId(schoolId)
                .telnum(openBusinessImportDTO.getParentTelNum())
                .sncode(openBusinessImportDTO.getSnCode())
                .studentId(student.getId())
                .productId(productSchool.getProductId())
                .productCode(productSchool.getProductCode())
                .build();

        //如果是河北业务 直接调用 原来kafka
        if (businessApply.getProductCode().equals(HEBEI_BUSINESS_CODE_MONTH) || businessApply.getProductCode().equals(HEBEI_BUSINESS_CODE_YEAR)) {
            kafkaProducer.sendOpenBusiness(JSONUtil.toJsonStr(businessApply));
        } else {
            //河南开通业务
            String errMsg = heNanOpenBusiness(businessApply,student);
            if(StrUtil.isNotBlank(errMsg)){
                return errMsg;
            }
        }
        return null;
    }

    private String heNanOpenBusiness(OpenBusinessApply apply,Student student) {
        Business business = businessService.getBusinessByStudentIdAndtelNum(apply.getStudentId(), apply.getTelnum());

        if (ObjectUtil.isEmpty(business)) {
            return "该学生已免业务";
        }
        /**
         *  2024-10-12 16:11:01 by wucaiyun  开通ungo 和self 业务学生不进行开通其他业务
         */
        List<Integer> statusList = Arrays.asList(1,4,6);
        if (null != business.getStatus() && statusList.contains(business.getStatus())) {
            log.info("telNum=" + apply.getTelnum() + ", flag=" + business.getFlag() + "业务办理已开通，不能业务开通！");
            return "该手机号码已经对该学生开通业务，请勿重复开通";
        }
        List<BusinessApply> bizDoingList = businessApplyService.isBizDoingAll(apply.getSchoolId(), apply.getTelnum(), business.getFlag());
        if (CollUtil.isNotEmpty(bizDoingList)) {
            return "该学生业务正在开通中";
        }

        List<Integer> typeList = Arrays.asList(0,3);
        List<BusinessSelf> resultList = businessSelfService.getBusinessSelfBySudentIdAndPhoneAndTypeList(apply.getStudentId(), apply.getTelnum(), typeList);
        if (CollUtil.isNotEmpty(resultList)) {
            log.info("telNum=" + apply.getTelnum() + ", flag=" + business.getFlag() + "业务办理已开通，不能业务开通！");
            return "该手机号码已经对该学生开通自推广业务，请勿重复开通";
        }
        BusinessApply bizApply = new BusinessApply();
        bizApply.setFlag(business.getFlag());
        bizApply.setGroupId(business.getGroupId());
        bizApply.setGroupUpdateTime(business.getGroupUpdateTime());
        bizApply.setManagerTelnum(apply.getManagerTelnum());
        bizApply.setOperate(BusinessApplyOpreateEnum.OPEN.getCode());//开通业务
        bizApply.setOperateTime(new Date());
        bizApply.setParentId(business.getParentId());
        bizApply.setProductCode(apply.getProductCode());
        bizApply.setProductId(apply.getProductId().intValue());
        bizApply.setRes(BusinessApplyResEnum.RES_NOT_PROCESS.getCode());//未处理
        bizApply.setSchoolId(apply.getSchoolId());
        bizApply.setSncode(apply.getSncode());
        bizApply.setStep(BusinessApplyStepEnum.STEP_1.getCode());
        bizApply.setStudentId(apply.getStudentId());
        bizApply.setTelnum(business.getTelNum());

        bizApply.setStudentId(apply.getStudentId());
        bizApply.setTelnum(apply.getTelnum());
        if (bizApply.getProductCode().startsWith("Z")) {
            if (StrUtil.isBlank(student.getTelNum())) {
                return "手机号为空,无法开通";
            }
            bizApply.setBatch(student.getTelNum());
            bizApply.setSourcesOfData(10);
        } else {
            bizApply.setSourcesOfData(1);
        }
        businessApplyService.save(bizApply);
        return null;
    }

}
