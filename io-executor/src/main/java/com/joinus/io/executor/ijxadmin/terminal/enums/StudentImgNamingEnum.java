package com.joinus.io.executor.ijxadmin.terminal.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
/**
 *
 * @return 
 * @description 学生图片导出命名方式
 * <AUTHOR>
 * @date 2022/11/21 17:45
 */
public enum StudentImgNamingEnum {

    /*
     * 学生编号不存在时，以学生姓名代替
     */
    MAKE_CARD(1, "学生编号"),
    STUDENT_NAME(2, "学生姓名"),
    STUDENT_ID(3, "学生id");

    @EnumValue
    @Getter
    private final int code;

    private final String label;

}
