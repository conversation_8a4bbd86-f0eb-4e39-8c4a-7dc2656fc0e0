package com.joinus.io.executor.ijxadmin.outerach.model;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.joinus.dao.outreach.OutreachTerminalOperationEntity;
import com.joinus.dao.outreach.OutreachTerminalStatusEntity;
import com.joinus.io.executor.model.BaseDbResult;
import com.joinus.io.executor.model.BasePoiResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> mawh
 * @create 2024-4-9 08:57:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class OutreachSchoolPoiResult extends BasePoiResult {

    private Long id;

    @DateTimeFormat("yyyy-MM-dd")
    private Date outreachDate;

    private String organizationId;

    private String organizationName;

    private Integer schoolType;

    private Integer hasAccessTermianl;

    private Integer hasTelephoneTermianl;

    private Integer hasOtherTermianl;

    private Integer hasRemoveTerminal;

    private Integer hasAddInstallTerminal;

    private Integer hasAddInstallApply;

    private Integer hasGuardIssues;

    private Integer metPrincipal;


    private String visitPeople;

    private String schoolDemand;

    private String issues;

    private String growthOpportunities;

    private Long visitUserId;

    private String visitUserName;

    private String visitUserPhone;

    private Date createTime;

    private String  accessTermianl;

    private String  telephoneTermianl;

    private String  preRepairTermianl;

    private String  repairTermianl;

    private String removeTerminal;

    private String addInstallTerminal;

    private String addInstallApplyTerminal;

    private String faceFunction;

    private String messageFunction;

    private String guardIssues;

    private List<OutreachTerminalStatusEntity> outreachTerminalStatusParamList;


    private List<OutreachTerminalOperationEntity> outreachTerminaOperationParamList;

    private List<OutreachTerminalStatusEntity> outreachTerminalStatusEntityList;

    private List<OutreachTerminalOperationEntity> outreachTerminalOperationEntityList;

    private String visitType;



}
