package com.joinus.io.executor.ijxadmin.adjustclass.model;

import cn.hutool.core.annotation.Alias;
import com.alibaba.excel.annotation.ExcelProperty;
import com.joinus.io.executor.model.BaseParamValid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchAdjustClassImportDTO extends BaseParamValid {

    private static final String EMPTY_STRING = "(^$)";

    @Alias("学校名称")
    @NotEmpty(message = "学校名称不能为空")
    private String schoolName;

    @Alias("年级名称")
    @NotEmpty(message = "年级名称不能为空")
    private String gradeName;

    @Alias("新班级名称")
    @NotEmpty(message = "新班级名称不能为空")
    private String newClassName;

    @Alias("旧班级名称")
    private String oldClassName;

    @Alias("学生姓名")
    @NotEmpty(message = "学生姓名不能为空")
    private String studentName;

    @Alias("错误原因")
    private String errorMsg;

}
