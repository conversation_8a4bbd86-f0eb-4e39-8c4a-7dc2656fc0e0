package com.joinus.io.executor.ijxadmin.adjustclass.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.io.executor.enums.ActiveEnum;
import com.joinus.io.executor.ijxadmin.adjustclass.model.BatchAdjustClassCommonParam;
import com.joinus.io.executor.ijxadmin.adjustclass.model.BatchAdjustClassImportDTO;
import com.joinus.io.executor.model.PoiImportDetail;
import com.joinus.io.executor.model.SysLog;
import com.joinus.io.executor.model.entity.*;
import com.joinus.io.executor.param.PoiTask;
import com.joinus.io.executor.service.*;
import com.joinus.io.executor.utils.ParamCheckUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service("batchAdjustClassImportServiceImpl")
@AllArgsConstructor
@Slf4j
public class BatchAdjustClassImportServiceImpl extends AbstractImportBizService {
    @Resource
    private ISchoolService schoolService;
    @Resource
    private IGradeService gradeService;
    @Resource
    private IKlassService klassService;
    @Resource
    private IStudentService studentService;
    @Resource
    private IBusinessSelfService businessSelfService;

    @Resource
    private ISysLogService sysLogService;

    @Override
    public BatchAdjustClassCommonParam buildCommonParam(PoiTask task, List<PoiImportDetail> poiImportDetailList) {
        List<BatchAdjustClassImportDTO> batchAdjustClassImportDTOs = new ArrayList<>();
        poiImportDetailList.stream().map(PoiImportDetail::getContent)
                .forEach(content -> {
                    BatchAdjustClassImportDTO batchAdjustClassImportDTO = JSONUtil.toBean((String) content,
                            BatchAdjustClassImportDTO.class);
                    batchAdjustClassImportDTOs.add(batchAdjustClassImportDTO);
                });
        //学校列表
        List<String> schoolNameList =
                batchAdjustClassImportDTOs.stream()
                        .map(BatchAdjustClassImportDTO::getSchoolName)
                        .distinct()
                        .collect(Collectors.toList());
        List<School> schoolList = schoolService.listBySchoolNames(schoolNameList);
        List<Long> schoolIdList = schoolList.stream()
                .map(School::getId)
                .collect(Collectors.toList());
        //年级列表
        List<Grade> gradeList = new ArrayList<>();
        //班级列表
        List<Klass> klassList = new ArrayList<>();
        if (CollUtil.isNotEmpty(schoolIdList)) {
            gradeList = gradeService.listBySchoolIds(schoolIdList);
            klassList = klassService.listBySchoolIds(schoolIdList);
        }


       return  BatchAdjustClassCommonParam.builder()
                 .schoolList(schoolList)
                .gradeList(gradeList)
                .klassList(klassList)
                .build();
    }

    @Override
    public String dealImportDetail(PoiTask poiTask, PoiImportDetail importDetail) {
        log.info("task info {}", JSONUtil.toJsonStr(poiTask));
        log.info("importDetail info {}", JSONUtil.toJsonStr(importDetail));
        BatchAdjustClassImportDTO batchAdjustClassImportDTO = JSONUtil.toBean(importDetail.getContent(),
                BatchAdjustClassImportDTO.class);
        //属性合法性校验
        String invalidMsg = ParamCheckUtil.checkParam(batchAdjustClassImportDTO);
        if (StrUtil.isNotBlank(invalidMsg)) {
            return invalidMsg;
        }
        BatchAdjustClassCommonParam commonParam = this.buildCommonParam(poiTask, List.of(importDetail));
        //1 学校、年级、班级校验
        List<School> schoolList = commonParam.getSchoolList().stream()
                .filter(school -> school.getSchoolName().equals(batchAdjustClassImportDTO.getSchoolName()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(schoolList)) {
            return "对不起查询不到学校信息";
        }
        Long schoolId = schoolList.get(0).getId();

        List<Grade> gradeList = commonParam.getGradeList().stream()
                .filter(grade -> grade.getGradeName().equals(batchAdjustClassImportDTO.getGradeName()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(gradeList) || gradeList.size() > 1) {
            return "对不起查询不到年级信息";
        }
        Long gradeId=gradeList.get(0).getId();
        List<Student> listStudent = new ArrayList<>();
        if(StrUtil.isNotBlank(batchAdjustClassImportDTO.getOldClassName())) {
            List<Klass> oldKlassList = commonParam.getKlassList().stream()
                    .filter(klass -> klass.getClassName().equals(batchAdjustClassImportDTO.getOldClassName()) && gradeId.equals(klass.getGradeId()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(oldKlassList)) {
                return "对不起查询不到旧班级信息";
            }

            Long classId = oldKlassList.get(0).getId();
            Long id = klassService.getClassIdByParameters(schoolId, gradeId, classId);
            if (ObjectUtil.isEmpty(id)) {
                return "对不起查询不到旧班级信息!";
            }
            listStudent = studentService.selectStudentsByClassIdAndStudentName(id, batchAdjustClassImportDTO.getStudentName());
        }else{
            listStudent=studentService.selectStudentsBySchoolIdAndGradeIdAndStudentName(schoolId,gradeId,batchAdjustClassImportDTO.getStudentName());
        }

        if (listStudent.isEmpty()) {
            return "对不起查询不到学生信息!";
        }else if(listStudent.size() == 1){
            List<Klass> newKlassList = commonParam.getKlassList().stream()
                    .filter(klass -> klass.getClassName().equals(batchAdjustClassImportDTO.getNewClassName()) & gradeId.equals(klass.getGradeId()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(newKlassList)) {
                return "对不起查询不到新班级信息";
            }
            Student student = new Student();
            student.setClassId(newKlassList.get(0).getId());
            student.setId(listStudent.get(0).getId());
            student.setUpdateTime(new Date());
            //进行调班
            studentService.updateById(student);
            //查询学生是否开通自推广业务，如果开通修改自推广业务中的classId和gradeId
            List<BusinessSelf> businessSelfList = businessSelfService.getBusinessSelfByStudentId(student.getId());
            if (CollUtil.isNotEmpty(businessSelfList)) {
                businessSelfList.forEach(businessSelf -> {
                    BusinessSelf updateBusinessSelf = new BusinessSelf();
                    updateBusinessSelf.setClassId(student.getClassId());
                    updateBusinessSelf.setId(businessSelf.getId());
                    updateBusinessSelf.setGradeId(newKlassList.get(0).getGradeId());
                    businessSelfService.updateById(updateBusinessSelf);
                });
            }
            // 加入日志信息
            SysLog sysLog = SysLog.builder()
                    .operator(poiTask.getOperatorName())
                    .logType(2)
                    .isactive(ActiveEnum.VALID)
                    .logDesc("管理员" + poiTask.getOperatorName() + "修改学生" + batchAdjustClassImportDTO.getStudentName()+"到新班级" + student.getClassId())
                    .content("管理员" + poiTask.getOperatorName() + "修改学生" + batchAdjustClassImportDTO.getStudentName() +"到新班级" + student.getClassId())
                    .operator(poiTask.getOperatorName())
                    .logTime(DateUtil.date().toJdkDate())
                    .build();
            sysLogService.saveSysLog(sysLog);
        }else{
            return "对不起发现重名的学生信息!";
        }
        return null;
    }
}
