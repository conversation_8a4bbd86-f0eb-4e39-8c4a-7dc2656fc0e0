package com.joinus.io.executor.ijxadmin.adjustclass.model;

import com.joinus.io.executor.model.BaseImportCommonParam;
import com.joinus.io.executor.model.entity.Grade;
import com.joinus.io.executor.model.entity.Klass;
import com.joinus.io.executor.model.entity.School;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchAdjustClassCommonParam extends BaseImportCommonParam {

    private List<Grade> gradeList;

    private List<Klass> klassList;

    private List<School> schoolList;


}
