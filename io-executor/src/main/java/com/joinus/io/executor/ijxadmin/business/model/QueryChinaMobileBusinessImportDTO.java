package com.joinus.io.executor.ijxadmin.business.model;

import cn.hutool.core.annotation.Alias;
import com.joinus.io.executor.model.BaseParamValid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryChinaMobileBusinessImportDTO extends BaseParamValid {

    @Alias("flag")
    @NotEmpty(message = "flag不能为空")
    private String flag;

    @Alias("集团ID")
    @NotEmpty(message = "集团ID不能为空")
    private String groupId;

    @Alias("手机号")
    @NotEmpty(message = "手机号号不能为空")
    private String telNum;





}
