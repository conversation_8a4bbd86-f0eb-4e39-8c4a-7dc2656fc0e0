package com.joinus.io.executor.ijxadmin.terminal.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class TerminalExportExtraParam extends TerminalExportParam{

    private List<String> simStatusList;

    private List<Integer> terminalStatusList;

    private List<Integer> businessFlagList;

    private String regionProvince;

    private String regionCity;

    private String regionCounty;

    private Long userId;

    private String regionId;

    private String regionAnd;
}
