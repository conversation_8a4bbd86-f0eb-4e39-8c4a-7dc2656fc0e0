package com.joinus.io.executor.ijxadmin.terminal.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.poi.ss.usermodel.IndexedColors;


@Getter
@AllArgsConstructor
public enum ExcelLevelColorEnum {
    LEVEL_1(0,"A", IndexedColors.WHITE.getIndex()),
    LEVEL_2(1,"AA", IndexedColors.LIGHT_YELLOW.getIndex()),
    LEVEL_3(2,"AAA", IndexedColors.LIGHT_TURQUOISE.getIndex()),
    LEVEL_4(3,"AAAA", IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex()),
    LEVEL_5(4,"AAAAA", IndexedColors.GREY_25_PERCENT.getIndex());

    private int level;

    private String levelDesc;

    private short color;

    public static short getColorByLevelDesc(String levelDesc){

        for(ExcelLevelColorEnum enumType : ExcelLevelColorEnum.values()){
            if(enumType.levelDesc.equals(levelDesc)){
                return enumType.color;
            }
        }
        return IndexedColors.LIGHT_BLUE.index;
    }

    public static String getLevelDescByLevel(int level){

        for(ExcelLevelColorEnum enumType : ExcelLevelColorEnum.values()){
            if(enumType.level == level){
                return enumType.levelDesc;
            }
        }
        return LEVEL_1.levelDesc;
    }

}
