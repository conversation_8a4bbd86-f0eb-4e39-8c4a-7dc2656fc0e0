package com.joinus.io.executor.ijxadmin.business.model;

import cn.hutool.core.annotation.Alias;
import com.joinus.io.executor.model.BaseParamValid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotEmpty;

@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OpenBusinessImportDTO extends BaseParamValid {

    @Alias("年级名称")
    @NotEmpty(message = "年级不能为空")
    private String gradeName;

    @Alias("班级名称")
    @NotEmpty(message = "班级不能为空")
    private String className;

    @Alias("学生姓名")
    @NotEmpty(message = "学生姓名不能为空")
    private String studentName;

    @Alias("家长手机号")
    @NotEmpty(message = "家长手机号不能为空")
    private String parentTelNum;

    @Alias("验证码")
    @NotEmpty(message = "验证码不能为空")
    private String snCode;



}
