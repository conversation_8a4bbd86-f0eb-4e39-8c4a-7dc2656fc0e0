package com.joinus.io.executor.ijxadmin.callrecords.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.joinus.io.executor.model.BasePoiResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CallRecordsExportPoiResult extends BasePoiResult {

    @ExcelProperty("地市")
    private String superRegionName;

    @ExcelProperty("县区")
    private String regionName;

    @ExcelProperty("学校名称")
    private String schoolName;

    @ExcelProperty("年级")
    private String gradeName;

    @ExcelProperty("班级")
    private String className;

    @ExcelProperty("学生姓名")
    private String studentName;

    @ExcelProperty("卡号")
    private String cardCode;


    @ExcelProperty("终端编号")
    private String terminalNum;

    @ExcelProperty("终端名称")
    private String terminalName;

    @ExcelProperty("sim卡号")
    private String simNum;


    @ExcelProperty("设备安装位置")
    private String installAddress;

    private Date startTime;

    @ExcelProperty("通话时间")
    private String callTime;

    @ExcelProperty("拨打电话")
    private String phone;


    @ExcelProperty("通话时长")
    private String talkLength;

    @ExcelProperty("接听类型")
    private String answerState;

    @ExcelProperty("计费方式")
    private String chargeType;


    @ExcelProperty("通话类型")
    private String callType;

    @ExcelProperty("等待时长")
    private String waitSeconds;


    @ExcelProperty("家长是否登录APP")
    private String isLoginApp;


}
