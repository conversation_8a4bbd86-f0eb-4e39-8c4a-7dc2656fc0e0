package com.joinus.io.executor.ijxadmin.outerach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.joinus.dao.outreach.OutreachTerminalOperationEntity;
import com.joinus.dao.outreach.OutreachTerminalStatusEntity;
import com.joinus.io.executor.ijxadmin.outerach.model.MobileOuterachExportPoiResult;
import com.joinus.io.executor.ijxadmin.outerach.model.OuterachExportParam;
import com.joinus.io.executor.ijxadmin.outerach.model.OutreachSchoolPoiResult;
import com.joinus.io.executor.ijxadmin.outerach.model.OutreachTerminalSnapshotPoiResult;
import com.joinus.io.executor.ijxadmin.outerach.model.enums.HasEnum;
import com.joinus.io.executor.ijxadmin.outerach.model.enums.OperationTypeEnum;
import com.joinus.io.executor.ijxadmin.outerach.model.enums.StatusEnum;
import com.joinus.io.executor.ijxadmin.outerach.model.enums.TerminalTypeEnum;
import com.joinus.io.executor.model.BaseDbResult;
import com.joinus.io.executor.model.BaseParamValid;
import com.joinus.io.executor.model.BasePoiResult;
import com.joinus.io.executor.model.outreach.OutreachSchoolRecordResult;
import com.joinus.io.executor.model.outreach.OutreachTerminalSnapshotResult;
import com.joinus.io.executor.model.outreach.TerminalFunction;
import com.joinus.io.executor.param.PoiTask;
import com.joinus.io.executor.param.TaskQueryParam;
import com.joinus.io.executor.service.*;
import com.joinus.io.executor.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;


@Service("schoolOuterachExportServiceImpl")
@AllArgsConstructor
@Slf4j
public class SchoolOuterachExportServiceImpl extends AbstractExportBizService {

      @Autowired
      private IOutreachSchoolRecordService outreachSchoolRecordService;
      @Autowired
      private IOutreachTerminalStatusService outreachTerminalStatusService;
      @Autowired
      private IOutreachTerminalOperationService outreachTerminalOperationService;


    @Override
    public String checkParam(BaseParamValid task, Long count) {
        OuterachExportParam outerachExportParam = (OuterachExportParam) task;
        Date startTime = outerachExportParam.getOutreachStartDate();
        Date endTime = outerachExportParam.getOutreachEndDate();
        // 计算两个时间之间的差，单位：天
        long days = DateUtil.between(startTime, endTime, DateUnit.DAY);
        if (days > 31) {
            return "学校走访记录最多导出31天";
        }

        return null;
    }

    @Override
    public Long queryResultCount(BaseParamValid task) {
        return 0L;
    }

    @Override
    public List<OutreachSchoolRecordResult> listBiz(BaseParamValid task, String exportHeaders) {
         OuterachExportParam outerachExportParam = (OuterachExportParam) task;
        return outreachSchoolRecordService.getSchoolOutreachRecord(outerachExportParam);
    }


    @Override
    public List<OutreachSchoolPoiResult> beautyResult(List<? extends BaseDbResult> boList) {

        List<OutreachSchoolRecordResult> resultList = BeanUtil.copyToList(boList, OutreachSchoolRecordResult.class);
        List<Long> outreachId = resultList.stream().map(OutreachSchoolRecordResult::getId).collect(Collectors.toList());
        Map<Long, List<OutreachTerminalStatusEntity>> statusMap = new HashMap<>();
        Map<Long, List<OutreachTerminalOperationEntity>> operationMap = new HashMap<>();
        List<List<Long>> splitList = CollUtil.split(outreachId, 1000);
        for (List<Long> subList : splitList) {
            List<OutreachTerminalStatusEntity> statusList = outreachTerminalStatusService.getOutreachTerminalStatusByOutreachId(subList);
            statusMap.putAll(statusList.stream().collect(Collectors.groupingBy(OutreachTerminalStatusEntity::getOutreachId)));
            List<OutreachTerminalOperationEntity> operationList = outreachTerminalOperationService.getOutreachTerminalOperationByOutreachId(subList);
            operationMap.putAll(operationList.stream().collect(Collectors.groupingBy(OutreachTerminalOperationEntity::getOutreachId)));
        }
        for (OutreachSchoolRecordResult outreachSchoolRecordResult : resultList) {
            List<OutreachTerminalStatusEntity> outreachTerminalStatusEntityList = statusMap.get(outreachSchoolRecordResult.getId());
            List<OutreachTerminalOperationEntity> outreachTerminalOperationEntityList = operationMap.get(outreachSchoolRecordResult.getId());
            if (HasEnum.YES.getCode().equals(outreachSchoolRecordResult.getHasAccessTermianl())) {
                List<OutreachTerminalStatusEntity> accessTerminalList = outreachTerminalStatusEntityList.stream()
                        .filter(entity -> entity.getTerminalType().equals(TerminalTypeEnum.ACCESS.getCode()))
                        .collect(Collectors.toList());
                OutreachTerminalStatusEntity accessTerminal = accessTerminalList.get(0);
                TerminalFunction terminalFunction = JSONUtil.toBean(accessTerminal.getOtherFunction(), TerminalFunction.class);
                if ((HasEnum.YES.getCode().equals(outreachSchoolRecordResult.getHasGuardIssues()))) {
                    outreachSchoolRecordResult.setGuardIssues(ObjectUtil.isNotEmpty(accessTerminal.getRemarks()) ? accessTerminal.getRemarks() : "有问题");
                }
                outreachSchoolRecordResult.setFaceFunction(StatusEnum.getDesByCode(accessTerminal.getStatus()));
                outreachSchoolRecordResult.setMessageFunction(StatusEnum.getDesByCode(terminalFunction.getStatus()));
            }

            if (HasEnum.YES.getCode().equals(outreachSchoolRecordResult.getHasTelephoneTermianl())) {
                List<OutreachTerminalStatusEntity> telephoneTerminalList = outreachTerminalStatusEntityList.stream()
                        .filter(entity -> entity.getTerminalType().equals(TerminalTypeEnum.TELEPHONE.getCode()))
                        .collect(Collectors.toList());
                OutreachTerminalStatusEntity telephoneTerminal = telephoneTerminalList.get(0);

                outreachSchoolRecordResult.setTelephoneTermianl(StatusEnum.getPhoneDesByCode(telephoneTerminal.getStatus()));

            }

            outreachSchoolRecordResult.setOutreachTerminalStatusEntityList(outreachTerminalStatusEntityList);
            outreachSchoolRecordResult.setOutreachTerminalOperationEntityList(outreachTerminalOperationEntityList);

            if (HasEnum.YES.getCode().equals(outreachSchoolRecordResult.getHasRemoveTerminal())) {
                List<OutreachTerminalOperationEntity> removeList = outreachTerminalOperationEntityList.stream()
                        .filter(entity -> entity.getOperationType().equals(OperationTypeEnum.REMOVE.getCode()) && entity.getStatus().equals(StatusEnum.NORMAL.getCode()))
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(removeList)) {
                    List<String> terminalNameList = new ArrayList<>();
                    removeList.forEach(outreachTerminalOperationEntity -> {
                        terminalNameList.add(TerminalTypeEnum.getDesByCode(outreachTerminalOperationEntity.getTerminalType()));

                    });
                    outreachSchoolRecordResult.setRemoveTerminal(String.join("、", terminalNameList));
                }
            }

            if (HasEnum.YES.getCode().equals(outreachSchoolRecordResult.getHasAddInstallTerminal())) {
                List<OutreachTerminalOperationEntity> addInstallList = outreachTerminalOperationEntityList.stream()
                        .filter(entity -> entity.getOperationType().equals(OperationTypeEnum.ADDINSTALL.getCode()) && entity.getStatus().equals(StatusEnum.NORMAL.getCode()))
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(addInstallList)) {
                    List<String> terminalNameList = new ArrayList<>();
                    addInstallList.forEach(outreachTerminalOperationEntity -> {
                        terminalNameList.add(TerminalTypeEnum.getDesByCode(outreachTerminalOperationEntity.getTerminalType()));
                    });
                    outreachSchoolRecordResult.setAddInstallTerminal(String.join("、", terminalNameList));
                }
            }

            if (HasEnum.YES.getCode().equals(outreachSchoolRecordResult.getHasAddInstallApply())) {
                List<OutreachTerminalOperationEntity> addInstallList = outreachTerminalOperationEntityList.stream()
                        .filter(entity -> entity.getOperationType().equals(OperationTypeEnum.ADDINSTALLAPPLY.getCode()) && entity.getStatus().equals(StatusEnum.NORMAL.getCode()))
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(addInstallList)) {
                    List<String> terminalNameList = new ArrayList<>();
                    addInstallList.forEach(outreachTerminalOperationEntity -> {
                        terminalNameList.add(TerminalTypeEnum.getDesByCode(outreachTerminalOperationEntity.getTerminalType()));
                    });
                    outreachSchoolRecordResult.setAddInstallApplyTerminal(String.join("、", terminalNameList));
                }
            }
        }

        return BeanUtil.copyToList(resultList, OutreachSchoolPoiResult.class);
    }

    @Override
    public ByteArrayInputStream buildPoiStream(BaseParamValid task, String fileName,
                                               String exportHeaders, int pageSize, long totalCount) {
        //根据业务参数查询需要导出的业务数据列表
        List<? extends BaseDbResult> baseDbResultList = listBiz(task, exportHeaders);

        //生成表头
        List<List<String>> head = createHead(baseDbResultList);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        // 创建Sheet实例
        WriteSheet writeSheet = EasyExcel.writerSheet("学校走访").head(head)
                .registerWriteHandler(EasyExcelUtil.cellBorder())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
        WriteSheet writeSheet1 = EasyExcel.writerSheet("学校走访检测记录")
                 .registerWriteHandler(EasyExcelUtil.cellBorder())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
        ExcelWriter excelWriter = EasyExcel.write(out, OutreachTerminalSnapshotPoiResult.class).build();

        //对结果列表各字段进行对应业务处理，形成可视化字段
        List<? extends BasePoiResult> poiResultList = beautyResult(baseDbResultList);
        List<? extends BasePoiResult> resultList = buildDetetionResult(baseDbResultList,task);
        List<List<Object>> data = new ArrayList<>();
        buildResult(poiResultList, data);
        excelWriter.write(data, writeSheet);
        excelWriter.write(resultList, writeSheet1);
        excelWriter.finish();
        return new ByteArrayInputStream(out.toByteArray());
    }


    public List<OutreachTerminalSnapshotPoiResult> buildDetetionResult(List<? extends BaseDbResult> boList,BaseParamValid task) {
        List<OutreachTerminalSnapshotResult> results= new ArrayList<>();
        List<OutreachSchoolRecordResult> resultList = BeanUtil.copyToList(boList, OutreachSchoolRecordResult.class);
        OuterachExportParam outerachExportParam = (OuterachExportParam) task;
       // 使用stream分拆
        List<Long> todayList = resultList.stream()
                .filter(record -> DateUtil.isSameDay(record.getOutreachDate(), new Date()))
                .map(OutreachSchoolRecordResult::getId)
                .collect(Collectors.toList());

        List<Long> otherList = resultList.stream()
                .filter(record -> !DateUtil.isSameDay(record.getOutreachDate(), new Date()))
                .map(OutreachSchoolRecordResult::getId)
                .collect(Collectors.toList());
        List<List<Long>> todaySplitList = CollUtil.split(todayList, 1000);
        for (List<Long> subList : todaySplitList) {
            results.addAll(outreachSchoolRecordService.getTodaySchoolOutreachTerminalSnapShotDetectionRecordsByOutreachId(subList,outerachExportParam));
        }

        List<List<Long>> otherSplitList = CollUtil.split(otherList, 1000);
        for (List<Long> subList : otherSplitList) {
            results.addAll( outreachSchoolRecordService.getSchoolOutreachTerminalSnapShotDetectionRecordsByOutreachId(subList,outerachExportParam));
        }

        results.forEach(outreachTerminalSnapshotResult -> {
            outreachTerminalSnapshotResult.setIsLogin("1".equals(outreachTerminalSnapshotResult.getIsLogin()) ? "在线" : "离线");
        });
        List<OutreachTerminalSnapshotResult> sortedResults = results.stream()
                .sorted(Comparator.comparingLong(OutreachTerminalSnapshotResult::getOutreachId)
                        .reversed() // 使排序为降序
                        .thenComparing(OutreachTerminalSnapshotResult::getDetectionTime, Comparator.reverseOrder())) // 先按 outreachId 降序排列，再根据 hasDetectionRecords 降序排列
                .collect(Collectors.toList());
        return BeanUtil.copyToList(sortedResults, OutreachTerminalSnapshotPoiResult.class);
    }

    public void buildResult(List<? extends BasePoiResult> poiResultList, List<List<Object>> data) {
        List<OutreachSchoolPoiResult> outreachSchoolRecordResults = (List<OutreachSchoolPoiResult>) poiResultList;
        outreachSchoolRecordResults.forEach(poiResult -> {
            List<Object> dataList = new ArrayList<>();
            dataList.add(poiResult.getId());
            dataList.add(poiResult.getVisitUserName());
            dataList.add("学校走访");
            dataList.add(poiResult.getOrganizationName());
            dataList.add(DateUtil.format(poiResult.getOutreachDate(),"yyyy-MM-dd"));
            dataList.add(poiResult.getFaceFunction());
            dataList.add(poiResult.getMessageFunction());
            dataList.add(poiResult.getGuardIssues());
            dataList.add(poiResult.getTelephoneTermianl());
            List<OutreachTerminalOperationEntity> preRepairList = poiResult.getOutreachTerminalOperationEntityList().stream()
                    .filter(entity -> entity.getOperationType().equals(OperationTypeEnum.PREREPAIR.getCode())).sorted(Comparator.comparing(OutreachTerminalOperationEntity::getTerminalType)).collect(Collectors.toList());
            for (OutreachTerminalOperationEntity outreachTerminalOperationEntity : preRepairList) {
                dataList.add(StatusEnum.getPrerepairDesByCode(outreachTerminalOperationEntity.getStatus()));
            }

            List<OutreachTerminalOperationEntity> repairList = poiResult.getOutreachTerminalOperationEntityList().stream()
                    .filter(entity -> entity.getOperationType().equals(OperationTypeEnum.REPAIR.getCode())).sorted(Comparator.comparing(OutreachTerminalOperationEntity::getTerminalType)).collect(Collectors.toList());
            for (OutreachTerminalOperationEntity outreachTerminalOperationEntity : repairList) {
                dataList.add(outreachTerminalOperationEntity.getRemarks());
            }

            dataList.add(poiResult.getRemoveTerminal());
            dataList.add(poiResult.getAddInstallTerminal());
            dataList.add(poiResult.getAddInstallApplyTerminal());
            dataList.add(1 == poiResult.getMetPrincipal() ? "有" : "没有");
            dataList.add(poiResult.getVisitPeople());
            dataList.add(poiResult.getSchoolDemand());
            dataList.add(poiResult.getIssues());
            dataList.add(poiResult.getGrowthOpportunities());
            List<OutreachTerminalStatusEntity> outreachTerminalStatusList = poiResult.getOutreachTerminalStatusEntityList().stream()
                    .filter(entity -> TerminalTypeEnum.CONSUMER.getCode().equals(entity.getTerminalType()) || TerminalTypeEnum.CABINET.getCode().equals(entity.getTerminalType()) || TerminalTypeEnum.CUSTOM.getCode().equals(entity.getTerminalType())).sorted(Comparator.comparing(OutreachTerminalStatusEntity::getTerminalType)).collect(Collectors.toList());
            for (OutreachTerminalStatusEntity outreachTerminalStatusEntity : outreachTerminalStatusList) {
                if (TerminalTypeEnum.CONSUMER.getCode().equals(outreachTerminalStatusEntity.getTerminalType()) || TerminalTypeEnum.CABINET.getCode().equals(outreachTerminalStatusEntity.getTerminalType())) {
                    dataList.add(StatusEnum.getFunctionDesByCode(outreachTerminalStatusEntity.getStatus()));
                } else {
                    if (poiResult.getHasOtherTermianl() == 1) {
                        dataList.add(ObjectUtil.isEmpty(outreachTerminalStatusEntity.getTerminalName()) ? "" : outreachTerminalStatusEntity.getTerminalName() + ":" + StatusEnum.getFunctionDesByCode(outreachTerminalStatusEntity.getStatus()));
                    } else {
                        dataList.add("");
                    }
                }
            }
            data.add(dataList);
        });
    }

    private List<List<String>> createHead(List<? extends BaseDbResult> boList) {
        List<List<String>> head = new ArrayList<>();
        // 固定的表头部分
        head.add(Arrays.asList("走访ID", "走访ID"));
        head.add(Arrays.asList("汇报人", "汇报人"));
        head.add(Arrays.asList("走访类型", "走访类型"));
        head.add(Arrays.asList("走访单位", "走访单位"));
        head.add(Arrays.asList("走访日期", "走访日期"));
        // 门禁设备下的功能
        head.add(Arrays.asList("门禁设备", "刷脸刷卡功能"));
        head.add(Arrays.asList("门禁设备", "短信接收功能"));
        head.add(Arrays.asList("门禁设备", "咨询门卫是否有其他问题"));
        head.add(Arrays.asList("话机设备", "通话功能测试"));
        head.add(Arrays.asList("当下报修", "门禁设备"));
        head.add(Arrays.asList("当下报修", "话机设备"));
        head.add(Arrays.asList("当下报修", "消费机设备"));
        head.add(Arrays.asList("当下报修", "云校柜设备"));
        head.add(Arrays.asList("当下维修", "门禁设备"));
        head.add(Arrays.asList("当下维修", "话机设备"));
        head.add(Arrays.asList("当下维修", "消费机设备"));
        head.add(Arrays.asList("当下维修", "云校柜设备"));
        head.add(Arrays.asList("拆机加装排查", "有无需要拆机设备"));
        head.add(Arrays.asList("拆机加装排查", "有无需要加装的设备"));
        head.add(Arrays.asList("拆机加装排查", "有无需要加装申请的设备"));
        head.add(Arrays.asList("是否遇见校长", "是否遇见校长"));
        head.add(Arrays.asList("拜访对象", "拜访对象"));
        head.add(Arrays.asList("学校需求及问题", "学校需求及问题"));
        head.add(Arrays.asList("存在的问题", "存在的问题"));
        head.add(Arrays.asList("存在的增长机会", "存在的增长机会"));
        head.add(Arrays.asList("其他设备", "消费机"));
        head.add(Arrays.asList("其他设备", "云校柜"));
        List<OutreachSchoolRecordResult> outreachSchoolRecordResults= (List<OutreachSchoolRecordResult>) boList;
        List<Long> ids = outreachSchoolRecordResults.stream()
                .map(OutreachSchoolRecordResult::getId)
                .collect(Collectors.toList());
        //最多的自定义设备
        long count = 0L;
        //获取走访id中最大的自定义设备个数
        if (ObjectUtil.isNotEmpty(ids)) {
            List<OutreachTerminalStatusEntity> records = new ArrayList<>();
            List<List<Long>> splitList = CollUtil.split(ids, 1000);
            for (List<Long> subList : splitList) {
                records.addAll(outreachTerminalStatusService.getOutreachTerminalStatusEntityByOutreachId(subList));
            }
            Map<Long, Long> countsByOutreachId = records.stream()
                    .collect(Collectors.groupingBy(OutreachTerminalStatusEntity::getOutreachId, Collectors.counting()));
            Optional<Long> maxCount = countsByOutreachId.values().stream()
                    .max(Long::compare);
            if (maxCount.isPresent()) {
                count = maxCount.get();
            }

            for (int i = 0; i < (int) count; i++) {
                head.add(Arrays.asList("其他设备", "其他设备" + (i + 1)));
            }
        }
        return head;
    }

    @Override
    public OuterachExportParam buildQueryParams(PoiTask task) {
        TaskQueryParam taskQueryParam = JSONUtil.toBean(task.getQueryParams(), TaskQueryParam.class);
        return analyseRegion(taskQueryParam);
    }

    private OuterachExportParam analyseRegion(TaskQueryParam taskQueryParam) {

        OuterachExportParam outerachExportParam = JSONUtil.toBean(taskQueryParam.getQueryParams(), OuterachExportParam.class);
        log.info("extraParam:{}", outerachExportParam);
        return outerachExportParam;
    }

}
