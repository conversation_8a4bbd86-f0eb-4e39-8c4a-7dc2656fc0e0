package com.joinus.io.executor;

import com.joinus.io.executor.config.CustomBeanNameGenerator;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@MapperScan(
    basePackages = {"com.joinus.io.executor.mapper", "com.joinus.common.mapper", "com.joinus.dao"},
    nameGenerator = CustomBeanNameGenerator.class
)
public class IoExecutorApplication {
    public static void main(String[] args) {
        SpringApplication.run(IoExecutorApplication.class);
    }
}