package com.joinus.io.executor.ijxadmin.terminal.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TerminalStatusEnum {

    NOT_IN_USER(0, "未使用"),
    IN_USER(1, "在使用"),
    LOST(2, "已丢失"),
    FOR_TEST(3, "测试用");

    @EnumValue
    private final int code;

    private final String label;

}
