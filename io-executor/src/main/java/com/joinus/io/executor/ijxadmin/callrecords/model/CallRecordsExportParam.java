package com.joinus.io.executor.ijxadmin.callrecords.model;

import com.joinus.io.executor.model.BaseParamValid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CallRecordsExportParam extends BaseParamValid {

    /*
     * 终端号
     */
    private String terminalNum;

    /*
     * 电话
     */
    private String phone;


    /*
     * 开始时间
     */
    private String startTime;

    /*
     * 结束时间
     */
    private String endTime;


    private Integer answerState;

    private Integer chargeType;

    /*
     * 学校id
     */
    private Long schoolId;

    /*
     * 区域id
     */
    private String regionId;

    private String terminalName;


    private Long gradeId;

    private Long classId;

    private String  studentName;

    private String  cardCode;

    private List<Long> studentList;

    /*
     * 页码
     */
    private int pageNo;


    /**
     * 每页条数
     */
    private int pageSize;

    private String tableName;

}
