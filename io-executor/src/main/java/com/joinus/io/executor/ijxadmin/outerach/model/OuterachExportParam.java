package com.joinus.io.executor.ijxadmin.outerach.model;

import com.joinus.io.executor.model.BaseParamValid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class OuterachExportParam extends BaseParamValid {

    private String visitUserName;

    private Date outreachStartDate;

    private Date outreachEndDate;

}
