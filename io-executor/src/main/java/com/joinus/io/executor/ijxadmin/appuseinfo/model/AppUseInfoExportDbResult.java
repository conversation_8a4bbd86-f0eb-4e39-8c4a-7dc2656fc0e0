package com.joinus.io.executor.ijxadmin.appuseinfo.model;

import com.joinus.io.executor.ijxadmin.terminal.enums.*;
import com.joinus.io.executor.model.BaseDbResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppUseInfoExportDbResult extends BaseDbResult {
    private String region;
    private String regionName;
    private String schoolName;
    private String userType;
    private String userName;
    private String telNum;
    private String loginTime;
    private String platformType;
    private String version;
    private String firstLogin;
    private String appType;
    private String loginType;

}
