package com.joinus.io.executor.ijxadmin.outerach.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.joinus.io.executor.model.BasePoiResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MobileOuterachExportPoiResult extends BasePoiResult {

    @ExcelProperty("汇报人")
    private String visitUserName;

    @ExcelProperty("走访类型")
    private String visitType;

    @ExcelProperty("走访单位")
    private String organizationName;

    @ExcelProperty("走访日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date outreachDate;

    @ExcelProperty("拜访对象")
    private String visitPeople;

    @ExcelProperty("汇报工作")
    private String workReport;

    @ExcelProperty("客户需求及问题")
    private String customerDemand;

    @ExcelProperty("学校开发")
    private String businessOpportunities;

    @ExcelProperty("城市经理服务反馈")
    private String cityManagerFeedback;

    @ExcelProperty("存在的问题")
    private String issues;

    @ExcelProperty("存在的增长机会")
    private String growthOpportunities;

}
