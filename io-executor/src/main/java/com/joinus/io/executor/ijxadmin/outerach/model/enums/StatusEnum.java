package com.joinus.io.executor.ijxadmin.outerach.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> anpy
 * @create 2024/4/8 14:51
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {

    NOT(0, "未检测","未检测","",""),
    NORMAL(1, "功能正常","功能正常","设备正常使用","维修不了的设备，已粘贴故障标签，当天完成报修"),
    ABNORMAL(2, "功能异常,需要报修","完成通话测试，无问题","有问题，需要报修","报修设备进行登记，关注维修进度，保证按时完成");

    String des;
    String phoneDes;
    String functionDes;
    String prerepairDes;
    Integer code;

    StatusEnum(Integer code, String des,String phoneDes,String  functionDes,String prerepairDes) {
        this.code = code;
        this.des = des;
        this.phoneDes = phoneDes;
        this.functionDes=functionDes;
        this.prerepairDes=prerepairDes;
    }

    public static String getDesByCode(int code) {
        String resultLabel = "";
        for (StatusEnum enums : StatusEnum.values()) {
            if (enums.code == code) {
                resultLabel = enums.des;
            }
        }
        return resultLabel;
    }

    public static String getPhoneDesByCode(int code) {
        String resultLabel = "";
        for (StatusEnum enums : StatusEnum.values()) {
            if (enums.code == code) {
                resultLabel = enums.phoneDes;
            }
        }
        return resultLabel;
    }

    public static String getPrerepairDesByCode(int code) {
        String resultLabel = "";
        for (StatusEnum enums : StatusEnum.values()) {
            if (enums.code == code) {
                resultLabel = enums.prerepairDes;
            }
        }
        return resultLabel;
    }

    public static String getFunctionDesByCode(int code) {
        String resultLabel = "";
        for (StatusEnum enums : StatusEnum.values()) {
            if (enums.code == code) {
                resultLabel = enums.functionDes;
            }
        }
        return resultLabel;
    }
}
