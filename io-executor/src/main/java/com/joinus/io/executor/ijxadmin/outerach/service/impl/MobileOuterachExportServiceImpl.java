package com.joinus.io.executor.ijxadmin.outerach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.joinus.io.executor.ijxadmin.outerach.model.MobileOuterachExportDbResult;
import com.joinus.io.executor.ijxadmin.outerach.model.MobileOuterachExportPoiResult;
import com.joinus.io.executor.ijxadmin.outerach.model.OuterachExportParam;
import com.joinus.io.executor.model.BaseDbResult;
import com.joinus.io.executor.model.BaseParamValid;
import com.joinus.io.executor.model.BasePoiResult;
import com.joinus.io.executor.param.PoiTask;
import com.joinus.io.executor.param.TaskQueryParam;
import com.joinus.io.executor.service.*;
import com.joinus.io.executor.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;


@Service("mobileOuterachExportServiceImpl")
@AllArgsConstructor
@Slf4j
public class MobileOuterachExportServiceImpl extends AbstractExportBizService {

  private IOuterachMobileRecordService mobileOuterachRecordService;


    @Override
    public String checkParam(BaseParamValid task, Long count) {
        OuterachExportParam outerachExportParam = (OuterachExportParam) task;
        Date startTime = outerachExportParam.getOutreachStartDate();
        Date endTime = outerachExportParam.getOutreachEndDate();
        // 计算两个时间之间的差，单位：天
        long days = DateUtil.between(startTime, endTime, DateUnit.DAY);
        if (days > 365) {
            return "移动走访记录最多导出365天";
        }

        return null;
    }

    @Override
    public Long queryResultCount(BaseParamValid task) {
        return 0L;
    }

    @Override
    public List<MobileOuterachExportDbResult> listBiz(BaseParamValid task, String exportHeaders) {
        OuterachExportParam outerachExportParam = (OuterachExportParam) task;
        return mobileOuterachRecordService.getMobileOutreachRecord(outerachExportParam);
    }

    @Override
    public List<MobileOuterachExportPoiResult> beautyResult(List<? extends BaseDbResult> boList) {

        List<MobileOuterachExportPoiResult> resultList = BeanUtil.copyToList(boList, MobileOuterachExportPoiResult.class);
        resultList.forEach(item -> {
            item.setVisitType("移动走访");
        });
        return resultList;
    }

    @Override
    public ByteArrayInputStream buildPoiStream(BaseParamValid task, String fileName,
                                               String exportHeaders,int pageSize,long totalCount) {
        //根据业务参数查询需要导出的业务数据列表
        List<? extends BaseDbResult> baseDbResultList = listBiz(task, exportHeaders);

        //对结果列表各字段进行对应业务处理，形成可视化字段
        List<? extends BasePoiResult> poiResultList = beautyResult(baseDbResultList);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        EasyExcel.write(out, MobileOuterachExportPoiResult.class)
                .sheet("移动走访")
                .registerWriteHandler(EasyExcelUtil.cellBorder())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(poiResultList)
        ;
        return new ByteArrayInputStream(out.toByteArray());
    }


    @Override
    public OuterachExportParam buildQueryParams(PoiTask task) {
        TaskQueryParam taskQueryParam = JSONUtil.toBean(task.getQueryParams(), TaskQueryParam.class);
        return analyseRegion(taskQueryParam);
    }

    private OuterachExportParam analyseRegion(TaskQueryParam taskQueryParam) {

        OuterachExportParam outerachExportParam = JSONUtil.toBean(taskQueryParam.getQueryParams(), OuterachExportParam.class);
        log.info("extraParam:{}", outerachExportParam);
        return outerachExportParam;
    }

}
