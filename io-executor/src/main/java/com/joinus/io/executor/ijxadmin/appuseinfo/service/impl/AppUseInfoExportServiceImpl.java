package com.joinus.io.executor.ijxadmin.appuseinfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.joinus.common.exception.BaseException;
import com.joinus.io.executor.enums.ImportDetailResultStateEnum;
import com.joinus.io.executor.ijxadmin.appuseinfo.model.AppUseInfoExportDbResult;
import com.joinus.io.executor.ijxadmin.appuseinfo.model.AppUseInfoExportParam;
import com.joinus.io.executor.ijxadmin.appuseinfo.model.AppUseInfoExportPoiResult;
import com.joinus.io.executor.ijxadmin.terminal.enums.ExcelLevelColorEnum;
import com.joinus.io.executor.ijxadmin.terminal.model.TerminalExportExtraParam;
import com.joinus.io.executor.ijxadmin.terminal.model.TerminalExportParam;
import com.joinus.io.executor.ijxadmin.terminal.model.TerminalInfoExportDbResult;
import com.joinus.io.executor.ijxadmin.terminal.model.TerminalInfoExportPoiResult;
import com.joinus.io.executor.model.BaseDbResult;
import com.joinus.io.executor.model.BaseParamValid;
import com.joinus.io.executor.model.BasePoiResult;
import com.joinus.io.executor.param.PoiTask;
import com.joinus.io.executor.param.TaskQueryParam;
import com.joinus.io.executor.service.AbstractExportBizService;
import com.joinus.io.executor.service.IAppUseInfoService;
import com.joinus.io.executor.service.ISysUserService;
import com.joinus.io.executor.service.ITerminalService;
import com.joinus.io.executor.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("appUseInfoExportServiceImpl")
@AllArgsConstructor
@Slf4j
public class AppUseInfoExportServiceImpl extends AbstractExportBizService {

    private final IAppUseInfoService appUseInfoService;


    @Override
    public String  checkParam(BaseParamValid task,Long count) {

        if (count>60000) {
            return "数据大于6万条，请添加搜索条件后重新导出";
        }
        return null;
    }

    @Override
    public Long queryResultCount(BaseParamValid task) {
        AppUseInfoExportParam appUseInfoExportParam = (AppUseInfoExportParam) task;
        convertUserType(appUseInfoExportParam);
        Long count=0L;
        if ("qyl".equals(appUseInfoExportParam.getAppType())) {
            count = appUseInfoService.getQylUserInfoCount(appUseInfoExportParam);

        } else if ("xda".equals(appUseInfoExportParam.getAppType())) {
            count =appUseInfoService.getXdaUserInfoCount(appUseInfoExportParam);
        }
        return count;
    }

    private void convertUserType(AppUseInfoExportParam appUseInfoExportParam){
        if("1".equals(appUseInfoExportParam.getUserType())){
            appUseInfoExportParam.setUserType("0");
        }else if("2".equals(appUseInfoExportParam.getUserType())){
            appUseInfoExportParam.setUserType("1");
        }else{
            appUseInfoExportParam.setUserType(null);
        }
    }
    @Override
    public List<AppUseInfoExportDbResult> listBiz(BaseParamValid task, String exportHeaders) {
        AppUseInfoExportParam appUseInfoExportParam = (AppUseInfoExportParam) task;
        List<AppUseInfoExportDbResult> appUseInfoExportDbResults = new ArrayList<>();
        if ("qyl".equals(appUseInfoExportParam.getAppType())) {
            appUseInfoExportDbResults=appUseInfoService.getQylUserInfo(appUseInfoExportParam);
        } else if ("xda".equals(appUseInfoExportParam.getAppType())) {
            appUseInfoExportDbResults=appUseInfoService.getXdaUserInfo(appUseInfoExportParam);
        }
        return appUseInfoExportDbResults;
    }


    @Override
    public List<AppUseInfoExportPoiResult> beautyResult(List<? extends BaseDbResult> boList) {

        List<AppUseInfoExportPoiResult> resultList = BeanUtil.copyToList(boList, AppUseInfoExportPoiResult.class);
        resultList.forEach(item -> {
            String telNum = item.getTelNum();
            if (telNum != null && telNum.length() == 11) {
                String maskedTelNum = telNum.substring(0, 3) + "****" + telNum.substring(7);
                item.setTelNum(maskedTelNum);
            }
        });
        return resultList;
    }


    @Override
    public ByteArrayInputStream buildPoiStream(BaseParamValid task, String fileName,
                                               String exportHeaders,int pageSize,long totalCount) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out, AppUseInfoExportPoiResult.class)
                .registerWriteHandler(EasyExcelUtil.cellBorder())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();
        // Creating the Sheet
        WriteSheet writeSheet = EasyExcel.writerSheet(fileName).build();
        AppUseInfoExportParam appUseInfoExportParam = (AppUseInfoExportParam) task;
        int count = (int) Math.ceil((double) totalCount / pageSize);
        count = count == 0 ? 1 : count;
        for (int i = 0; i < count; i++) {
            //根据业务参数查询需要导出的业务数据列表
            appUseInfoExportParam.setPageSize(pageSize);
            appUseInfoExportParam.setPageNo(i + 1);
            List<? extends BaseDbResult> baseDbResultList = listBiz(appUseInfoExportParam, exportHeaders);
            //对结果列表各字段进行对应业务处理，形成可视化字段
            List<? extends BasePoiResult> basePoiResultList = beautyResult(baseDbResultList);
            excelWriter.write(basePoiResultList, writeSheet);
        }
        // Finish writing
        excelWriter.finish();
        return new ByteArrayInputStream(out.toByteArray());
    }

    @Override
    public AppUseInfoExportParam buildQueryParams(PoiTask task) {
        TaskQueryParam taskQueryParam = JSONUtil.toBean(task.getQueryParams(), TaskQueryParam.class);
        return analyseRegion(taskQueryParam);
    }


    private AppUseInfoExportParam analyseRegion(TaskQueryParam taskQueryParam) {

        AppUseInfoExportParam appUseInfoExportParam = JSONUtil.toBean(taskQueryParam.getQueryParams(), AppUseInfoExportParam.class);
        AppUseInfoExportParam extraParam = new AppUseInfoExportParam();
        BeanUtil.copyProperties(appUseInfoExportParam, extraParam);

          log.info("extraParam:{}",extraParam);
        return extraParam;
    }

}
