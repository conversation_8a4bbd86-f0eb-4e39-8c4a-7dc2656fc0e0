package com.joinus.io.executor.ijxadmin.business.model;


import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Date;

@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OpenBusinessApply {

	private String managerTelnum;/* 操作人手机号 */
	private Long schoolId;/* 学校ID */
	private String telnum;//业务电话号码
	private String sncode;/* 随机码 用户通过短信发起请求，BOSS返回的随机码 */
	private Long studentId;//学生Id
	private Long productId;/* 办理套餐（套餐ID） */
	private String productCode;//短信业务代码
}
