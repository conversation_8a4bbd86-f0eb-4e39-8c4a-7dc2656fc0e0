package com.joinus.io.executor.constants;

public class RedisExpireConstant {

    // 七牛私有文件上传过期删除时间,不包含当天，往后30天的23:59:59
    public static final Integer QINIU_PRIVATE_FILE_EXPIRE_DAYS = 30 ;

    // 七牛私有文件下载链接过期时间为5分钟，其中浏览器会缓存下载文件（即链接失效，但是在下载过的浏览器访问时仍可下载）
    public static final Integer QINIU_PRIVATE_FILE_DOWNLOAD_URL_EXPIRE_SENDS = 60 * 5 ;


    // kafka重复消费时间间隔(秒)
    public static final Integer REDIS_INTERVAL_SECONDS = 30 ;

    /**
     * 处理business 手机号
     **/
    public static final String REDIS_KEY_BUSINESS_PHONES="IO-EXECUTOR:BUSINESS_PHONE_%s_%s";


    /**
     * 处理parent 手机号
     **/
    public static final String REDIS_KEY_PARENT_PHONES="IO-EXECUTOR:PARENT_PHONE_%s";
}
