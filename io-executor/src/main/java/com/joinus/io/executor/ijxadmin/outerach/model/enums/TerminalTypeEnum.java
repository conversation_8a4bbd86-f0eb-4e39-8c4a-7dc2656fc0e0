package com.joinus.io.executor.ijxadmin.outerach.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> anpy
 * @create 2024/4/8 14:51
 */
@Getter
@AllArgsConstructor
public enum TerminalTypeEnum {

    ACCESS(1, "门禁设备"),
    TELEPHONE(2, "话机设备"),
    CONSUMER(3, "消费机"),
    CABINET(4, "云校柜"),
    CUSTOM(5,"自定义");

    String des;
    Integer code;

    TerminalTypeEnum(Integer code, String des) {
        this.code = code;
        this.des = des;
    }

    public static String getDesByCode(int code) {
        String resultLabel = "";
        for (TerminalTypeEnum enums : TerminalTypeEnum.values()) {
            if (enums.code == code) {
                resultLabel = enums.des;
            }
        }
        return resultLabel;
    }
}
