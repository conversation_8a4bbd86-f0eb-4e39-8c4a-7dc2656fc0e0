package com.joinus.io.executor.ijxadmin.business.model;

import java.io.Serializable;

public class ChinaMobileResultInfo implements Serializable{
	
	private String respCode;
	
	private String respDesc;
	
	private Object result;


	public String getRespCode() {
		return respCode;
	}



	public void setRespCode(String respCode) {
		this.respCode = respCode;
	}



	public String getRespDesc() {
		return respDesc;
	}



	public void setRespDesc(String respDesc) {
		this.respDesc = respDesc;
	}



	public Object getResult() {
		return result;
	}



	public void setResult(Object result) {
		this.result = result;
	}


	@Override
	public String toString() {
		return "MobileDataInfo [respCode=" + respCode + ", respDesc=" + respDesc + ", result=" + result + "]";
	} 
	
	
}
