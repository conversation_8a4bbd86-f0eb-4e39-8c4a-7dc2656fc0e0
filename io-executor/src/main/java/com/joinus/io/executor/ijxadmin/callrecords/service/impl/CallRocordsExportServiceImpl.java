package com.joinus.io.executor.ijxadmin.callrecords.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.joinus.io.executor.enums.CallTypeEnum;
import com.joinus.io.executor.enums.ChargeTypeEnum;
import com.joinus.io.executor.enums.IsLoginAppEnum;
import com.joinus.io.executor.enums.PhoneTypeEnum;
import com.joinus.io.executor.ijxadmin.callrecords.model.CallRecordsExportDbResult;
import com.joinus.io.executor.ijxadmin.callrecords.model.CallRecordsExportParam;
import com.joinus.io.executor.ijxadmin.callrecords.model.CallRecordsExportPoiResult;
import com.joinus.io.executor.model.BaseDbResult;
import com.joinus.io.executor.model.BaseParamValid;
import com.joinus.io.executor.model.BasePoiResult;
import com.joinus.io.executor.model.entity.School;
import com.joinus.io.executor.model.entity.Student;
import com.joinus.io.executor.model.entity.Terminal;
import com.joinus.io.executor.param.PoiTask;
import com.joinus.io.executor.param.TaskQueryParam;
import com.joinus.io.executor.service.*;
import com.joinus.io.executor.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.joinus.io.executor.constants.Constant.PHONE_DETAIL_TABLE.*;


@Service("callRocordsExportServiceImpl")
@AllArgsConstructor
@Slf4j
public class CallRocordsExportServiceImpl extends AbstractExportBizService {

    private final ICallRecordsService callRecordsService;
    private final ITerminalService terminalService;
    private final ISchoolService schoolService;
    private final IStudentService studentService;


    @Override
    public String checkParam(BaseParamValid task,Long count) {
        CallRecordsExportParam callRecordsExportParam = (CallRecordsExportParam) task;
        Date startTime = DateUtil.parseDate(callRecordsExportParam.getStartTime());
        Date endTime = DateUtil.parseDate(callRecordsExportParam.getEndTime());
        String cardCode = callRecordsExportParam.getCardCode();
        String studentName = callRecordsExportParam.getStudentName();
        String phone = callRecordsExportParam.getPhone();
        Long schoolId =callRecordsExportParam.getSchoolId();

        // 计算两个时间之间的差，单位：天
        long days = DateUtil.between(startTime, endTime, DateUnit.DAY);
        if (StrUtil.isNotBlank(cardCode) || StrUtil.isNotBlank(phone) || (ObjectUtil.isNotEmpty(studentName) && ObjectUtil.isNotEmpty(schoolId))) {
            if (days > 366) {
                return "通话时间最多导出366天";
            }
        } else {
            if (days > 30) {
                return "不包含学生姓名或卡号或拨打电话查询,最多导出30天";
            }
        }

        if (count > 60000) {
            return "导出条数大于6万条,无法导出";
        }

        return null;
    }

    @Override
    public Long queryResultCount(BaseParamValid task) {
        CallRecordsExportParam callRecordsExportParam = (CallRecordsExportParam) task;
        Long callRecordsCount = 0L;
        String studentName = callRecordsExportParam.getStudentName();
        Long schoolId =callRecordsExportParam.getSchoolId();
        if (ObjectUtil.isNotEmpty(studentName) && ObjectUtil.isNotEmpty(schoolId)) {
            List<Student> students = studentService.selectStudentsBySchoolIdAndStudentName(schoolId, studentName);
            if (ObjectUtil.isEmpty(students)) {
                return 0L;
            }else{
                callRecordsExportParam.setStudentList(students.stream().map(Student::getId).collect(Collectors.toList()));
            }
        }
        if (isToday(callRecordsExportParam)) {
            callRecordsCount = callRecordsService.getTodayCallRecordsCount(callRecordsExportParam);
        } else {
            callRecordsExportParam.setTableName(this.getPhoneDetailTableName(callRecordsExportParam.getStartTime()));
            callRecordsCount = callRecordsService.getCallRecordsCount(callRecordsExportParam);
        }
        return callRecordsCount;
    }

    /**
     * @Description: 通话明细分表
     * <AUTHOR>
     * @date 2024-10-9 09:25:55
     * @param
     * @return
     */
    public  String getPhoneDetailTableName(String date){
        if (StrUtil.isBlank(date)) {
            return TABLE_NAME_PHONE_DETAIL_NOW;
        }
        Date jdkDate = DateUtil.parseDate(date).toJdkDate();
        if (DateUtil.year(new Date()) > DateUtil.year(jdkDate)) {
            return StrUtil.format(TABLE_NAME_PHONE_DETAIL_HISTORY, DateUtil.year(jdkDate));
        }

        return TABLE_NAME_PHONE_DETAIL_THIS_YEAR;

    }

    private Boolean isToday(CallRecordsExportParam callRecordsExportParam) {
        String startTime = callRecordsExportParam.getStartTime();
        String endTime = callRecordsExportParam.getEndTime();
        String today = DateUtil.today();
        return today.equals(startTime) && today.equals(endTime);
    }

    @Override
    public List<CallRecordsExportDbResult> listBiz(BaseParamValid task, String exportHeaders) {
        CallRecordsExportParam callRecordsExportParam = (CallRecordsExportParam) task;
        List<CallRecordsExportDbResult> callRecords = null;
        if (isToday(callRecordsExportParam)) {
            callRecords = callRecordsService.getTodayCallRecords(callRecordsExportParam);
        } else {
            callRecordsExportParam.setTableName(this.getPhoneDetailTableName(callRecordsExportParam.getStartTime()));
            callRecords = callRecordsService.getCallRecords(callRecordsExportParam);
        }
        return callRecords;
    }

    @Override
    public List<CallRecordsExportPoiResult> beautyResult(List<? extends BaseDbResult> boList) {

        List<CallRecordsExportPoiResult> resultList = BeanUtil.copyToList(boList, CallRecordsExportPoiResult.class);
        resultList.forEach(item -> {
            String phone = item.getPhone();
            if (ObjectUtil.isNotEmpty(phone)) {
                item.setPhone(DesensitizedUtil.mobilePhone(phone));
            }
            String studentName = item.getStudentName();
            if (ObjectUtil.isNotEmpty(studentName)) {
                item.setStudentName(DesensitizedUtil.chineseName(studentName));
            }
            String answerState = item.getAnswerState();
            if (ObjectUtil.isNotEmpty(answerState)) {
                item.setAnswerState(PhoneTypeEnum.getLabelByCode(Integer.parseInt(answerState)));
            }
            String chargeType = item.getChargeType();
            if (ObjectUtil.isNotEmpty(chargeType)) {
                item.setChargeType(ChargeTypeEnum.getLabelByCode(Integer.parseInt(chargeType)));
            }

            String callType = item.getCallType();
            if (ObjectUtil.isNotEmpty(callType)) {
                item.setCallType(CallTypeEnum.getLabelByCode(Integer.parseInt(callType)));
            }

            String isLoginApp = item.getIsLoginApp();
            if (ObjectUtil.isNotEmpty(isLoginApp)) {
                item.setIsLoginApp(IsLoginAppEnum.getLabelByCode(Integer.parseInt(isLoginApp)));
            }

            Date startTime = item.getStartTime();
            if (ObjectUtil.isNotEmpty(startTime)) {
                item.setCallTime(DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss"));
            }

            String talkLength = item.getTalkLength();
            if (ObjectUtil.isNotEmpty(talkLength)) {
                int minutes = Integer.parseInt(talkLength) / 60;
                int seconds = Integer.parseInt(talkLength) % 60;
                item.setTalkLength(minutes + "分" + seconds + "秒");
            }

            if (ObjectUtil.isEmpty(item.getSchoolName())) {
                if (ObjectUtil.isNotEmpty(item.getTerminalNum())) {
                    List<Terminal> terminals = terminalService.getTerminalByterminalNum(item.getTerminalNum());
                    if (ObjectUtil.isNotEmpty(terminals)) {
                        List<School> schools = schoolService.listById(terminals.get(0).getSchoolId().longValue());
                        if (ObjectUtil.isNotEmpty(schools)) {
                            item.setSchoolName(schools.get(0).getSchoolName());
                        }
                    }
                }
            }

            if (ObjectUtil.isEmpty(item.getStudentName())) {
                if (ObjectUtil.isNotEmpty(item.getCardCode())) {
                    List<Student> students = studentService.getStudentsByCardCode(item.getCardCode());
                    if (ObjectUtil.isNotEmpty(students)) {
                        item.setStudentName(students.get(0).getStudentName());
                    }
                }
            }
        });
        return resultList;
    }


    @Override
    public ByteArrayInputStream buildPoiStream(BaseParamValid task, String fileName,
                                               String exportHeaders, int pageSize, long totalCount) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out, CallRecordsExportPoiResult.class)
                .excludeColumnFiledNames(Collections.singleton("startTime"))
                .registerWriteHandler(EasyExcelUtil.cellBorder())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();
        // Creating the Sheet
        WriteSheet writeSheet = EasyExcel.writerSheet(fileName).build();
        CallRecordsExportParam callRecordsExportParam = (CallRecordsExportParam) task;
        int count = (int) Math.ceil((double) totalCount / pageSize);
        count = count == 0 ? 1 : count;
        for (int i = 0; i < count; i++) {
            //根据业务参数查询需要导出的业务数据列表
            callRecordsExportParam.setPageSize(pageSize);
            callRecordsExportParam.setPageNo(i + 1);
            List<? extends BaseDbResult> baseDbResultList = listBiz(callRecordsExportParam, exportHeaders);
            //对结果列表各字段进行对应业务处理，形成可视化字段
            List<? extends BasePoiResult> basePoiResultList = beautyResult(baseDbResultList);
            excelWriter.write(basePoiResultList, writeSheet);
        }
        // Finish writing
        excelWriter.finish();
        return new ByteArrayInputStream(out.toByteArray());
    }

    @Override
    public CallRecordsExportParam buildQueryParams(PoiTask task) {
        TaskQueryParam taskQueryParam = JSONUtil.toBean(task.getQueryParams(), TaskQueryParam.class);
        return analyseRegion(taskQueryParam);
    }

    private CallRecordsExportParam analyseRegion(TaskQueryParam taskQueryParam) {

        CallRecordsExportParam callRecordsExportParam = JSONUtil.toBean(taskQueryParam.getQueryParams(), CallRecordsExportParam.class);
        log.info("extraParam:{}", callRecordsExportParam);
        return callRecordsExportParam;
    }

}
