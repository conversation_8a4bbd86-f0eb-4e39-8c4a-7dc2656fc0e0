package com.joinus.io.executor.ijxadmin.terminal.model;

import com.joinus.io.executor.ijxadmin.terminal.enums.*;
import com.joinus.io.executor.model.BaseDbResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TerminalInfoExportDbResult extends BaseDbResult {
    private Long id;
    private String city;
    private String county;
    private Integer cityId;
    private Integer countyId;
    private String terminalNum;
    private String terminalName;
    private Integer terminalType;
    private String terminalTypeName;
    private Integer typeId;
    private String typeName;
    private String simNum;
    private String simStatus;
    private TerminalOprCodeEnum oprCode;
    private SimPriceEnum price;
    private Date lastCallTime;
    private String schoolName;
    private Long schoolId;
    private String schoolType;
    private TerminalBusinessFlagEnum businessFlag;
    private String installAddress;
    private String softVersion;
    private String hardVersion;
    private String lastIp;
    private String whiteNumFlag;
    private Date whiteNumTime;
    private String whiteNum;
    private TerminalLoginEnum isLogin;
    private Date lastTime;
    private TerminalStatusEnum terminalStatus;
    private Date installTime;
    private String IMEI;
    private String IMSI;
    private String SN;
    private String deactivatedAt;
    private TerminalIsactiveEnum isActive;
    private String imgNum;
    private String installType;
    private String remark;
    private String userNames;
    private Long parentTerminalId;
    private Integer folder;
    private BigDecimal totalCosts;
    private BigDecimal currentMonthExpense;
    private BigDecimal discountOffers;
    private BigDecimal simCosts;

}
