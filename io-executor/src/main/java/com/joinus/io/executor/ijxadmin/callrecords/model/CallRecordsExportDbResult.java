package com.joinus.io.executor.ijxadmin.callrecords.model;

import com.joinus.io.executor.model.BaseDbResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CallRecordsExportDbResult extends BaseDbResult {

    private String superRegionName;

    private String regionName;

    private String schoolName;

    private String gradeName;

    private String className;

    private String studentName;

    private String cardCode;

    private String terminalNum;

    private String terminalName;

    private String simNum;

    private String installAddress;

    private Date startTime;

    private String phone;

    private String talkLength;

    private String answerState;

    private String chargeType;

    private String callType;

    private String waitSeconds;

    private String isLoginApp;

}
