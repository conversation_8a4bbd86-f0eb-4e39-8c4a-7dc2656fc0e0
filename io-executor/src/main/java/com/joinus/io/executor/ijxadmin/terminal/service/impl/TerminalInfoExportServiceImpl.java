package com.joinus.io.executor.ijxadmin.terminal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.joinus.io.executor.enums.ImportDetailResultStateEnum;
import com.joinus.io.executor.ijxadmin.terminal.enums.ExcelLevelColorEnum;
import com.joinus.io.executor.ijxadmin.terminal.model.TerminalExportExtraParam;
import com.joinus.io.executor.ijxadmin.terminal.model.TerminalExportParam;
import com.joinus.io.executor.ijxadmin.terminal.model.TerminalInfoExportDbResult;
import com.joinus.io.executor.ijxadmin.terminal.model.TerminalInfoExportPoiResult;
import com.joinus.io.executor.model.BaseDbResult;
import com.joinus.io.executor.model.BaseParamValid;
import com.joinus.io.executor.model.BasePoiResult;
import com.joinus.io.executor.model.PoiOperator;
import com.joinus.io.executor.param.PoiTask;
import com.joinus.io.executor.param.TaskQueryParam;
import com.joinus.io.executor.service.AbstractExportBizService;
import com.joinus.io.executor.service.ISysUserService;
import com.joinus.io.executor.service.ITerminalService;
import com.joinus.io.executor.utils.EasyExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("terminalInfoExportService")
@AllArgsConstructor
@Slf4j
public class TerminalInfoExportServiceImpl extends AbstractExportBizService {

    private final ITerminalService terminalService;

    private final ISysUserService sysUserService;

    @Override
    public String  checkParam(BaseParamValid task,Long count) {
        return null;
    }


    @Override
    public Long queryResultCount(BaseParamValid task) {
        return 0L;
    }

    @Override
    public List<TerminalInfoExportDbResult> listBiz(BaseParamValid task, String exportHeaders) {
        TerminalExportExtraParam terminalExportExtraParam = (TerminalExportExtraParam) task;
        return terminalService.listTerminalInfo(terminalExportExtraParam);
    }


    @Override
    public List<TerminalInfoExportPoiResult> beautyResult(List<? extends BaseDbResult> boList) {
        List<TerminalInfoExportDbResult> dbResultList = (List<TerminalInfoExportDbResult>) boList;
        dbResultList.stream().forEach(dbResult -> {
            BigDecimal simCosts =
                    dbResult.getTotalCosts().compareTo(dbResult.getCurrentMonthExpense().add(dbResult.getDiscountOffers())) > 0 ?
                            dbResult.getTotalCosts() :
                            dbResult.getCurrentMonthExpense().add(dbResult.getDiscountOffers());
            dbResult.setSimCosts(simCosts);
        });
        List<TerminalInfoExportPoiResult> dtoList = BeanUtil.copyToList(dbResultList, TerminalInfoExportPoiResult.class);

        //处理树形结构
        Map<Long, List<TerminalInfoExportPoiResult>> parentTerminalMap =
                dtoList.stream()
                        .collect(Collectors.groupingBy(TerminalInfoExportPoiResult::getParentTerminalId));

        List<TerminalInfoExportPoiResult> resultList = new ArrayList<>(dtoList.size());
        dtoList.stream()
                .filter(m -> m.getParentTerminalId() == -1)
                .forEach(m -> {
                    m.setLevel(0);
                    resultList.add(m);
                    setChildrenToResultList(m, parentTerminalMap, resultList);
                });
        return resultList;
    }

    /**
     * 递归查询子节点，生成按照先根序遍历方式遍历的list列表
     * @param parent  根节点
     *
     * @return 根节点信息
     */
    private void setChildrenToResultList(TerminalInfoExportPoiResult parent,
                                         Map<Long, List<TerminalInfoExportPoiResult>> parentMap,
                                         List<TerminalInfoExportPoiResult> resultList) {

        List<TerminalInfoExportPoiResult> children = parentMap.get(parent.getId());

        if (CollectionUtil.isNotEmpty(children)) {
            children.forEach(child -> {
                child.setLevel(parent.getLevel() + 1);
                resultList.add(child);
                setChildrenToResultList(child, parentMap, resultList);
            });
        }
    }

    @Override
    public ByteArrayInputStream buildPoiStream(BaseParamValid task, String fileName,
                                               String exportHeaders,int pageSize,long totalCount) {
        //根据业务参数查询需要导出的业务数据列表
         List<? extends BaseDbResult> baseDbResultList = listBiz(task, exportHeaders);

        //对结果列表各字段进行对应业务处理，形成可视化字段
         List<? extends BasePoiResult> poiResultList = beautyResult(baseDbResultList);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        HashMap<Integer, Short> colorMap = new HashMap<Integer, Short>();

        EasyExcel.write(out, TerminalInfoExportPoiResult.class)
                .sheet(fileName)
                .registerWriteHandler(new CellWriteHandler() {
                    @Override
                    public void afterCellDispose(CellWriteHandlerContext context) {
                        if (BooleanUtils.isNotTrue(context.getHead())) {
                            WriteCellData<?> cellData = context.getFirstCellData();
                            WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                            writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
                            if (cellData.getColumnIndex() == 0) {
                                writeCellStyle.setFillForegroundColor(ExcelLevelColorEnum.getColorByLevelDesc(cellData.getStringValue()));
                                colorMap.put(cellData.getRowIndex(),
                                        ExcelLevelColorEnum.getColorByLevelDesc(cellData.getStringValue()));
                            } else {
                                writeCellStyle.setFillForegroundColor(colorMap.get(cellData.getRowIndex()));
                            }
                        } else {
                            WriteCellData<?> cellData = context.getFirstCellData();
                            WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                            writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
                            writeCellStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
                        }
                    }
                })
                .registerWriteHandler(EasyExcelUtil.cellBorder())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .doWrite(poiResultList)
        ;
        return new ByteArrayInputStream(out.toByteArray());
    }
    @Override
    public TerminalExportExtraParam buildQueryParams(PoiTask task) {
        TaskQueryParam taskQueryParam = JSONUtil.toBean(task.getQueryParams(), TaskQueryParam.class);
        return analyseRegion(taskQueryParam);
    }

    public boolean updatePoiImportDetailResultState(Long id, ImportDetailResultStateEnum stateEnum, String errMsg) {
        return false;
    }

    private TerminalExportExtraParam analyseRegion(TaskQueryParam taskQueryParam) {

        TerminalExportParam terminalExportParam = JSONUtil.toBean(taskQueryParam.getQueryParams(), TerminalExportParam.class);
        TerminalExportExtraParam extraParam = new TerminalExportExtraParam();
        BeanUtil.copyProperties(terminalExportParam, extraParam);


        if (StrUtil.isNotBlank(extraParam.getSimStatus())) {
            extraParam.setSimStatusList(StrUtil.split(extraParam.getSimStatus(), ','));
        }

        List<Integer> terminalStatusList = StrUtil.split(extraParam.getTerminalStatus(), ',')
                .stream()
                .filter(ObjectUtil::isNotEmpty)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        extraParam.setTerminalStatusList(terminalStatusList);


        List<Integer> businessFlagList = StrUtil.split(extraParam.getBusinessFlag(), ',')
                .stream()
                .filter(ObjectUtil::isNotEmpty)
                .map(Integer::parseInt)
                .collect(Collectors.toList());


        extraParam.setBusinessFlagList(businessFlagList);

        Long userId = taskQueryParam.getOperator().getId();
        List<String> regionList = sysUserService.listRegionsByUserId(userId);
        List<Long> schoolIdList = sysUserService.listSchoolIdByUserId(userId);


        Integer regionType = extraParam.getRegionType();
        String region = extraParam.getRegion();

        boolean isManager = CollectionUtil.isEmpty(regionList) && CollectionUtil.isEmpty(schoolIdList);
        boolean containAllRegions = CollectionUtil.isNotEmpty(regionList) && "0".equals(regionList.get(0));
        if (isManager || containAllRegions) {
            //管理员
            if (null != regionType && StrUtil.isNotBlank(region)) {
                switch (extraParam.getRegionType().toString()) {
                    case "1" :
                        extraParam.setRegionProvince(region);
                        break;
                    case "2" :
                        extraParam.setRegionCity(region);
                        break;
                    case "3" :
                        extraParam.setRegionCounty(region);
                    default:
                        break;
                }
            }
        } else {
            //普通用户
            extraParam.setUserId(userId);
            if (null != regionType && StrUtil.isNotBlank(region)) {
                extraParam.setRegionId(extraParam.getRegion());
            }
            if (null == regionType || regionType != 1 ) {
                extraParam.setRegionAnd(" and (srn2.REGION_ID = '"+region+"' or srn.REGION_ID = '"+region+"' ) ");
            }

                extraParam.setRegionAnd(" and (srn3.REGION_ID = '"+region+"') ");
        }
          log.info("extraParam:{}",extraParam);
        return extraParam;
    }

}
