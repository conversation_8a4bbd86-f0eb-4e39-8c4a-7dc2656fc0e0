package com.joinus.io.executor.ijxadmin.business.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 学校套餐配置
 *
 * <AUTHOR> maweihua
 * @create 2024-10-10 09:59:55
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ProductSchool {
    /**
     * 套餐ID
     */
    private Long productId;
    /**
     * 套餐编号
     */
    private String productCode;
    /**
     * 年级ID
     */
    private Long gradeId;
}
