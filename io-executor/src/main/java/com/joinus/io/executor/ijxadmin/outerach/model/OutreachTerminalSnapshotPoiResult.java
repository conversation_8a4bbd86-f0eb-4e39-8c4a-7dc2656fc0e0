package com.joinus.io.executor.ijxadmin.outerach.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.joinus.io.executor.model.BasePoiResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> mawh
 * @create 2024-4-15 17:45:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class OutreachTerminalSnapshotPoiResult  extends BasePoiResult {


    @ExcelProperty("走访ID")
    private Long outreachId;

    @ExcelProperty("学校名称")
    private String schoolName;

    @ExcelProperty("设备名称")
    private String terminalName;

    @ExcelProperty("终端类型")
    private String terminalTypeDesc;

    @ExcelProperty("终端编号")
    private String terminalNum;

    @ExcelProperty("设备状态")
    private String isLogin;

    @ExcelProperty("检测记录")
    private String hasDetectionRecords;

    @ExcelProperty("检测人员")
    private String detectionUserName;

    @ExcelProperty("检测时间")
    private String detectionTime;



}
