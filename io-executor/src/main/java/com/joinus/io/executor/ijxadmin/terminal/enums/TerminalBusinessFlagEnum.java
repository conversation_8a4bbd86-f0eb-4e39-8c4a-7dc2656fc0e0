package com.joinus.io.executor.ijxadmin.terminal.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TerminalBusinessFlagEnum {

    NEWLY_ADDED(1, "新增"),
    NORMAL(2, "正常"),
    TO_BE_DETERMINED(3, "待定"),
    SUSPEND(4, "终止"),
    ILLUSORY(5, "虚假"),
    OUTSIDE_THE_SYSTEM(6, "体制外");

    @EnumValue
    private final int code;

    private final String label;

}
