package com.joinus.io.executor.constants;

/**
 * @className: Regex
 */
public interface Regex {
    String USERNAME = "^[a-zA-Z]\\w{4,17}$";
    String PASSWORD = "(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+.~_¥!@#$%^&*()]{6,20}$";
    String MOBILE = "^1\\d{10}$";
    String EMAIL = "^([a-z0-9A-Z]+[-|.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
    String CHINESE = "^[一-龥],*$";
    String CHINESE_NAME = "^[一-龥]{2,6}$";
    String ID_CARD = "(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";
    String URL = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";
    String IP_ADDR = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
    String NUMBER = "[0-9]*";
    String IMAGE = "(?i)(jpg|jpeg|png|gif)$";
    String TWO_DECIMAL_NUMBER = "^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$";
    String NO = "^[A-Za-z0-9]+$";
    String DATE_FORMAT = "^\\d{4}\\-(0[1-9]|1[012])\\-(0[1-9]|[12][0-9]|3[01])$";
    String YEAR_MONTH_DATE_FORMAT = "^\\d{4}\\-(0[1-9]|1[012])$";
    String GENDER = "^[男|女]{1}$";

    String RESTRICTION = "^(允许|禁止|)$";

    String DORM = "^(住校|走读|)$";

    String BUS = "^[否|是]{1}$";

    String PHONE_NUMBER_REGEX = "^1[3-9]\\d{9}$";

}
