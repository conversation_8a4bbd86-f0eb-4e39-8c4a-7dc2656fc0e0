package com.joinus.io.executor.ijxadmin.outerach.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> anpy
 * @create 2024/4/8 14:51
 */
@Getter
@AllArgsConstructor
public enum SchoolTypeEnum {

    ADMIN(1, "admin系统"),
    OUTREACH(2, "走访系统");

    String des;
    Integer code;

    SchoolTypeEnum(Integer code, String des) {
        this.code = code;
        this.des = des;
    }
}
