package com.joinus.io.executor.ijxadmin.appuseinfo.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.joinus.io.executor.ijxadmin.terminal.enums.*;
import com.joinus.io.executor.model.BasePoiResult;
import com.joinus.io.executor.utils.EasyExcelEnumConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class AppUseInfoExportPoiResult extends BasePoiResult {

    @ExcelProperty("地市")
    private String region;


    @ExcelProperty("县区")
    private String regionName;

    @ExcelProperty("学校")
    private String schoolName;

    @ExcelProperty("角色")
    private String userType;

    @ExcelProperty("用户姓名")
    private String userName;

    @ExcelProperty("电话")
    private String telNum;

    @ExcelProperty("登录时间")
    private String loginTime;


    @ExcelProperty("平台")
    private String platformType;

    @ExcelProperty("版本")
    private String version;

    @ExcelProperty("是否首次登录")
    private String firstLogin;

    @ExcelProperty("登录方式")
    private String loginType;

    @ExcelProperty("app类型")
    private String appType;


}
