plugins {
    id 'java'
    id "org.springframework.boot"
    id "com.palantir.docker" version "0.32.0"
}
group 'com.joinus.basic'
version 'io-executor-1.0.64'

repositories {
    mavenCentral()
}

ext {
    set('dockerRepo', 'harbor.ijx.icu')
    if (!project.hasProperty('dockerPrefix')) {
        dockerPrefix = 'ijx'
    }
}

dependencies {

    implementation project(':common')
//    implementation 'org.springframework.cloud:spring-cloud-starter-gateway'
    //由于springcloud2020弃用了Ribbon，因此Alibaba在2021版本nacos中删除了Ribbon的jar包，因此无法通过lb路由到指定微服务，出现了503情况。
    //所以只需要引入springcloud loadbalancer包即可
//    implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'
    implementation "com.alibaba:easyexcel:3.0.5"
    implementation 'com.alibaba:fastjson:1.2.75'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'cn.easyproject:orai18n:********.0'
    implementation 'com.qiniu:qiniu-java-sdk:7.7.0'
    implementation project(':dao')
    implementation 'net.coobird:thumbnailator:0.4.8'
    implementation 'org.apache.ant:ant:1.8.2'
    implementation("io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:1.26.0")

}


configurations.all {
//    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-web'
}

bootJar {
    launchScript()
    archiveName "snake-basic-io-executor.jar"
}
bootRun {
    jvmArgs = ['-Duser.timezone=GMT+8']
}
dockerPrepare.dependsOn(bootJar)
docker {
    name "${dockerRepo}/${dockerPrefix}/snake-basic-io-executor"
    tag 'taskLatest', "${dockerRepo}/${dockerPrefix}/snake-basic-io-executor:latest"
    tag 'taskVersion', "${dockerRepo}/${dockerPrefix}/snake-basic-io-executor:${version}"
    dockerfile file('Dockerfile')
    copySpec.from("build/libs").into("./")
    buildArgs([BUILD_VERSION: 'version'])
}