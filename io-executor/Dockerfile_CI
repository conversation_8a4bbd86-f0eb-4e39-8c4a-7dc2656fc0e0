FROM ijx-registry.cn-beijing.cr.aliyuncs.com/ijx-public/openjdk11:alpine-jre
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories \
    && apk add tzdata openssl \
    && apk add --update ttf-dejavu fontconfig && rm -rf /var/cache/apk/* \
    && apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
VOLUME /tmp
COPY build/libs/snake-io-executor.jar snake-basic-io-executor.jar
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/snake-basic-io-executor.jar"]