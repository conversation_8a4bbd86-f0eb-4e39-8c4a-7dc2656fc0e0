FROM registry.cn-beijing.aliyuncs.com/ijx-public/opentelemetry-javaagent:1.26.0 as opentelemetry
FROM ijx-registry.cn-beijing.cr.aliyuncs.com/ijx-public/openjdk11:alpine-jre
RUN apk add --update ttf-dejavu fontconfig && rm -rf /var/cache/apk/*
RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
VOLUME /tmp
ADD snake-basic-io-executor.jar snake-basic-io-executor.jar
COPY --from=opentelemetry / /
CMD ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/snake-basic-io-executor.jar"]