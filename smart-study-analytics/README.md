# Smart Study Analytics

## 项目简介

Smart Study Analytics 是一套基于 Spring Boot、MyBatis-Plus 的学业统计分析服务，支持对班级、年级、学生、每道题目和知识点的考试数据进行统计分析，输出多维度的正确率和参与人数等核心指标。

## 技术栈
- Spring Boot 3.x
- MyBatis-Plus
- PostgreSQL
- Redis
- Lombok
- Knife4j-Swagger (API 文档)

## 数据库主要表结构

### 1. 班级考试统计表 `class_exam_statistics`
| 字段名         | 类型           | 说明         |
|---------------|---------------|--------------|
| id            | bigserial PK   | 主键         |
| class_id      | bigint         | 班级ID       |
| grade_id      | bigint         | 年级ID       |
| correct_rate  | numeric(5,2)   | 正确率       |
| exam_id       | uuid           | 试卷ID       |
| student_count | integer        | 参与人数     |
| created_at    | timestamp      | 创建时间     |
| updated_at    | timestamp      | 更新时间     |
| deleted_at    | timestamp      | 删除时间     |

### 2. 个人试卷表 `personal_exam`
| 字段名         | 类型           | 说明         |
|---------------|---------------|--------------|
| id            | bigserial PK   | 主键         |
| student_id    | bigint         | 学生ID       |
| exam_id       | uuid           | 试卷ID       |
| created_at    | timestamp      | 创建时间     |
| updated_at    | timestamp      | 更新时间     |
| deleted_at    | timestamp      | 删除时间     |

### 3. 个人试卷做题结果表 `personal_exam_question`
| 字段名         | 类型           | 说明             |
|---------------|---------------|------------------|
| id            | bigserial PK   | 主键             |
| personal_exam_id | bigint      | 个人试卷ID       |
| question_id   | uuid           | 题目ID           |
| question_type | enum           | 题目类型         |
| result        | enum           | 做题结果         |
| created_at    | timestamp      | 创建时间         |
| updated_at    | timestamp      | 更新时间         |
| deleted_at    | timestamp      | 删除时间         |

### 4. 题目与知识点关系表 `question_knowledge_point`
| 字段名             | 类型         | 说明         |
|--------------------|-------------|--------------|
| id                 | bigserial PK| 主键         |
| exam_id            | uuid        | 试卷ID       |
| question_id        | uuid        | 题目ID       |
| knowledge_point_id | uuid        | 知识点ID     |
| knowledge_point_name | text      | 知识点名称   |
| created_at         | timestamp   | 创建时间     |
| updated_at         | timestamp   | 更新时间     |
| deleted_at         | timestamp   | 删除时间     |

## 统计口径说明

### 题目维度平均法
本系统采用“题目维度平均法”进行班级、年级正确率统计。

#### 1. 班级/年级正确率
- **计算逻辑**：先统计每道题目的正确率（即所有学生对该题的答对率），再将所有题目的正确率相加，最后除以题目数量，得到班级/年级的整体正确率。
- **公式**：
  - 设本次考试共有 N 道题，第 i 道题的正确率为 correctRate_i。
  - 班级/年级正确率 = (Σ correctRate_i) / N
  - 即 (correctRate_1 + correctRate_2 + ... + correctRate_N) / N

#### 2. 每道题目的正确率
- **计算逻辑**：针对每道题，统计所有参与学生的答题结果（正确/错误）。
- **公式**：
  - 设某题参与人数为 Q，答对人数为 C。
  - 题目正确率 = C / Q

#### 3. 知识点正确率
- **计算逻辑**：针对每个知识点，统计所有涉及该知识点的题目在所有学生中的答题结果。
- **公式**：
  - 设知识点相关题目总答题次数为 K，总答对次数为 D。
  - 知识点正确率 = D / K

#### 4. 参与考试人数
- **计算逻辑**：直接统计本班/本年级实际有考试分析结果的学生数量。
- **公式**：
  - 参与人数 = 统计分析结果表中，班级/年级+考试id条件下的学生数

### 代码链说明
- `updateStatistics()` 获取班级/年级分析结果，分别调用 `calculateQuestionCorrectRate()` 计算每道题的正确率。
- 在 `calculateQuestionCorrectRate()` 内部，遍历每道题，统计所有学生的答题正确率。
- 班级/年级的总正确率是所有题目的正确率平均值，而不是学生的平均正确率。

## 启动方式
1. 配置数据库连接、Redis等环境变量。
2. 使用IDE或命令行运行Spring Boot主程序。

```bash
./mvnw spring-boot:run
```

## 接口文档
- 访问 `http://<host>:<port>/doc.html` 查看Swagger/Knife4j自动生成的API文档。

## 贡献指南
1. Fork 本项目并新建分支进行开发。
2. 提交 PR 前请确保代码通过本地测试。
3. 遵循统一的代码规范和分层架构。

## 联系方式
如有问题或建议，请联系项目维护者。