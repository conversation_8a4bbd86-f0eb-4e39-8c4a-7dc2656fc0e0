FROM registry.cn-beijing.aliyuncs.com/ijx-public/opentelemetry-javaagent:1.26.0 as opentelemetry
FROM ijx-registry.cn-beijing.cr.aliyuncs.com/ijx-public/openjdk11:alpine-jre
RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata \
    && mkdir -p /app/fonts \
    && chmod -R 755 /app/fonts
COPY STFANGSO.TTF /app/fonts/
VOLUME /tmp
COPY smart-study-analytics.jar smart-study-analytics.jar
COPY --from=opentelemetry / /
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/smart-study-analytics.jar"]