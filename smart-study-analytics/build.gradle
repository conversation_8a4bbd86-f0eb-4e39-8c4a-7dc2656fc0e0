plugins {
    id 'java'
    id "org.springframework.boot"
    id "com.palantir.docker" version "0.32.0"
}

group 'com.jounus.basic'
version 'smart-study-analytics-1.2.26'

ext {
    set('dockerRepo', 'harbor.ijx.icu')
    if (!project.hasProperty('dockerPrefix')) {
        dockerPrefix = 'ijx'
    }
}
dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.7.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.7.0'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    implementation project(':common')
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'org.projectlombok:lombok'

    implementation 'org.postgresql:postgresql:42.7.5'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'com.itextpdf:itext7-core:7.1.9'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.0'
    implementation 'com.google.zxing:core:3.5.2'
    implementation 'com.google.zxing:javase:3.5.2'
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.17.4'
    implementation 'com.aliyun:ocr_api20210707:3.1.2'
    implementation 'com.aliyun:sts20150401:1.1.6'
    implementation 'com.openhtmltopdf:openhtmltopdf-core:1.0.10'
    implementation 'com.openhtmltopdf:openhtmltopdf-pdfbox:1.0.10'
    implementation 'net.sourceforge.nekohtml:nekohtml:1.9.22' // 添加 XML 解析器
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'com.sun.mail:javax.mail:1.6.2' //
    implementation('io.jsonwebtoken:jjwt:0.6.0') {
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-databind'
    }
    implementation('com.alibaba:easyexcel:4.0.3') {
        exclude group: 'org.apache.poi', module: 'poi-ooxml-schemas'
    }
    implementation 'org.apache.poi:poi:5.4.0'
    implementation 'org.apache.poi:poi-ooxml:5.4.0'

}

test {
    useJUnitPlatform()
}

bootJar {
    launchScript()
    archiveName "smart-study-analytics.jar"
}
dockerPrepare.dependsOn(bootJar)
docker {
    name "${dockerRepo}/${dockerPrefix}/smart-study-analytics"
    tag 'taskLatest', "${dockerRepo}/${dockerPrefix}/smart-study-analytics:latest"
    tag 'taskVersion', "${dockerRepo}/${dockerPrefix}/smart-study-analytics:${version}"
    dockerfile file('Dockerfile')
    files 'build/libs/smart-study-analytics.jar', 'src/main/resources/static/fonts/STFANGSO.TTF'
    buildArgs([BUILD_VERSION: 'version'])
}