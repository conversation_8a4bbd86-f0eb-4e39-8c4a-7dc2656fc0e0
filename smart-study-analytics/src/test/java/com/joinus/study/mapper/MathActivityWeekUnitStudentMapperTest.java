package com.joinus.study.mapper;

import com.joinus.study.model.po.MathActivityWeekUnitPo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试 MathActivityWeekUnitStudentMapper 的 selectExamTypeByExamIds 方法
 */
@SpringBootTest
@ActiveProfiles("test")
public class MathActivityWeekUnitStudentMapperTest {

    @Resource
    private MathActivityWeekUnitStudentMapper mathActivityWeekUnitStudentMapper;

    @Test
    public void testSelectExamTypeByExamIds() {
        // 准备测试数据 - 使用一些示例UUID
        List<UUID> examIds = Arrays.asList(
            UUID.fromString("123e4567-e89b-12d3-a456-************"),
            UUID.fromString("123e4567-e89b-12d3-a456-************")
        );

        try {
            // 执行查询
            List<MathActivityWeekUnitPo> result = mathActivityWeekUnitStudentMapper.selectExamTypeByExamIds(examIds);
            
            // 验证查询能够正常执行，不抛出参数异常
            assertNotNull(result);
            System.out.println("查询成功，返回结果数量: " + result.size());
            
        } catch (Exception e) {
            // 如果还是参数错误，测试失败
            if (e.getMessage().contains("未设定参数值")) {
                fail("SQL参数绑定仍然有问题: " + e.getMessage());
            }
            // 其他异常（如数据不存在）是可以接受的
            System.out.println("查询执行完成，异常信息: " + e.getMessage());
        }
    }

    @Test
    public void testSelectExamTypeByExamIdsWithEmptyList() {
        // 测试空列表的情况
        List<UUID> examIds = Arrays.asList();

        try {
            List<MathActivityWeekUnitPo> result = mathActivityWeekUnitStudentMapper.selectExamTypeByExamIds(examIds);
            assertNotNull(result);
            assertTrue(result.isEmpty());
            System.out.println("空列表查询成功");
            
        } catch (Exception e) {
            System.out.println("空列表查询异常: " + e.getMessage());
        }
    }

    @Test
    public void testSelectExamTypeByExamIdsWithSingleItem() {
        // 测试单个元素的情况
        List<UUID> examIds = Arrays.asList(
            UUID.fromString("123e4567-e89b-12d3-a456-************")
        );

        try {
            List<MathActivityWeekUnitPo> result = mathActivityWeekUnitStudentMapper.selectExamTypeByExamIds(examIds);
            assertNotNull(result);
            System.out.println("单个元素查询成功，返回结果数量: " + result.size());
            
        } catch (Exception e) {
            if (e.getMessage().contains("未设定参数值")) {
                fail("单个元素SQL参数绑定仍然有问题: " + e.getMessage());
            }
            System.out.println("单个元素查询异常: " + e.getMessage());
        }
    }
}
