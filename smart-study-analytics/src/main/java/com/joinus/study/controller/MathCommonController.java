package com.joinus.study.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.joinus.common.common.BaseController;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.ApiResult;
import com.joinus.dao.po.SchoolInfoPO;
import com.joinus.study.model.CurrentUser;
import com.joinus.study.model.bo.JumpUrlBo;
import com.joinus.study.model.entity.MathPageViewTracking;
import com.joinus.study.model.entity.MathSchoolExam;
import com.joinus.study.model.param.ImportSchoolExamParam;
import com.joinus.study.model.param.MathMembershipNoticeParam;
import com.joinus.study.model.param.MathPageViewTrackingParam;
import com.joinus.study.model.vo.MathSchoolExamVo;
import com.joinus.study.service.*;
import com.joinus.study.util.CurrentUserHolder;
import com.joinus.study.utils.AliOssUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;


@RestController
@AllArgsConstructor
@RequestMapping("/math/common")
@Slf4j
@Api(value = "数学-通用相关接口",  tags = "数学-通用相关接口")
public class MathCommonController extends BaseController {


    @Autowired
    private MathPageViewTrackingService mathPageViewTrackingService;

    @ApiOperation(value = "保存页面浏览数据")
    @PostMapping("/page-view-trackings")
    public ApiResult<String> savePageViewTracking(@RequestBody @Valid MathPageViewTrackingParam mathPageViewTrackingParam) {
        CurrentUser currentUser = CurrentUserHolder.getCurrentUser();
        boolean save = mathPageViewTrackingService.addTracking(mathPageViewTrackingParam, currentUser);
        if (save) {
            return ApiResult.success("保存成功");
        }
        return ApiResult.failed("保存失败");
    }

}
