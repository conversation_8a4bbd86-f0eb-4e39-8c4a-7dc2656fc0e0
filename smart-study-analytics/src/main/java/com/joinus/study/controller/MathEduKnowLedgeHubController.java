package com.joinus.study.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.annotation.RequiresMathMembership;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.model.dto.ExamQuestionInfoListData;
import com.joinus.study.model.dto.QuestionCoordinateDto;
import com.joinus.study.model.dto.QuestionDeleteDto;
import com.joinus.study.model.dto.QuestionDetailDTO;
import com.joinus.study.model.enums.ExampleQuestionEnum;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.EduKnowLedgeHubBusinessService;
import com.joinus.study.service.ExamAnalyzeResultService;
import com.joinus.study.util.EnumUtil;
import com.joinus.study.utils.AliOssUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * @Description: edu-knowledge-hub （AI）项目关联业务
 * @Author: anpy
 * @date: 2025/3/12 10:41
 */
@RestController
@RequestMapping("/edu-knowledge-hub")
@Api(tags = "切题、解析")
@Slf4j
public class MathEduKnowLedgeHubController {

    @Resource
    private EduKnowLedgeHubBusinessService eduKnowLedgeHubBusinessService;
    @Resource
    private ExamAnalyzeResultService examAnalyzeResultService;
    @Resource
    private AliOssUtils aliOssUtils;
    @GetMapping("/question-coordinate")
    @ApiOperation(value = "查询题目坐标信息", notes = "查询题目坐标信息", response = ApiResult.class)
    public ApiResult<QuestionCoordinateVo.DataDTO> questionCoordinateInfo(@Valid QueryQuestionCoordinateParam param) throws JsonProcessingException {
        QuestionCoordinateVo questionCoordinateVo = eduKnowLedgeHubBusinessService.questionCoordinateInfo(param);
        return ApiResult.success(questionCoordinateVo.getData());
    }

    @PostMapping("/img/cut")
    @ApiOperation(value = "根据图片和坐标切题", notes = "根据图片和坐标切题", response = ApiResult.class)
    public ApiResult<CutImageFromPositionsVo> cutImageFromPositions(@RequestBody @Valid CutImageFromPositionsParam param) {
        return ApiResult.success(eduKnowLedgeHubBusinessService.cutImageFromPositions(param));
    }

    @PostMapping(value = "/question-single-analysis-results", produces = "text/event-stream;charset=UTF-8")
    @ApiOperation(value = "查询单题解析结果", notes = "查询单题解析结果", response = ApiResult.class)
    public Flux<String> querySingleQuestionAnalysisResults(@RequestBody @Valid SolveQuestionFromImgParam param) throws JsonProcessingException {
        return eduKnowLedgeHubBusinessService.querySingleQuestionAnalysisResults(param);
    }

    @PostMapping(value = "/ai-question-single-analysis-results", produces = "text/event-stream;charset=UTF-8")
    @ApiOperation(value = "查询AI单题解析结果", notes = "查询AI单题解析结果", response = ApiResult.class)
    public Flux<String> queryAiSingleQuestionAnalysisResults(@RequestBody @Valid SolveQuestionFromImgParam param) {
        return eduKnowLedgeHubBusinessService.getAiResultFluxData(param);
    }

    @PostMapping("/knowledge-points")
    @ApiOperation(value = "获取知识点和难度")
    public KnowledgePointsVO knowledgePoints(@Valid KnowledgePointsParams param) {
        return eduKnowLedgeHubBusinessService.knowledgePoints(param, null, param.getPublisher());
    }

    /**
     * 流式输出后将结果异步存入数据库
     */
    @PostMapping("/text/stream")
    public Flux<String> textStream(@RequestBody SolveQuestionFromImgParam param) {
        return eduKnowLedgeHubBusinessService.chatStream(param);
    }

    /**
     * 模拟流式输出
     */
    @PostMapping("/text/stream/mock")
    public Flux<String> textStreamMock(@RequestBody SolveQuestionFromImgParam param) {
        return eduKnowLedgeHubBusinessService.chatStreamMock(param);
    }

    @GetMapping("/question-multi-analysis-results")
    @ApiOperation(value = "查询多题解析结果", notes = "查询多题解析结果", response = ApiResult.class)
    public Flux<String> queryMultiQuestionAnalysisResults(@Valid SolveQuestionFromImgParam param) {
        return eduKnowLedgeHubBusinessService.queryMultiQuestionAnalysisResults(param);
    }

    @PostMapping("/multi-image-analysis-results")
    @ApiOperation(value = "多图片切题", notes = "多图片切题", response = ApiResult.class)
    public MultiImageAnalyticsResultVo queryMultiImageAnalyticsResult(@Valid MultiImageAnalysicsParam param) {
        return eduKnowLedgeHubBusinessService.queryMultiImageAnalyticsResult(param);
    }

    @PostMapping("/exam-analysis-question-label")
    @ApiOperation(value = "试卷诊断-试卷题目标注", notes = "试卷诊断试卷题目标注", response = ApiResult.class)
    public ExamAnalysisQuestionLabelVo examAnalysisQuestionLabel(@Valid ExamAnalysisQuestionLabelParam param) {
        return eduKnowLedgeHubBusinessService.examAnalysisQuestionLabel(param);
    }

    @PostMapping("/check-exam-existence")
    @ApiOperation(value = "试卷诊断-判断试卷是否存在", notes = "试卷诊断-判断试卷是否存在", response = ApiResult.class)
    public CheckExamExsitenceVo checkExamExistence(@RequestBody @Valid CheckExamExsitenceParam param) {
        return eduKnowLedgeHubBusinessService.checkExamExistence(param);
    }

    @PostMapping("/check-exam-existence/v2")
    @ApiOperation(value = "试卷诊断-判断试卷是否存在", notes = "试卷诊断-判断试卷是否存在", response = ApiResult.class)
    public ApiResult checkExamExistenceV2(@RequestBody @Valid CheckExamExsitenceParam param) {
        return ApiResult.success(eduKnowLedgeHubBusinessService.checkExamExistenceV2(param));
    }

    @PostMapping("/erase-pen-marks")
    @ApiOperation(value = "擦除手写笔迹", notes = "擦除手写笔迹", response = ApiResult.class)
    public EraseHandwritingFromQuestionVo erasePenMarks(@RequestBody @Valid EraseHandwritingFromQuestionParam param) {
        return eduKnowLedgeHubBusinessService.eraseHandwritingFromQuestion(param);
    }

    @GetMapping("/flexibly-generating")
    @ApiOperation(value = "举一反三生成题目-流式返回", notes = "举一反三生成题目-流式返回", response = ApiResult.class)
    public Flux<String> flexiblyGenerating(@Valid FlexiblyQuestionParam param) {
        return eduKnowLedgeHubBusinessService.flexiblyGenerating(param);
    }

    @GetMapping("/flexibly-generated")
    @ApiOperation(value = "举一反三生成题目-正常返回", notes = "举一反三生成题目-正常返回", response = ApiResult.class)
    public ApiResult<FlexiblyGeneratedVo> flexiblyGenerated(@Valid FlexiblyQuestionParam param) {
        FlexiblyGeneratedVo flexiblyGeneratedVo = eduKnowLedgeHubBusinessService.flexiblyGenerated(param);
        if(DataUtils.isNotEmpty(flexiblyGeneratedVo)){
            return ApiResult.success(flexiblyGeneratedVo);
        }else{
            return ApiResult.failed("举一反三题目生成失败！");
        }
    }

    @GetMapping("/oss/token")
    @ApiOperation(value = "获取上传OSS token", notes = "获取上传OSS token", response = ApiResult.class)
    public OssTokenVo ossToken(@Valid OssTokenParam param) {
        return eduKnowLedgeHubBusinessService.ossToken(param);
    }

    @PostMapping("/check/exist-graphics")
    @ApiOperation(value = "判断题目是否存在图片", response = ApiResult.class)
    public CheckExistGraphicsVo checkExistGraphics(@RequestBody @Valid CheckExistGraphicsParam param) {
        return eduKnowLedgeHubBusinessService.checkExistGraphics(param);
    }

    @GetMapping("/presigned-url")
    @ApiOperation(value = "获取图片临时链接", response = ApiResult.class)
    public PresignedUrlVo presignedUrl(@Valid PresignedUrlParam param) {
        return eduKnowLedgeHubBusinessService.presignedUrl(param);
    }

    @GetMapping("/special-training")
    @ApiOperation(value = "获取薄弱知识点", response = ApiResult.class)
    public SpecialTrainingVo specialTraining(@RequestParam Long examAnalyzeResultId) {
        CommonResponse.ERROR.assertNotNull(examAnalyzeResultId, "试卷解析结果id不能为空");
        return examAnalyzeResultService.specialTraining(examAnalyzeResultId);
    }

    @GetMapping("/special-training-pdf")
    @ApiOperation(value = "薄弱知识点pdf", notes = "薄弱知识点pdf")
    public SpecialTrainingPdfVo specialTrainingPdf(@RequestParam Long examAnalyzeResultId,
                                                   @RequestParam Boolean selectAnswer,
                                                   @RequestParam Boolean selectQuestion,
                                                   @RequestParam UUID pdfUUID) {
        CommonResponse.ERROR.assertNotNull(examAnalyzeResultId, "试卷解析id不能为空");
        CommonResponse.ERROR.assertNotNull(selectQuestion, "是否选择题目不能为空");
        CommonResponse.ERROR.assertNotNull(selectAnswer, "是否选择答案不能为空");
        CommonResponse.ERROR.assertNotNull(pdfUUID, "pdfUUID不能为空");
        SpecialTrainingPdfParam param = SpecialTrainingPdfParam.builder()
                .examAnalyzeResultId(examAnalyzeResultId)
                .selectQuestion(selectQuestion)
                .selectAnswer(selectAnswer)
                .pdfUUID(pdfUUID)
                .build();
        return examAnalyzeResultService.specialTrainingPdf(param);
    }

    @GetMapping("/special-training-pdf/preview")
    @ApiOperation(value = "薄弱知识点pdf", notes = "薄弱知识点pdf")
    public SpecialTrainingPdfVo specialTrainingPdfPreview(@RequestParam Long examAnalyzeResultId,
                                                   @RequestParam Boolean selectAnswer,
                                                   @RequestParam Boolean selectQuestion,
                                                   @RequestParam UUID pdfUUID) {
        CommonResponse.ERROR.assertNotNull(examAnalyzeResultId, "试卷解析id不能为空");
        CommonResponse.ERROR.assertNotNull(selectQuestion, "是否选择题目不能为空");
        CommonResponse.ERROR.assertNotNull(selectAnswer, "是否选择答案不能为空");
        CommonResponse.ERROR.assertNotNull(pdfUUID, "pdfUUID不能为空");
        SpecialTrainingPdfParam param = SpecialTrainingPdfParam.builder()
                .examAnalyzeResultId(examAnalyzeResultId)
                .selectQuestion(selectQuestion)
                .selectAnswer(selectAnswer)
                .pdfUUID(pdfUUID)
                .build();
        return examAnalyzeResultService.specialTrainingPdfPreview(param);
    }

    @PostMapping("/create-exam")
    @ApiOperation(value = "生成试卷")
    public ApiResult<ExamQuestionInfoListData> createExam(@RequestBody @Valid CreateExamParam param) {
        return ApiResult.success(eduKnowLedgeHubBusinessService.createExam(param));
    }

    @GetMapping("/example-questionPicture")
    @ApiOperation(value = "示例题图", notes = "示例题图", response = ApiResult.class)
    public ApiResult<String> exampleQuestionPicture(@RequestParam ExampleQuestionEnum type) {
        String exampleQuestionPicture = eduKnowLedgeHubBusinessService.exampleQuestionPicture(type);
        return ApiResult.success(exampleQuestionPicture);
    }

    @GetMapping("/example-question")
    @ApiOperation(value = "示例题相关全信息", notes = "示例相关全信息", response = ApiResult.class)
    public ApiResult<ExampleQuestionJsonVo> exampleQuestion(@RequestParam ExampleQuestionEnum type) throws JsonProcessingException {
        ExampleQuestionJsonVo questionJsonVo = eduKnowLedgeHubBusinessService.exampleQuestion(type);
        return ApiResult.success(questionJsonVo);
    }

    @GetMapping("/publisher-list")
    @ApiOperation(value = "出版商列表", notes = "出版商列表", response = ApiResult.class)
    public ApiResult<List<PublisherVo>> publisherList() {
        return ApiResult.success(EnumUtil.getPublisherList());
    }

    @GetMapping("/math/exams/{examId}")
    @ApiOperation(value = "根据examId获取试卷详情", notes = "根据examId获取试卷详情", response = ApiResult.class)
    public ApiResult<MathExamVO> getExamsByExamId(@PathVariable("examId") UUID examId) {
        MathExamVO mathExamVO = eduKnowLedgeHubBusinessService.getExamsByExamId(examId);
        return ApiResult.success(mathExamVO);
    }

    @PostMapping("/ability/coordinate-point/multi")
    @ApiOperation(value = "根据图片解析题目坐标", notes = "根据图片解析题目坐标", response = ApiResult.class)
    public ApiResult<QuestionCoordinateDto.DataDTO> coordinatePointMulti(@RequestBody @Valid QueryQuestionCoordinateParam param) {
        QuestionCoordinateDto questionCoordinateDto = eduKnowLedgeHubBusinessService.coordinatePointMulti(param);
        return ApiResult.success(questionCoordinateDto.getData());
    }

    @GetMapping("/math/exams/questions/{examId}")
    @ApiOperation(value = "获取试卷题目列表", notes = "获取试卷题目列表", response = ApiResult.class)
    public ApiResult<List<MathExamQuestionVO>> getQuestionsByExamId(@PathVariable("examId") UUID examId) {
        List<MathExamQuestionVO> questions = eduKnowLedgeHubBusinessService.getQuestionsByExamId(examId);
        return ApiResult.success(questions);
    }

    @PostMapping("/math/exams/{examId}/questions")
    @ApiOperation(value = "新增试卷题目", notes = "新增试卷题目", response = ApiResult.class)
    public ApiResult addQuestion(@PathVariable("examId") UUID examId,
                                 @RequestBody @Valid AddExamQuestionParam param) {
        QuestionDetailDTO questionDetailDTO = eduKnowLedgeHubBusinessService.addQuestion(examId, param);
        if (questionDetailDTO != null && questionDetailDTO.getCode() != 200){
            return ApiResult.failed(questionDetailDTO.getMessage());
        }
        return ApiResult.success(questionDetailDTO.getData());
    }

    @PutMapping("/math/exams/{examId}/questions/{questionId}")
    @ApiOperation(value = "编辑试卷题目", notes = "编辑试卷题目", response = ApiResult.class)
    public ApiResult editQuestion(@PathVariable("examId") UUID examId,
                                  @PathVariable("questionId") UUID questionId,
                                  @RequestBody @Valid AddExamQuestionParam param) {
        QuestionDetailDTO questionDetailDTO = eduKnowLedgeHubBusinessService.editQuestion(examId, questionId, param);
        if (questionDetailDTO != null && questionDetailDTO.getCode() != 200){
            return ApiResult.failed(questionDetailDTO.getMessage());
        }
        return ApiResult.success(questionDetailDTO.getData());
    }

    @DeleteMapping("/math/exams/{examId}/questions/{questionId}")
    @ApiOperation(value = "删除试卷题目", notes = "删除试卷题目", response = ApiResult.class)
    public ApiResult deleteQuestions(@PathVariable("examId") UUID examId,
                                     @PathVariable("questionId") UUID questionId) {
        QuestionDeleteDto questionDeleteDto = eduKnowLedgeHubBusinessService.deleteQuestions(examId,questionId);
        if (questionDeleteDto != null && questionDeleteDto.getCode() != 200){
            return ApiResult.failed(questionDeleteDto.getMessage());
        }
        return ApiResult.success(questionDeleteDto.getMessage());
    }

    @GetMapping("/questionType-list")
    @ApiOperation(value = "题目类型列表", notes = "题目类型列表", response = ApiResult.class)
    public ApiResult<List<PublisherVo>> questionTypeList() {
        return ApiResult.success(EnumUtil.getQuestionTypeList());
    }


    @GetMapping("/pdf/preview")
    @ApiOperation(value = "pdf预览链接", notes = "生成pdf预览链接", response = ApiResult.class)
    @ApiImplicitParam(name = "key", value = "key", required = true, dataType = "String")
    public ApiResult<String> getQuestionTypes(@RequestParam String key) {
        String pdfPreviewUrl = aliOssUtils.generatePdfPreview(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket(), key);
        return ApiResult.success(pdfPreviewUrl);
    }

}
