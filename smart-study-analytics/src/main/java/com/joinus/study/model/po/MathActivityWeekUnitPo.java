package com.joinus.study.model.po;

import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathActivityWeekUnitPo implements Serializable {

    private Long weekUnidId;

    private MathActivityWeekUnitTypeEnum type; //类型  SECTION_TEST，CHAPTER_TEST，COMPREHENSIVE_TEST

    private UUID examId;
}
