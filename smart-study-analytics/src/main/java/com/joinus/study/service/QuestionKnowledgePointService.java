package com.joinus.study.service;

import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.vo.KnowledgePointStatisticsVo;
import com.joinus.study.model.vo.KnowledgePointsVO;
import com.joinus.study.model.vo.StudentInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【question_knowledge_point】的数据库操作Service
* @createDate 2025-03-11 09:48:14
*/
public interface QuestionKnowledgePointService extends IService<QuestionKnowledgePoint> {

    /**
     * 根据问题id查询关联知识点
     * @param questionId
     * @return
     */
    List<QuestionKnowledgePoint> getQuestionKnowledgePointList(@Param("questionId") UUID questionId);

    List<KnowledgePointStatisticsVo> selectKnowledgePointStatisticsByExamId(Long id);

    List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> getListByExamIdAndQuestionId(UUID examId, UUID questionId);

    List<StudentInfoVo> getStudentIds(Long studentId);

    List<KnowledgePointStatisticsVo> getClassKnowledgePointStatics(List<Long> studentIds, List<Long>classIds,UUID examId);

    List<QuestionKnowledgePoint> listKnowledgePointsByExamIdAndPublisher(UUID examId, PublisherEnum publisher);

    void rebuildExamQuestionPublisher();
}
