package com.joinus.study.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.study.mapper.ReadingQuestionAnswersMapper;
import com.joinus.study.model.entity.ReadingQuestionAnswers;
import com.joinus.study.service.ReadingQuestionAnswersService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

@AllArgsConstructor
@Service("readingQuestionAnswersService")
@Slf4j
public class ReadingQuestionAnswersServiceImpl extends ServiceImpl<ReadingQuestionAnswersMapper, ReadingQuestionAnswers>
        implements ReadingQuestionAnswersService {
    @Override
    public ReadingQuestionAnswers getByQuestionId(UUID questionId) {
        LambdaQueryWrapper<ReadingQuestionAnswers> wrapper = Wrappers.lambdaQuery(ReadingQuestionAnswers.class);
        wrapper.eq(Objects.nonNull(questionId), ReadingQuestionAnswers::getQuestionId, questionId);
        wrapper.eq(ReadingQuestionAnswers::getAnsweringFormulaId,null);
        return this.getOne(wrapper);
    }
}
