package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ReadingPersonalPlan;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.model.param.ReadingPlanPageParam;
import com.joinus.study.model.vo.ReadingPersonalPlanVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/28 10:46
 * description：阅读提分训练营首页相关service
 */
public interface ReadingPersonalHomePageService {

    /**
     * 学生年级学期教材信息保存/编辑
     * @param vo
     */
    void textbookAdd(ReadingPersonalUser vo) throws Exception;

    /**
     * 学生年级学期教材信息查询
     * @param studentId
     * @return
     */
    ReadingPersonalUser getTextbookInfo(Long studentId) throws Exception;

    /**
     * 创建阅读计划
     * @param vo
     */
    void planAdd(ReadingPersonalPlan vo) throws Exception;

    /**
     * 阅读计划详情
     * @param planId
     * @return
     */
    ReadingPersonalPlanVo getPlanInfoById(Long planId) throws Exception;

    /**
     * 首页阅读计划三条展示未开始或进行中
     * @param studentId
     * @return
     */
    List<ReadingPersonalPlanVo> planUnderWayList(Long studentId) throws Exception;

    /**
     * 阅读计划分页列表
     * @param pageParam
     * @return
     */
    Page<ReadingPersonalPlanVo> planPages(ReadingPlanPageParam pageParam);

    /**
     * 获取薄弱点
     * @return
     */
    Map<String, List<Map<String, Object>>> getKnowledgePointsInfo(Long studentId) throws Exception;
}
