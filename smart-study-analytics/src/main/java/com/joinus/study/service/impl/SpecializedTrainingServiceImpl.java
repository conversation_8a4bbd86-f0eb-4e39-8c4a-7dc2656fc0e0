package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.enums.ApiResultCode;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.*;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.model.vo.CreateQuestionByKnowledgeVo;
import com.joinus.study.model.vo.SpecializedTrainingNewResultVo;
import com.joinus.study.model.vo.SpecializedTrainingNewResultVoV2;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.service.GradeService;
import com.joinus.study.service.SpecializedTrainingService;
import com.joinus.study.utils.AliOssUtils;
import com.joinus.study.utils.PDFToImageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: 专项训练服务
 * @Author: anpy
 * @date: 2025/4/28 17:34
 */
@Service
@Slf4j
public class SpecializedTrainingServiceImpl implements SpecializedTrainingService {

    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private PersonalExamMapper personalExamMapper;
    @Resource
    private MathExamsMapper mathExamsMapper;
    @Resource
    private MathQuestionsMapper  mathQuestionsMapper;
    @Value("${ijx.ink.q.math:https://ijx.ink/q/math/}")
    private String inkQMath;
    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private GradeService gradeService;
    @Resource
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private MathActivityWeekUnitStudentMapper mathActivityWeekUnitStudentMapper;
    @Resource
    private MathActivityStudentMapper mathActivityStudentMapper;
    private static Pattern imgTagPattern = Pattern.compile("<img[^>]*src=\"\"[^>]*>");
    private static Pattern dataS3EnumPattern = Pattern.compile("data-s3-enum=\"([^\"]*)\"");
    private static Pattern dataS3KeyPattern = Pattern.compile("data-s3-key=\"([^\"]*)\"");

    @Override
    public SpecializedTrainingNewResultVo createQuestionByKnowledgeAndQuestionType(CreateQuestionByKnowledgeParam param) {
        try {
            // 调用 AI 生成题目
            CreateQuestionByKnowledgeVo createQuestionByKnowledgeVo = eduKnowledgeHubService.createQuestionByKnowledgeAndQuestionType(param);
            CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
            CommonResponse.ERROR.assertCollNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

            // 创建新的返回结果对象
            SpecializedTrainingNewResultVo newResultVo = new SpecializedTrainingNewResultVo();

            // 初始化统计数据
            int totalQuestions = 0;
            int totalQuestionTypes = 0;
            int totalKnowledgePoints = 0;

            QuestionSortNo sortNo = new QuestionSortNo();

            // 处理知识点和题型数据
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData())) {
                // 创建知识点列表
                List<SpecializedTrainingNewResultVo.KnowledgePointDTO> knowledgePoints = new ArrayList<>();
                // 创建题型列表
                List<SpecializedTrainingNewResultVo.QuestionTypeDTO> questionTypes = new ArrayList<>();
                // 用于去重题型的Map
                Map<String, SpecializedTrainingNewResultVo.QuestionTypeDTO> questionTypeMap = new HashMap<>();

                // 遍历原始数据，提取知识点和题型
                for (CreateQuestionByKnowledgeVo.DataDTO dataDTO : createQuestionByKnowledgeVo.getData()) {
                    // 处理知识点
                    if (StringUtils.isNotBlank(dataDTO.getKnowledgePointId())) {
                        totalKnowledgePoints++;

                        SpecializedTrainingNewResultVo.KnowledgePointDTO knowledgePointDTO = new SpecializedTrainingNewResultVo.KnowledgePointDTO();
                        knowledgePointDTO.setKnowledgePointId(dataDTO.getKnowledgePointId());
                        knowledgePointDTO.setKnowledgePointName(dataDTO.getKnowledgePointName());

                        // 处理知识点下的题目
                        List<UUID> questionIds = new ArrayList<>();
                        if (CollUtil.isNotEmpty(dataDTO.getQuestionIds())) {
                            for (String id : dataDTO.getQuestionIds()) {
                                questionIds.add(UUID.fromString(id));
                            }
                        }
                        knowledgePointDTO.setQuestionIds(questionIds);
                        knowledgePointDTO.setQuestionList(getQuestionList(questionIds, sortNo));

                        // 统计知识点下的题目数量
                        if (CollUtil.isNotEmpty(knowledgePointDTO.getQuestionList())) {
                            for (SpecializedTrainingQuestionDataVo questionVo : knowledgePointDTO.getQuestionList()) {
                                if (questionVo != null && questionVo.getList() != null) {
                                    totalQuestions += questionVo.getList().size();
                                }
                            }
                        }

                        knowledgePoints.add(knowledgePointDTO);
                    }

                    // 处理题型
                    if (CollUtil.isNotEmpty(dataDTO.getQuestionTypes())) {
                        for (CreateQuestionByKnowledgeVo.DataDTO.QuestionTypesDTO typeDTO : dataDTO.getQuestionTypes()) {
                            if (typeDTO != null && StringUtils.isNotBlank(typeDTO.getQuestionTypeId())) {
                                totalQuestionTypes++;

                                // 处理题型，使用Map进行去重
                                SpecializedTrainingNewResultVo.QuestionTypeDTO questionTypeDTO = questionTypeMap.get(typeDTO.getQuestionTypeId());
                                if (questionTypeDTO == null) {
                                    // 如果Map中不存在，创建新的题型对象
                                    questionTypeDTO = new SpecializedTrainingNewResultVo.QuestionTypeDTO();
                                    questionTypeDTO.setQuestionTypeId(typeDTO.getQuestionTypeId());
                                    questionTypeDTO.setQuestionTypeName(typeDTO.getQuestionTypeName());
                                    questionTypeDTO.setQuestionIds(new ArrayList<>());
                                    questionTypeDTO.setQuestionList(new ArrayList<>());
                                    questionTypeMap.put(typeDTO.getQuestionTypeId(), questionTypeDTO);
                                }

                                // 处理题型下的题目
                                List<UUID> questionIds = new ArrayList<>();
                                if (CollUtil.isNotEmpty(typeDTO.getQuestionIds())) {
                                    for (String id : typeDTO.getQuestionIds()) {
                                        UUID uuid = UUID.fromString(id);
                                        questionIds.add(uuid);
                                        questionTypeDTO.getQuestionIds().add(uuid);
                                    }
                                }

                                // 获取题目列表并添加到题型中
                                List<SpecializedTrainingQuestionDataVo> questionList = getQuestionList(questionIds, sortNo);
                                if (CollUtil.isNotEmpty(questionList)) {
                                    questionTypeDTO.getQuestionList().addAll(questionList);

                                    // 统计题型下的题目数量
                                    for (SpecializedTrainingQuestionDataVo questionVo : questionList) {
                                        if (questionVo != null && questionVo.getList() != null) {
                                            totalQuestions += questionVo.getList().size();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 设置知识点列表
                newResultVo.setKnowledgePoint(knowledgePoints);
                // 将题型Map转换为列表并设置
                questionTypes.addAll(questionTypeMap.values());
                newResultVo.setQuestionTypes(questionTypes);
            }

            // 设置统计结果
            newResultVo.setTotalQuestions(totalQuestions);
            newResultVo.setTotalQuestionTypes(totalQuestionTypes);
            newResultVo.setTotalKnowledgePoints(totalKnowledgePoints);

            return newResultVo;
        } catch (Exception e) {
            log.error("createQuestionByKnowledgeAndQuestionType 方法执行出错，参数: {}", param, e);
            throw e;
        }
    }

    @Override
    public SpecializedTrainingNewResultVoV2 createQuestionByKnowledgeAndQuestionTypeV2(CreateQuestionByKnowledgeParamV2 param) {
        try {
            // 调用 AI 生成题目
            CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = eduKnowledgeHubService.createQuestionByKnowledgeAndQuestionTypeV2(param);
            QuestionSortNo sortNo = new QuestionSortNo();
            // 创建返回对象
            SpecializedTrainingNewResultVoV2 newResultVo = new SpecializedTrainingNewResultVoV2();

            // 只计算三个字段：知识点数量、题型数量和题目数量
            int totalKnowledgePoints = 0;
            int totalQuestionTypes = 0;
            int totalQuestions = 0;

            // 1. 计算知识点数量
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
                totalKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().size();
            }

            // 2. 计算题型数量
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
                totalQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().size();
            }

            // 3. 计算题目数量（去重）
            Set<UUID> allQuestionIds = new HashSet<>();

            // 添加知识点中的题目ID
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
                for (CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO knowledgePointDTO : createQuestionByKnowledgeVo.getData().getKnowledgePoints()) {
                    if (CollUtil.isNotEmpty(knowledgePointDTO.getQuestions())) {
                        List<SpecializedTrainingQuestionDataVo> questionList = getQuestionListV2(knowledgePointDTO.getQuestions(), sortNo);
                        knowledgePointDTO.setQuestionList(questionList);
                        knowledgePointDTO.setQuestionIds(knowledgePointDTO.getQuestions().stream().map(CreateQuestionByKnowledgeVoV2.DataDTO.PastQuestionVo::getId).collect(Collectors.toList()));
                        allQuestionIds.addAll(knowledgePointDTO.getQuestionIds());
                    }
                }
            }

            // 添加题型中的题目ID
            if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
                for (CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO questionTypeDTO : createQuestionByKnowledgeVo.getData().getQuestionTypes()) {
                    if (CollUtil.isNotEmpty(questionTypeDTO.getQuestions())) {
                        List<SpecializedTrainingQuestionDataVo> questionList = getQuestionListV2(questionTypeDTO.getQuestions(), sortNo);
                        questionTypeDTO.setQuestionList(questionList);
                        questionTypeDTO.setQuestionIds(questionTypeDTO.getQuestions().stream().map(CreateQuestionByKnowledgeVoV2.DataDTO.PastQuestionVo::getId).collect(Collectors.toList()));
                        allQuestionIds.addAll(questionTypeDTO.getQuestionIds());
                    }
                }
            }

            // 题目总数就是去重后的题目ID数量
            totalQuestions = allQuestionIds.size();

            // 设置统计结果
            newResultVo.setTotalQuestions(totalQuestions);
            newResultVo.setTotalQuestionTypes(totalQuestionTypes);
            newResultVo.setTotalKnowledgePoints(totalKnowledgePoints);

            newResultVo.setKnowledgePoint(createQuestionByKnowledgeVo.getData().getKnowledgePoints());
            newResultVo.setQuestionTypes(createQuestionByKnowledgeVo.getData().getQuestionTypes());

            return newResultVo;
        } catch (Exception e) {
            log.error("createQuestionByKnowledgeAndQuestionTypeV2 方法执行出错，参数: {}", param, e);
            throw e;
        }
    }

    private List<SpecializedTrainingQuestionDataVo> getQuestionList(List<UUID> questionIds, QuestionSortNo finalSortNo) {
        if (CollUtil.isEmpty(questionIds)) {
            return new ArrayList<>();
        }

        List<SpecializedTrainingQuestionVo> collect = questionIds.stream()
                .map(questionId -> {
                    Integer sortNo = finalSortNo.getSortNo();
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(questionId);
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setSortNo(++sortNo);
                    finalSortNo.setSortNo(sortNo);


                    return specializedTrainingQuestionVo;
                })
                .collect(Collectors.toList());
        //collect 根据questionType分组
        List<SpecializedTrainingQuestionDataVo> collect1 = collect.stream()
                .collect(Collectors.groupingBy(SpecializedTrainingQuestionVo::getQuestionType))
                .entrySet().stream()
                .map(entry -> {
                    SpecializedTrainingQuestionDataVo specializedTrainingQuestionDataVo = new SpecializedTrainingQuestionDataVo();
                    specializedTrainingQuestionDataVo.setType(entry.getKey());
                    specializedTrainingQuestionDataVo.setList(entry.getValue());
                    return specializedTrainingQuestionDataVo;
                }).collect(Collectors.toList());
        return collect1;
    }

    private List<SpecializedTrainingQuestionDataVo> getQuestionListV2(List<CreateQuestionByKnowledgeVoV2.DataDTO.PastQuestionVo> questions, QuestionSortNo finalSortNo) {
        if (CollUtil.isEmpty(questions)) {
            return new ArrayList<>();
        }

        List<SpecializedTrainingQuestionVo> collect = questions.stream()
                .map(question -> {
                    Integer sortNo = finalSortNo.getSortNo();
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setSortNo(++sortNo);
                    specializedTrainingQuestionVo.setIsPastExamPaper(question.getIsPastExamPaper());
                    specializedTrainingQuestionVo.setPastExamPaperRegion(question.getPastExamPaperRegion());
                    specializedTrainingQuestionVo.setPastExamPaperYear(question.getPastExamPaperYear());
                    finalSortNo.setSortNo(sortNo);
                    return specializedTrainingQuestionVo;
                })
                .collect(Collectors.toList());
        //collect 根据questionType分组
        List<SpecializedTrainingQuestionDataVo> collect1 = collect.stream()
                .collect(Collectors.groupingBy(SpecializedTrainingQuestionVo::getQuestionType))
                .entrySet().stream()
                .map(entry -> {
                    SpecializedTrainingQuestionDataVo specializedTrainingQuestionDataVo = new SpecializedTrainingQuestionDataVo();
                    specializedTrainingQuestionDataVo.setType(entry.getKey());
                    specializedTrainingQuestionDataVo.setList(entry.getValue());
                    return specializedTrainingQuestionDataVo;
                }).collect(Collectors.toList());
        return collect1;
    }

    @Override
    public String decodeContentV2(String content) {
        if (content == null) {
            return "";
        }

        Matcher imgMatcher = imgTagPattern.matcher(content);
        StringBuffer sb = new StringBuffer();

        while (imgMatcher.find()) {
            String tag = imgMatcher.group();

            // 提取 data-s3-enum 属性（如果存在）
            Matcher enumMatcher = dataS3EnumPattern.matcher(tag);
            String s3Enum = enumMatcher.find() ? enumMatcher.group(1) : "MINIO_EDU_KNOWLEDGE_HUB";  // 默认 aliyun

            OssEnum ossEnum = OssEnum.valueOf(s3Enum);

            // 提取 data-s3-key 属性（必须有）
            Matcher keyMatcher = dataS3KeyPattern.matcher(tag);
            if (!keyMatcher.find()) {
                continue; // 或根据需求作处理
            }
            String s3Key = keyMatcher.group(1);

            // 根据修正后的 s3Enum 和 s3Key 生成新的 src
            PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(PresignedUrlParam.builder()
                    .ossEnum(ossEnum)
                    .ossKey(s3Key)
                    .build());
            String newTag = tag.replace("src=\"\"", "src=\"" + presignedUrlDto.getData().getPresignedUrl() + "\"");

            imgMatcher.appendReplacement(sb, newTag.replace("$", "\\$")); // 注意替换 $ 符号避免干扰
        }
        imgMatcher.appendTail(sb);

        return sb.toString();
    }

    @Override
    public ExamChapterQuestionDetailDto createQuestionByKnowledgeAndQuestionTypeV3(CreateQuestionByKnowledgeParamV2 param) throws BaseException {
        try {
            ExamChapterQuestionDetailDto examChapterQuestionDetailDto = new ExamChapterQuestionDetailDto();
            // 调用 AI 生成题目
            CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = eduKnowledgeHubService.createQuestionByKnowledgeAndQuestionTypeV2(param);
            Set<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOSet = new HashSet<>();
            Set<SpecializedTrainingQuestionVo> questionSet  = new HashSet<>();
            CreateQuestionByKnowledgeVoV2.DataDTO dataDTO = createQuestionByKnowledgeVo.getData();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> knowledgePointsQuestionList = dataDTO.getKnowledgePoints();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> questionTypesQuestionList = dataDTO.getQuestionTypes();
            knowledgePointsQuestionList.forEach(knowledgePointsDTO -> {

                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(knowledgePointsDTO.getGrade());
                bookQuestionDetailDto.setPublisher(knowledgePointsDTO.getPublisher());
                bookQuestionDetailDto.setSemester(knowledgePointsDTO.getSemester());
                bookQuestionDetailDto.setBookStr(PublisherEnum.getPublisherName(knowledgePointsDTO.getPublisher())+formatPublisher(knowledgePointsDTO.getGrade(),  knowledgePointsDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(knowledgePointsDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(knowledgePointsDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(knowledgePointsDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(knowledgePointsDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(knowledgePointsDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamChapterQuestionDetailDto.KnowledgePointsDTO();
                knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                pointsDTOSet.add(knowledgePoint);

                knowledgePointsDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                    specializedTrainingQuestionVo.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                    specializedTrainingQuestionVo.setSectionId(knowledgePointsDTO.getSectionId());
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            questionTypesQuestionList.forEach(questionTypesDTO -> {
                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(questionTypesDTO.getGrade());
                bookQuestionDetailDto.setPublisher(questionTypesDTO.getPublisher());
                bookQuestionDetailDto.setSemester(questionTypesDTO.getSemester());
                bookQuestionDetailDto.setBookStr(PublisherEnum.getPublisherName(questionTypesDTO.getPublisher()) +formatPublisher(questionTypesDTO.getGrade(),  questionTypesDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(questionTypesDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(questionTypesDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(questionTypesDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(questionTypesDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(questionTypesDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.QuestionTypesDTO questionType = new ExamChapterQuestionDetailDto.QuestionTypesDTO();
                questionType.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                questionType.setQuestionTypeName(questionTypesDTO.getQuestionTypeName());
                questionType.setSectionId(questionTypesDTO.getSectionId());
                questionTypesDTOSet.add(questionType);

                questionTypesDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                    specializedTrainingQuestionVo.setQuestionType(questionTypesDTO.getQuestionTypeName());
                    specializedTrainingQuestionVo.setSectionId(questionTypesDTO.getSectionId());
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            bookSet.stream().forEach(bookQuestionDetailDto -> {
                List<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterQuestionDetailDtos =
                        chapterSet.stream().filter(chapterQuestionDetailDto -> chapterQuestionDetailDto.getBookStr().equals(bookQuestionDetailDto.getBookStr())).collect(Collectors.toList());
                chapterQuestionDetailDtos.stream().forEach(chapterQuestionDetailDto -> {
                    List<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionList = sectionSet.stream()
                            .filter(sectionQuestionDetailDto -> sectionQuestionDetailDto.getChapterId().equals(chapterQuestionDetailDto.getChapterId())).collect(Collectors.toList());
                    sectionList.stream().forEach(sectionQuestionDetailDto -> {
                        List<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOS = pointsDTOSet.stream().filter(
                                knowledgePointsDTO -> knowledgePointsDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        pointsDTOS.stream().forEach(pointsDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getKnowledgePointId() !=null
                                            && question.getKnowledgePointId().equals(pointsDTO.getKnowledgePointId())
                                            && question.getSectionId().equals(pointsDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            pointsDTO.setQuestionList(questionList);
                        });
                        pointsDTOS.sort(Comparator.comparing(ExamChapterQuestionDetailDto
                                .KnowledgePointsDTO::getKnowledgePointId));
                        sectionQuestionDetailDto.setKnowledgePoints(pointsDTOS);

                        List<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOList = questionTypesDTOSet.stream().filter(
                                questionTypesDTO -> questionTypesDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        questionTypesDTOList.stream().forEach(questionTypesDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getQuestionTypeId() !=null
                                            && question.getQuestionTypeId().equals(questionTypesDTO.getQuestionTypeId())
                                            && question.getSectionId().equals(questionTypesDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            questionTypesDTO.setQuestionList(questionList);
                        });
                        questionTypesDTOList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.QuestionTypesDTO::getQuestionTypeId));
                        sectionQuestionDetailDto.setQuestionTypes(questionTypesDTOList);
                    });
                    sectionList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.SectionQuestionDetailDto::getSectionSortNo));
                    chapterQuestionDetailDto.setSectionList(sectionList);
                });
                chapterQuestionDetailDtos.sort(Comparator.comparing(ExamChapterQuestionDetailDto.ChapterQuestionDetailDto::getChapterSortNo));
                bookQuestionDetailDto.setChapterList(chapterQuestionDetailDtos);
            });
            ArrayList<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookList = new ArrayList<>(bookSet);
            bookList.sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getGrade)
                                    .thenComparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getSemester)
            );
            examChapterQuestionDetailDto.setBookList(bookList);
            examChapterQuestionDetailDto.setTotalQuestions(questionSet.size());
            examChapterQuestionDetailDto.setTotalKnowledgePoints(pointsDTOSet.size());
            examChapterQuestionDetailDto.setTotalQuestionTypes(questionTypesDTOSet.size());
            return examChapterQuestionDetailDto;
        } catch (Exception e) {
            log.error("createQuestionByKnowledgeAndQuestionTypeV3 方法执行出错，参数: {}", param, e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public ExamChapterQuestionDetailDto createHolidayTrainingQuestion(CreateQuestionByKnowledgeParamV2 param) throws BaseException {
        try {
            ExamChapterQuestionDetailDto examChapterQuestionDetailDto = new ExamChapterQuestionDetailDto();
            CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = new CreateQuestionByKnowledgeVoV2();
            // 调用 AI 生成题目
            if (param.getSectionId() != null) {
                createQuestionByKnowledgeVo = eduKnowledgeHubService.createHolidayTrainingSectionQuestion(param);
            }
            if (param.getChapterId() != null) {
                createQuestionByKnowledgeVo = eduKnowledgeHubService.createHolidayTrainingChapterQuestion(param);
            }
            Set<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOSet = new HashSet<>();
            Set<SpecializedTrainingQuestionVo> questionSet  = new HashSet<>();
            CreateQuestionByKnowledgeVoV2.DataDTO dataDTO = createQuestionByKnowledgeVo.getData();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> knowledgePointsQuestionList = dataDTO.getKnowledgePoints();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> questionTypesQuestionList = dataDTO.getQuestionTypes();
            knowledgePointsQuestionList.forEach(knowledgePointsDTO -> {

                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(knowledgePointsDTO.getGrade());
                bookQuestionDetailDto.setPublisher(knowledgePointsDTO.getPublisher());
                bookQuestionDetailDto.setSemester(knowledgePointsDTO.getSemester());
                bookQuestionDetailDto.setBookStr(PublisherEnum.getPublisherName(knowledgePointsDTO.getPublisher())+formatPublisher(knowledgePointsDTO.getGrade(),  knowledgePointsDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(knowledgePointsDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(knowledgePointsDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(knowledgePointsDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(knowledgePointsDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(knowledgePointsDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamChapterQuestionDetailDto.KnowledgePointsDTO();
                knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                pointsDTOSet.add(knowledgePoint);

                knowledgePointsDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                    specializedTrainingQuestionVo.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                    specializedTrainingQuestionVo.setSectionId(knowledgePointsDTO.getSectionId());
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            questionTypesQuestionList.forEach(questionTypesDTO -> {
                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(questionTypesDTO.getGrade());
                bookQuestionDetailDto.setPublisher(questionTypesDTO.getPublisher());
                bookQuestionDetailDto.setSemester(questionTypesDTO.getSemester());
                bookQuestionDetailDto.setBookStr(questionTypesDTO.getPublisher()
                        +formatPublisher(questionTypesDTO.getGrade(),  questionTypesDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(questionTypesDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(questionTypesDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(questionTypesDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(questionTypesDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(questionTypesDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.QuestionTypesDTO questionType = new ExamChapterQuestionDetailDto.QuestionTypesDTO();
                questionType.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                questionType.setQuestionTypeName(questionTypesDTO.getQuestionTypeName());
                questionType.setSectionId(questionTypesDTO.getSectionId());
                questionTypesDTOSet.add(questionType);

                questionTypesDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                    specializedTrainingQuestionVo.setQuestionType(questionTypesDTO.getQuestionTypeName());
                    specializedTrainingQuestionVo.setSectionId(questionTypesDTO.getSectionId());
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            bookSet.stream().forEach(bookQuestionDetailDto -> {
                List<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterQuestionDetailDtos =
                        chapterSet.stream().filter(chapterQuestionDetailDto -> chapterQuestionDetailDto.getBookStr().equals(bookQuestionDetailDto.getBookStr())).collect(Collectors.toList());
                chapterQuestionDetailDtos.stream().forEach(chapterQuestionDetailDto -> {
                    List<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionList = sectionSet.stream()
                            .filter(sectionQuestionDetailDto -> sectionQuestionDetailDto.getChapterId().equals(chapterQuestionDetailDto.getChapterId())).collect(Collectors.toList());
                    sectionList.stream().forEach(sectionQuestionDetailDto -> {
                        List<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOS = pointsDTOSet.stream().filter(
                                knowledgePointsDTO -> knowledgePointsDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        pointsDTOS.stream().forEach(pointsDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getKnowledgePointId() !=null
                                            && question.getKnowledgePointId().equals(pointsDTO.getKnowledgePointId())
                                            && question.getSectionId().equals(pointsDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            pointsDTO.setQuestionList(questionList);
                        });
                        pointsDTOS.sort(Comparator.comparing(ExamChapterQuestionDetailDto.KnowledgePointsDTO::getKnowledgePointId));
                        sectionQuestionDetailDto.setKnowledgePoints(pointsDTOS);

                        List<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOList = questionTypesDTOSet.stream().filter(
                                questionTypesDTO -> questionTypesDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        questionTypesDTOList.stream().forEach(questionTypesDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getQuestionTypeId() !=null
                                            && question.getQuestionTypeId().equals(questionTypesDTO.getQuestionTypeId())
                                            && question.getSectionId().equals(questionTypesDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            questionTypesDTO.setQuestionList(questionList);
                        });
                        questionTypesDTOList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.QuestionTypesDTO::getQuestionTypeId));
                        sectionQuestionDetailDto.setQuestionTypes(questionTypesDTOList);
                    });
                    sectionList.sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.SectionQuestionDetailDto::getSectionSortNo));
                    chapterQuestionDetailDto.setSectionList(sectionList);
                });
                chapterQuestionDetailDtos.sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.ChapterQuestionDetailDto::getChapterSortNo));
                bookQuestionDetailDto.setChapterList(chapterQuestionDetailDtos);
            });
            ArrayList<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookList = new ArrayList<>(bookSet);
            bookList.sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getGrade)
                    .thenComparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getSemester)
            );
            examChapterQuestionDetailDto.setBookList(bookList);
            examChapterQuestionDetailDto.setTotalQuestions(questionSet.size());
            examChapterQuestionDetailDto.setTotalKnowledgePoints(pointsDTOSet.size());
            examChapterQuestionDetailDto.setTotalQuestionTypes(questionTypesDTOSet.size());
            if (CollectionUtil.isNotEmpty(examChapterQuestionDetailDto.getBookList())) {
                examChapterQuestionDetailDto.setBookList(examChapterQuestionDetailDto.getBookList().stream().filter(bookQuestionDetailDto -> bookQuestionDetailDto.getChapterList().size() > 0).collect(Collectors.toList()));
            }
            return examChapterQuestionDetailDto;
        } catch (Exception e) {
            log.error("createQuestionByKnowledgeAndQuestionTypeV3 方法执行出错，参数: {}", param, e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public List<ExamBookQuestionDetailDto> createHolidayTrainingBookQuestion(CreateQuestionByKnowledgeParamV2 param) throws BaseException {
        try {
            CreateTextBookQuestioVo createQuestionByKnowledgeVo = eduKnowledgeHubService.createHolidayTrainingTextBookQuestion(param);
            List<CreateTextBookQuestioVo.DataDTO> dataDTOList = createQuestionByKnowledgeVo.getData();
            Set<ExamBookQuestionDetailDto> bookSet = new HashSet<>();
            dataDTOList.forEach(dataDTO -> {
                QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(dataDTO.getId());
                dataDTO.setQuestionType(questionInfoById.getQuestionType());
                dataDTO.setContent(questionInfoById.getQuestionContent());

                ExamBookQuestionDetailDto examBookQuestionDetailDto = new ExamBookQuestionDetailDto();
                ExamBookQuestionDetailDto.QuestionInfoDto examBookQuestionInfoDto = new ExamBookQuestionDetailDto.QuestionInfoDto();
                examBookQuestionDetailDto.setQuestionType(questionInfoById.getQuestionType());

                List<ExamBookQuestionDetailDto.QuestionInfoDto> questionInfoDtos = new ArrayList<>();
                examBookQuestionInfoDto.setQuestionId(dataDTO.getId());
                examBookQuestionInfoDto.setQuestionContent(decodeContentV2(questionInfoById.getQuestionContent()));
                examBookQuestionInfoDto.setOssUrl(questionInfoById.getOssUrl());
                examBookQuestionInfoDto.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                examBookQuestionInfoDto.setAnalyzeContent(decodeContentV2(questionInfoById.getAnalyzeContent()));
                examBookQuestionInfoDto.setSortNo(dataDTO.getSortNo());

                List<ExamBookQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new ArrayList<>();
                if (dataDTO.getKnowledgePoints() != null) {
                    dataDTO.getKnowledgePoints().forEach(knowledgePointsDTO -> {
                        ExamBookQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamBookQuestionDetailDto.KnowledgePointsDTO();
                        knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getId());
                        knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getName());
                        knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                        pointsDTOSet.add(knowledgePoint);
                    });
                }
                examBookQuestionInfoDto.setKnowledgePoints(pointsDTOSet);
                questionInfoDtos.add(examBookQuestionInfoDto);
                examBookQuestionDetailDto.setQuestionInfoDtos(questionInfoDtos);
                bookSet.add(examBookQuestionDetailDto);
            });
            List<ExamBookQuestionDetailDto> result = new ArrayList<>(bookSet);

            Map<String, List<ExamBookQuestionDetailDto.QuestionInfoDto>> groupedData = new HashMap<>();
            // 遍历原始列表，根据questionType分组
            for (ExamBookQuestionDetailDto item : result) {
                String qType = item.getQuestionType();
                List<ExamBookQuestionDetailDto.QuestionInfoDto> qInfos = item.getQuestionInfoDtos();
                if (!groupedData.containsKey(qType)) {
                    groupedData.put(qType, new ArrayList<>());
                }
                groupedData.get(qType).addAll(qInfos);
            }

            List<ExamBookQuestionDetailDto> resultList = new ArrayList<>();
            // 遍历Map并创建ExamBookQuestionDetailDto对象
            for (Map.Entry<String, List<ExamBookQuestionDetailDto.QuestionInfoDto>> entry : groupedData.entrySet()) {
                String questionType = entry.getKey();
                List<ExamBookQuestionDetailDto.QuestionInfoDto> questionInfoDtos = entry.getValue();
                // 创建新的DTO对象
                ExamBookQuestionDetailDto newDto = new ExamBookQuestionDetailDto();
                newDto.setQuestionType(questionType);
                newDto.setBookStr(PublisherEnum.getPublisherName(param.getPublisher())+formatPublisher(param.getGrade(), param.getSemester()));
                newDto.setQuestionInfoDtos(questionInfoDtos);
                // 添加到结果列表中
                resultList.add(newDto);
            }

            return resultList;
        } catch (Exception e) {
            log.error("createHolidayTrainingBookQuestion 方法执行出错，参数: {}", param, e);
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public QuestionAnalyzeResultDto questionAnalyzeResult(UUID questionId,PublisherEnum  publisher) {
        String  publisherName =null;
        if(publisher != null){
             publisherName = publisher.getDescription().replace("版", "");
        }
        QuestionAnalyzeResultDto questionAnalyzeResultDto = personalExamMapper.questionAnalyzeResult(questionId,publisherName);
        CommonResponse.ERROR.assertNotNull(questionAnalyzeResultDto, "题目解析失败");
        QuestionTypesDto questionTypesByQuestionId = eduKnowledgeHubService.getQuestionTypesByQuestionId(questionId);
        if (questionTypesByQuestionId.getData() != null) {
            QuestionTypesDto.DataDTO data = questionTypesByQuestionId.getData();
            Set<String> questionTypesName = new HashSet<>();
            data.getQuestionTypes().forEach(questionTypesDTO -> {
                questionTypesName.add(questionTypesDTO.getName());
            });

            questionAnalyzeResultDto.setQuestionTypesName(new ArrayList<>(questionTypesName));
        }

        return questionAnalyzeResultDto;
    }

    @Override
    public CheckExamExsitenceVo examExist(String qrStr) {
        CommonResponse.ERROR.assertNotEmpty(qrStr, "请输入试卷id");
        UUID examId = null;
        if (!qrStr.startsWith(inkQMath)) {
            CommonResponse.assertError("试卷不存在");
        }
        CheckExamExsitenceVo.DataDTO examData = new CheckExamExsitenceVo.DataDTO();
        try {
            String printId = qrStr.replace(inkQMath, "");
            MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(UUID.fromString(printId));
            CommonResponse.ERROR.assertNotNull(mathExamsEntity, "试卷不存在");
            examId = mathExamsEntity.getId();
            examData.setPublisher(mathExamsEntity.getPublisher() != null ? PublisherEnum.ofCustomerName(mathExamsEntity.getPublisher()) : null);
            examData.setExamSource(mathExamsEntity.getSource());
        } catch (Exception e) {
            log.error("examExist 方法执行出错，参数: {}", qrStr, e);
        }

        boolean hasKnowledge = false;
        ExamHasKnowledgeDto examHasKnowledgeDto = eduKnowledgeHubService.examHasKnowledge(ExamHasKnowledgeParam.builder().examId(examId).build());
        if (examHasKnowledgeDto != null && examHasKnowledgeDto.getCode() == CommonResponse.SUCCESS.getCode()) {
            hasKnowledge = examHasKnowledgeDto.getData().isExist();
        }

        CheckExamExsitenceVo checkExamExsitenceVo = CheckExamExsitenceVo.builder()
                .code(ApiResultCode.SUCCESS.getCode())
                .build();
        examData.setKnowledgePointsExist(hasKnowledge);
        examData.setExamId(examId);
        examData.setExist(true);
        examData.setSpecialTraining(true);

        checkExamExsitenceVo.setData(examData);
        return checkExamExsitenceVo;
    }

    @Override
    public CheckExamExsitenceVo examExistByPersonalExamId(Long personalExamId) {
        CommonResponse.ERROR.assertNotNull(personalExamId, "请输入personalExamId");
        PersonalExam personalExam = personalExamMapper.selectById(personalExamId);
        UUID examId = personalExam.getExamId();

        boolean hasKnowledge = false;
        ExamHasKnowledgeDto examHasKnowledgeDto = eduKnowledgeHubService.examHasKnowledge(ExamHasKnowledgeParam.builder().examId(examId).build());
        if (examHasKnowledgeDto != null && examHasKnowledgeDto.getCode() == CommonResponse.SUCCESS.getCode()) {
            hasKnowledge = examHasKnowledgeDto.getData().isExist();
        }

        CheckExamExsitenceVo checkExamExsitenceVo = CheckExamExsitenceVo.builder()
                .code(ApiResultCode.SUCCESS.getCode())
                .build();
        CheckExamExsitenceVo.DataDTO examData = new CheckExamExsitenceVo.DataDTO();
        examData.setPublisher(personalExam.getPublisher());
        examData.setKnowledgePointsExist(hasKnowledge);
        examData.setExamId(examId);
        examData.setExist(true);
        examData.setSpecialTraining(true);
        MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(examId);
        examData.setExamSource(mathExamsEntity.getSource());

        List<String> photos = personalExamMapper.selectExistExamPhotoByOriginalPaper(examData.getExamId());
        List<String> photosUrls = new ArrayList<>();
        if (CollUtil.isNotEmpty(photos)) {
            photos.forEach(photo -> {
                PresignedUrlParam param1 = new PresignedUrlParam();
                param1.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                param1.setOssKey(photo);
                PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(param1);
                photosUrls.add(presignedUrlDto.getData().getPresignedUrl());
            });
        }
        examData.setPhotoUrls(photosUrls);
        checkExamExsitenceVo.setData(examData);
        return checkExamExsitenceVo;
    }

    @Override
    public String saveExam(SpecializedTrainingCreateExamParam param) {
//        GradeInfo gradeByStudentId = gradeService.getGradeByStudentId(param.getStudentId());
//        param.setGrade(gradeByStudentId.getCurrentGradeLevel());
//        param.setSemester(gradeByStudentId.getCurrentSemester());
        param.setPublisher(param.getPublisher());
        SpecializedTrainingCrateExamDto specializedTrainingCrateExamDto = eduKnowledgeHubService.saveExam(param);
        if (specializedTrainingCrateExamDto != null && specializedTrainingCrateExamDto.getData() != null) {
            if (StrUtil.isNotBlank(specializedTrainingCrateExamDto.getData())) {
                return specializedTrainingCrateExamDto.getData();
            }
        }
        return null;
    }

    @Override
    public SpecializedTrainingUpdateExamDto updateExam(SpecializedTrainingUpdateExamParam param) {
        PresignedUrlParam build = PresignedUrlParam.builder()
                .ossEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB)
                .ossKey(param.getPdfUrl())
                .build();
        PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(build);
        try {
            //pdf转图片
            byte[][] pngBytes = PDFToImageUtils.convertPDFToImageBytes(presignedUrlDto.getData().getPresignedUrl(), "png");
            List<AISpecializedTrainingUpdateExamParam.ImagesDTO> imageList = new ArrayList<>();
            for (int i = 0; i < pngBytes.length; i++) {
                byte[] pngByte = pngBytes[i];
                String uuid = UUID.randomUUID().toString();
                String objectName = aliOssUtils.upload(pngByte, "question/", uuid + ".png");

                AISpecializedTrainingUpdateExamParam.ImagesDTO imagesDTOBuilder = AISpecializedTrainingUpdateExamParam.ImagesDTO.builder()
                        .ossEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB)
                        .ossKey(objectName)
                        .sortNo(i)
                        .type(0)
                        .build();
                imageList.add(imagesDTOBuilder);
            }

            AISpecializedTrainingUpdateExamParam updateExamParam = AISpecializedTrainingUpdateExamParam.builder()
                    .examId(param.getExamId())
                    .images(imageList)
                    .examSource(param.getExamSource())
                    .build();
            SpecializedTrainingUpdateExamDto specializedTrainingUpdateExamDto = eduKnowledgeHubService.specializedTrainingUpdateExam(updateExamParam);
            PersonalExam personalExam = PersonalExam.builder().examId(param.getExamId()).studentId(param.getStudentId())
                    .flag(param.getExamSource().getName()).publisher(param.getPublisher()).build();
            if (param.getExamSource().equals(ExamSourceType.HOLIDAY_TRAINING)) {
                MathActivityWeekUnitStudent mathActivityWeekUnitStudent = mathActivityWeekUnitStudentMapper.selectById(param.getWeekUnitStudentId());
                QueryWrapper<MathActivityStudent> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("activity_id", mathActivityWeekUnitStudent.getActivityId());
                queryWrapper.eq("student_id", mathActivityWeekUnitStudent.getStudentId());
                queryWrapper.isNull("deleted_at");
                MathActivityStudent mathActivityStudent = mathActivityStudentMapper.selectOne(queryWrapper);
                personalExam.setPublisher(mathActivityStudent.getPublisher());
                mathActivityWeekUnitStudentMapper.updateExamIdHistoryById(param.getWeekUnitStudentId(), param.getExamId().toString());
            }
            personalExamMapper.insert(personalExam);
            return specializedTrainingUpdateExamDto;
        } catch (Exception e) {
            log.error("updateExam 方法执行出错，参数: {}", param, e);
        }
        return SpecializedTrainingUpdateExamDto.builder()
                .code(ApiResultCode.FAIL.getCode())
                .message("更新失败")
                .build();
    }

    /**
     * 根据年级和学期格式化出版商信息
     *
     * @param grade    年级 1-12
     * @param semester 学期 1-上册 2-下册
     * @return 格式化后的字符串，如：七年级上册、高一下册
     */
    private String formatPublisher(Integer grade, Integer semester) {
        if (grade == null || semester == null) {
            return "未知";
        }
        String formattedGrade;

        if (grade >= 1 && grade <= 9) {
            // 1-9年级显示为：一年级、二年级、三年级等
            String[] gradeNames = {"一", "二", "三", "四", "五", "六", "七", "八", "九"};
            formattedGrade = gradeNames[grade - 1] + "年级";
        } else if (grade >= 10 && grade <= 12) {
            // 10-12年级显示为：高一、高二、高三
            String[] highSchoolGradeNames = {"一", "二", "三"};
            formattedGrade = "高" + highSchoolGradeNames[grade - 10];
        } else {
            // 处理异常情况
            formattedGrade = "未知年级";
        }

        // 添加学期信息
        return formattedGrade + (semester == 1 ? "上册" : "下册");
    }

    @Override
    public TextbookCatalogLabelTree textbookCatalogue(Long analyzeReportId, UUID examId) {
        ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(analyzeReportId);
        if (examAnalyzeResult == null) {
            CommonResponse.assertError("考情分析报告不存在");
        }
        if (StrUtil.isNotBlank(examAnalyzeResult.getManualExamScope())) {
            return JSON.parseObject(examAnalyzeResult.getManualExamScope(), TextbookCatalogLabelTree.class);
        }
        PersonalExam personalExam = personalExamMapper.selectById(examAnalyzeResult.getPersonalExamId());
        CommonResponse.ERROR.assertNotNull(personalExam, "未找到个人试卷信息");
        TextBookCatalogueDto textBookCatalogueDto = eduKnowledgeHubService.textbooksChaptersSections(examId,personalExam.getPublisher());
        if (CollUtil.isEmpty(textBookCatalogueDto.getData())) {
            CommonResponse.assertError("获取教材目录失败");
        }
        List<TextBookCatalogueDto.DataDTO> catalogueList = textBookCatalogueDto.getData();

        // 先按照grade升序排序，再按照semester升序排序
        catalogueList = catalogueList.stream()
                .sorted(Comparator.comparing(TextBookCatalogueDto.DataDTO::getGrade)
                        .thenComparing(TextBookCatalogueDto.DataDTO::getSemester))
                .collect(Collectors.toList());

        // 使用LinkedHashMap保持排序后的顺序
        Map<String, List<TextBookCatalogueDto.DataDTO>> publisherMap = catalogueList.stream()
                .collect(Collectors.groupingBy(
                        item -> formatPublisher(item.getGrade(), item.getSemester()),
                        LinkedHashMap::new,
                        Collectors.toList()));

        List<TextbookCatalogLabelTree.PublisherNode> publisherNodes = publisherMap.entrySet().stream()
                .map(entry -> {
                    String publisher = entry.getKey();
                    List<TextBookCatalogueDto.DataDTO> publisherItems = entry.getValue();

                    Map<String, List<TextBookCatalogueDto.DataDTO>> chapterMap = publisherItems.stream()
                            .collect(Collectors.groupingBy(TextBookCatalogueDto.DataDTO::getChapterId));

                    List<TextbookCatalogLabelTree.ChapterNode> chapterNodes = chapterMap.values().stream()
                            .map(sections -> {
                                if (sections.isEmpty()) {
                                    return null;
                                }

                                TextBookCatalogueDto.DataDTO first = sections.get(0);
                                List<TextbookCatalogLabelTree.SectionNode> sectionNodes = sections.stream()
                                        .map(section -> new TextbookCatalogLabelTree.SectionNode(
                                                section.getSectionId(),
                                                section.getSectionName(),
                                                section.getSectionSortNo(),
                                                section.getTextbookId(),
                                                section.getGrade(),
                                                section.getSemester(),
                                                false, false
                                        ))
                                        .sorted(Comparator.comparing(TextbookCatalogLabelTree.SectionNode::getSectionSortNo))
                                        .collect(Collectors.toList());

                                return new TextbookCatalogLabelTree.ChapterNode(
                                        first.getChapterId(),
                                        first.getChapterName(),
                                        first.getChapterSortNo(),
                                        sectionNodes, false, false
                                );
                            })
                            .filter(Objects::nonNull)
                            .sorted(Comparator.comparing(TextbookCatalogLabelTree.ChapterNode::getChapterSortNo))
                            .collect(Collectors.toList());

                    return new TextbookCatalogLabelTree.PublisherNode(publisher, chapterNodes, false, false);
                })
                .collect(Collectors.toList());
        return new TextbookCatalogLabelTree(publisherNodes);
    }

    @Override
    public void updateExamScope(ExamScopeParam param) {
        ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(param.getAnalyzeReportId());
        if (examAnalyzeResult == null) {
            CommonResponse.assertError("考情分析报告不存在");
        }
        examAnalyzeResult.setManualExamScope(param.getExamScope());
        examAnalyzeResultMapper.updateById(examAnalyzeResult);
    }

    @Override
    public TextbookCatalogLabelTree getExamScope(Long analyzeReportId) {
        ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(analyzeReportId);
        if (examAnalyzeResult == null) {
            CommonResponse.assertError("考情分析报告不存在");
        }
        if (StrUtil.isNotBlank(examAnalyzeResult.getManualExamScope())) {
            return JSON.parseObject(examAnalyzeResult.getManualExamScope(), TextbookCatalogLabelTree.class);
        }
        return null;
    }

    @Override
    public List<String> getManualExamScopeName(Long analyzeReportId) {
        ExamAnalyzeResult examAnalyzeResult = examAnalyzeResultMapper.selectById(analyzeReportId);
        if (examAnalyzeResult == null) {
            CommonResponse.assertError("考情分析报告不存在");
        }

        List<String> selectedItems = new ArrayList<>();
        if (StrUtil.isNotBlank(examAnalyzeResult.getManualExamScope())) {
            TextbookCatalogLabelTree textbookCatalogLabelTree = JSON.parseObject(examAnalyzeResult.getManualExamScope(), TextbookCatalogLabelTree.class);

            // 遍历publishers
            if (CollUtil.isNotEmpty(textbookCatalogLabelTree.getPublishers())) {
                List<TextbookCatalogLabelTree.PublisherNode> publishers = new ArrayList<>();

                for (TextbookCatalogLabelTree.PublisherNode publisher : textbookCatalogLabelTree.getPublishers()) {
                    // 如果出版商被显示，添加其名称
                    if (publisher.isShow()) {
                        selectedItems.add(publisher.getPublisher());
                    }

                    // 遍历chapters，无论出版商是否显示
                    if (CollUtil.isNotEmpty(publisher.getChapters())) {
                        for (TextbookCatalogLabelTree.ChapterNode chapter : publisher.getChapters()) {
                            // 如果章节被显示，添加其名称
                            if (chapter.isShow()) {
                                selectedItems.add("第" + chapter.getChapterSortNo() + "章 " + chapter.getChapterName());
                            }

                            // 遍历sections，无论章节是否显示
                            if (CollUtil.isNotEmpty(chapter.getSections())) {
                                for (TextbookCatalogLabelTree.SectionNode section : chapter.getSections()) {
                                    // 如果小节被显示，添加其名称
                                    if (section.isShow()) {
                                        selectedItems.add(chapter.getChapterSortNo() + "." + chapter.getChapterSortNo() + section.getSectionName());
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 将选中项列表打印到日志中以便于调试
            log.info("Selected items: {}", selectedItems);
        }

        return selectedItems;
    }

    @Override
    public KnowledgePointBySelectVo getKnowledgePointBySection(Long analyzeReportId) {
        List<UUID> sectionIds = examAnalyzeResultMapper.selectManualExamScopeSection(analyzeReportId);
        if (CollUtil.isEmpty(sectionIds)) {
            return null;
        }
        List<UUID> knowledgePointIds = examAnalyzeResultMapper.selectKnowledgePointByAnalyzeId(analyzeReportId);
        KnowledgePointBySelectDto knowledgePointBySection = eduKnowledgeHubService.getKnowledgePointBySection(sectionIds);
        if (knowledgePointBySection == null || CollUtil.isEmpty(knowledgePointBySection.getData())) {
            return null;
        }

        // 对知识点按ID去重
        List<KnowledgePointBySelectDto.DataDTO> data = knowledgePointBySection.getData().stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                KnowledgePointBySelectDto.DataDTO::getId,
                                Function.identity(),
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));

        if (CollUtil.isEmpty(knowledgePointIds)) {
            return KnowledgePointBySelectVo.builder()
                    .knowledgePointSize(data.size())
                    .coveredKnowledgePointSize(0)
                    .notCoveredKnowledgePointSize(data.size())
                    .knowledgePointList(data)
                    .build();
        }

        // 计算本次考试知识点在所有知识点中的覆盖情况
        // 过滤出所有知识点中包含在本次考试知识点列表中的知识点
        List<KnowledgePointBySelectDto.DataDTO> coveredData = data.stream()
                .filter(dataDTO -> knowledgePointIds.contains(dataDTO.getId()))
                .collect(Collectors.toList());

        // 本次考试知识点覆盖的数量
        int coveredSize = coveredData.size();
        // 未覆盖的知识点数量
        int notCoveredSize = data.size() - coveredSize;

        return KnowledgePointBySelectVo.builder()
                .knowledgePointSize(data.size())
                .coveredKnowledgePointSize(coveredSize)
                .notCoveredKnowledgePointSize(notCoveredSize)
                .knowledgePointList(data) // 返回所有知识点列表
                .build();
    }

    @Override
    public SpecializedTrainingQuestionVo getMathQuestionDetailById(UUID questionId,PublisherEnum  publisher) throws BaseException {
        String  publisherName =null;
        if(publisher != null){
            publisherName = publisher.getDescription().replace("版", "");
        }
        QuestionAnalyzeResultDto questionInfoById = personalExamMapper.questionAnalyzeResult(questionId,publisherName);
        SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
        BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
        specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(questionInfoById.getQuestionContent()));
        specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(questionInfoById.getAnalyzeContent()));
        specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
        PastExamQuestionDto pastExamQuestionDto = mathQuestionsMapper.selectPastExamQuestionDto(null, questionId);
        specializedTrainingQuestionVo.setIsPastExamPaper(false);
        if (pastExamQuestionDto != null && pastExamQuestionDto.getIsPastExamPaper()){
            specializedTrainingQuestionVo.setIsPastExamPaper(true);
            specializedTrainingQuestionVo.setPastExamPaperYear(pastExamQuestionDto.getPastExamPaperYear());
            specializedTrainingQuestionVo.setPastExamPaperRegion(pastExamQuestionDto.getPastExamPaperRegion());
        }
        return specializedTrainingQuestionVo;
    }

    @Override
    public ExamChapterQuestionDetailDto selectSpecializedTrainingExamDetail(UUID examId) {
        try {
            ExamChapterQuestionDetailDto examChapterQuestionDetailDto = new ExamChapterQuestionDetailDto();
            CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = eduKnowledgeHubService.selectSpecializedTrainingExamDetail(examId);
            Set<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new HashSet<>();
            Set<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOSet = new HashSet<>();
            Set<SpecializedTrainingQuestionVo> questionSet  = new HashSet<>();
            CreateQuestionByKnowledgeVoV2.DataDTO dataDTO = createQuestionByKnowledgeVo.getData();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> knowledgePointsQuestionList = dataDTO.getKnowledgePoints();
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> questionTypesQuestionList = dataDTO.getQuestionTypes();
            knowledgePointsQuestionList.forEach(knowledgePointsDTO -> {

                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(knowledgePointsDTO.getGrade());
                bookQuestionDetailDto.setPublisher(knowledgePointsDTO.getPublisher());
                bookQuestionDetailDto.setSemester(knowledgePointsDTO.getSemester());
                bookQuestionDetailDto.setBookStr(PublisherEnum.getPublisherName(knowledgePointsDTO.getPublisher())+formatPublisher(knowledgePointsDTO.getGrade(),  knowledgePointsDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(knowledgePointsDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(knowledgePointsDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(knowledgePointsDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(knowledgePointsDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(knowledgePointsDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(knowledgePointsDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamChapterQuestionDetailDto.KnowledgePointsDTO();
                knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                pointsDTOSet.add(knowledgePoint);

                knowledgePointsDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setKnowledgePointId(knowledgePointsDTO.getKnowledgePointId());
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(specializedTrainingQuestionVo.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setKnowledgePointName(knowledgePointsDTO.getKnowledgePointName());
                    specializedTrainingQuestionVo.setSectionId(knowledgePointsDTO.getSectionId());
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            questionTypesQuestionList.forEach(questionTypesDTO -> {
                ExamChapterQuestionDetailDto.BookQuestionDetailDto bookQuestionDetailDto = new ExamChapterQuestionDetailDto.BookQuestionDetailDto();
                bookQuestionDetailDto.setGrade(questionTypesDTO.getGrade());
                bookQuestionDetailDto.setPublisher(questionTypesDTO.getPublisher());
                bookQuestionDetailDto.setSemester(questionTypesDTO.getSemester());
                bookQuestionDetailDto.setBookStr(questionTypesDTO.getPublisher()
                        +formatPublisher(questionTypesDTO.getGrade(),  questionTypesDTO.getSemester()));
                bookSet.add(bookQuestionDetailDto);

                ExamChapterQuestionDetailDto.ChapterQuestionDetailDto chapterQuestionDetailDto = new ExamChapterQuestionDetailDto.ChapterQuestionDetailDto();
                chapterQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                chapterQuestionDetailDto.setChapterName(questionTypesDTO.getChapterName());
                chapterQuestionDetailDto.setBookStr(bookQuestionDetailDto.getBookStr());
                chapterQuestionDetailDto.setChapterSortNo(questionTypesDTO.getChapterSortNo());
                chapterSet.add(chapterQuestionDetailDto);

                ExamChapterQuestionDetailDto.SectionQuestionDetailDto sectionQuestionDetailDto = new ExamChapterQuestionDetailDto.SectionQuestionDetailDto();
                sectionQuestionDetailDto.setChapterId(questionTypesDTO.getChapterId());
                sectionQuestionDetailDto.setSectionId(questionTypesDTO.getSectionId());
                sectionQuestionDetailDto.setSectionName(questionTypesDTO.getSectionName());
                sectionQuestionDetailDto.setSectionSortNo(questionTypesDTO.getSectionSortNo());
                sectionSet.add(sectionQuestionDetailDto);

                ExamChapterQuestionDetailDto.QuestionTypesDTO questionType = new ExamChapterQuestionDetailDto.QuestionTypesDTO();
                questionType.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                questionType.setQuestionTypeName(questionTypesDTO.getQuestionTypeName());
                questionType.setSectionId(questionTypesDTO.getSectionId());
                questionTypesDTOSet.add(questionType);

                questionTypesDTO.getQuestions().forEach(question -> {
                    QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(question.getId());
                    SpecializedTrainingQuestionVo specializedTrainingQuestionVo = new SpecializedTrainingQuestionVo();
                    BeanUtil.copyProperties(questionInfoById, specializedTrainingQuestionVo);
                    specializedTrainingQuestionVo.setQuestionContent(decodeContentV2(specializedTrainingQuestionVo.getQuestionContent()));
                    specializedTrainingQuestionVo.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                    specializedTrainingQuestionVo.setAnalyzeContent(decodeContentV2(questionInfoById.getAnalyzeContent()));
                    specializedTrainingQuestionVo.setQuestionTypeId(questionTypesDTO.getQuestionTypeId());
                    specializedTrainingQuestionVo.setQuestionType(questionTypesDTO.getQuestionTypeName());
                    specializedTrainingQuestionVo.setSectionId(questionTypesDTO.getSectionId());
                    questionSet.add(specializedTrainingQuestionVo);
                });
            });
            bookSet.stream().forEach(bookQuestionDetailDto -> {
                List<ExamChapterQuestionDetailDto.ChapterQuestionDetailDto> chapterQuestionDetailDtos =
                        chapterSet.stream().filter(chapterQuestionDetailDto -> chapterQuestionDetailDto.getBookStr().equals(bookQuestionDetailDto.getBookStr())).collect(Collectors.toList());
                chapterQuestionDetailDtos.stream().forEach(chapterQuestionDetailDto -> {
                    List<ExamChapterQuestionDetailDto.SectionQuestionDetailDto> sectionList = sectionSet.stream()
                            .filter(sectionQuestionDetailDto -> sectionQuestionDetailDto.getChapterId() != null && sectionQuestionDetailDto.getChapterId().equals(chapterQuestionDetailDto.getChapterId())).collect(Collectors.toList());
                    sectionList.stream().forEach(sectionQuestionDetailDto -> {
                        List<ExamChapterQuestionDetailDto.KnowledgePointsDTO> pointsDTOS = pointsDTOSet.stream().filter(
                                knowledgePointsDTO -> knowledgePointsDTO.getSectionId() != null && knowledgePointsDTO.getSectionId().equals(sectionQuestionDetailDto.getSectionId())).collect(Collectors.toList());
                        pointsDTOS.stream().forEach(pointsDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getKnowledgePointId() !=null
                                            && question.getKnowledgePointId().equals(pointsDTO.getKnowledgePointId())
                                            && question.getSectionId().equals(pointsDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            pointsDTO.setQuestionList(questionList);
                        });
                        pointsDTOS.sort(Comparator.comparing(ExamChapterQuestionDetailDto.KnowledgePointsDTO::getKnowledgePointId));
                        sectionQuestionDetailDto.setKnowledgePoints(pointsDTOS);

                        List<ExamChapterQuestionDetailDto.QuestionTypesDTO> questionTypesDTOList = questionTypesDTOSet.stream().filter(
                                questionTypesDTO -> sectionQuestionDetailDto.getSectionId().equals(questionTypesDTO.getSectionId())).collect(Collectors.toList());
                        questionTypesDTOList.stream().forEach(questionTypesDTO -> {
                            List<SpecializedTrainingQuestionVo> questionList = questionSet.stream().filter(
                                    question -> question.getQuestionTypeId() !=null
                                            && question.getQuestionTypeId().equals(questionTypesDTO.getQuestionTypeId())
                                            && question.getSectionId().equals(questionTypesDTO.getSectionId())).collect(Collectors.toList());
                            questionList.sort(Comparator.comparing(SpecializedTrainingQuestionVo::getQuestionId));
                            questionTypesDTO.setQuestionList(questionList);
                        });
                        questionTypesDTOList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.QuestionTypesDTO::getQuestionTypeId));
                        sectionQuestionDetailDto.setQuestionTypes(questionTypesDTOList);
                    });
                    sectionList.sort(Comparator.comparing(ExamChapterQuestionDetailDto.SectionQuestionDetailDto::getSectionSortNo));
                    chapterQuestionDetailDto.setSectionList(sectionList);
                });
                chapterQuestionDetailDtos.sort(Comparator.comparing(ExamChapterQuestionDetailDto.ChapterQuestionDetailDto::getChapterSortNo));
                bookQuestionDetailDto.setChapterList(chapterQuestionDetailDtos);
            });
            ArrayList<ExamChapterQuestionDetailDto.BookQuestionDetailDto> bookList = new ArrayList<>(bookSet);
            examChapterQuestionDetailDto.setBookList(bookList);
            if (CollectionUtil.isNotEmpty(examChapterQuestionDetailDto.getBookList())) {
                examChapterQuestionDetailDto.setBookList(examChapterQuestionDetailDto.getBookList().stream().
                        filter(bookQuestionDetailDto ->
                                        bookQuestionDetailDto.getGrade() != null &&
                                                bookQuestionDetailDto.getSemester() != null &&
                                bookQuestionDetailDto.getChapterList().size() > 0
                                && bookQuestionDetailDto.getChapterList().get(0).getChapterId() != null).collect(Collectors.toList()));
            }

            examChapterQuestionDetailDto.getBookList().sort(Comparator.comparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getGrade)
                    .thenComparingInt(ExamChapterQuestionDetailDto.BookQuestionDetailDto::getSemester)
            );
//            examChapterQuestionDetailDto.setBookList(bookList);
            examChapterQuestionDetailDto.setTotalQuestions(questionSet.size());
            examChapterQuestionDetailDto.setTotalKnowledgePoints(pointsDTOSet.size());
            examChapterQuestionDetailDto.setTotalQuestionTypes(questionTypesDTOSet.size());
            return examChapterQuestionDetailDto;
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public List<ExamBookQuestionDetailDto> selectSpecializedTextBookTrainingExamDetail(UUID examId) {
        try {
            CreateTextBookQuestioVo createQuestionByKnowledgeVo = eduKnowledgeHubService.selectSpecializedTextBookTrainingExamDetail(examId);
            List<CreateTextBookQuestioVo.DataDTO> dataDTOList = createQuestionByKnowledgeVo.getData();
            Set<ExamBookQuestionDetailDto> bookSet = new HashSet<>();
            dataDTOList.forEach(dataDTO -> {
                QuestionInfoByIdDto questionInfoById = personalExamMapper.getQuestionInfoById(dataDTO.getId());
                dataDTO.setQuestionType(questionInfoById.getQuestionType());
                dataDTO.setContent(decodeContentV2(questionInfoById.getQuestionContent()));

                ExamBookQuestionDetailDto examBookQuestionDetailDto = new ExamBookQuestionDetailDto();
                ExamBookQuestionDetailDto.QuestionInfoDto examBookQuestionInfoDto = new ExamBookQuestionDetailDto.QuestionInfoDto();
                examBookQuestionDetailDto.setQuestionType(questionInfoById.getQuestionType());

                List<ExamBookQuestionDetailDto.QuestionInfoDto> questionInfoDtos = new ArrayList<>();
                examBookQuestionInfoDto.setQuestionId(dataDTO.getId());
                examBookQuestionInfoDto.setQuestionContent(decodeContentV2(questionInfoById.getQuestionContent()));
                examBookQuestionInfoDto.setOssUrl(questionInfoById.getOssUrl());
                examBookQuestionInfoDto.setAnswer(decodeContentV2(questionInfoById.getAnswer()));
                examBookQuestionInfoDto.setAnalyzeContent(decodeContentV2(questionInfoById.getAnalyzeContent()));
                examBookQuestionInfoDto.setSortNo(dataDTO.getSortNo());

                List<ExamBookQuestionDetailDto.KnowledgePointsDTO> pointsDTOSet = new ArrayList<>();
                dataDTO.getKnowledgePoints().forEach(knowledgePointsDTO -> {
                    ExamBookQuestionDetailDto.KnowledgePointsDTO knowledgePoint = new ExamBookQuestionDetailDto.KnowledgePointsDTO();
                    knowledgePoint.setKnowledgePointId(knowledgePointsDTO.getId());
                    knowledgePoint.setKnowledgePointName(knowledgePointsDTO.getName());
                    knowledgePoint.setSectionId(knowledgePointsDTO.getSectionId());
                    pointsDTOSet.add(knowledgePoint);
                });
                examBookQuestionInfoDto.setKnowledgePoints(pointsDTOSet);
                questionInfoDtos.add(examBookQuestionInfoDto);
                examBookQuestionDetailDto.setQuestionInfoDtos(questionInfoDtos);
                bookSet.add(examBookQuestionDetailDto);
            });
            List<ExamBookQuestionDetailDto> result = new ArrayList<>(bookSet);

            Map<String, List<ExamBookQuestionDetailDto.QuestionInfoDto>> groupedData = new HashMap<>();
            // 遍历原始列表，根据questionType分组
            for (ExamBookQuestionDetailDto item : result) {
                String qType = item.getQuestionType();
                List<ExamBookQuestionDetailDto.QuestionInfoDto> qInfos = item.getQuestionInfoDtos();
                if (!groupedData.containsKey(qType)) {
                    groupedData.put(qType, new ArrayList<>());
                }
                groupedData.get(qType).addAll(qInfos);
            }

            List<ExamBookQuestionDetailDto> resultList = new ArrayList<>();
            MathExamsEntity mathExamsEntity = mathExamsMapper.selectById(examId);
            // 遍历Map并创建ExamBookQuestionDetailDto对象
            for (Map.Entry<String, List<ExamBookQuestionDetailDto.QuestionInfoDto>> entry : groupedData.entrySet()) {
                String questionType = entry.getKey();
                List<ExamBookQuestionDetailDto.QuestionInfoDto> questionInfoDtos = entry.getValue();
                // 创建新的DTO对象
                ExamBookQuestionDetailDto newDto = new ExamBookQuestionDetailDto();
                newDto.setQuestionType(questionType);
                newDto.setBookStr(mathExamsEntity.getPublisher()+formatPublisher(mathExamsEntity.getGrade(), mathExamsEntity.getSemester()));
                newDto.setQuestionInfoDtos(questionInfoDtos);
                // 添加到结果列表中
                resultList.add(newDto);
            }

            return resultList;
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
    }
}
