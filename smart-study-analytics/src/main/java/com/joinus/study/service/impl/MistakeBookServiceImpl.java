package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.mapper.StudyRecordMapper;
import com.joinus.study.model.dto.QuestionAnswerDto;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.MistakeBookSourceEnum;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.enums.SubjectEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.mapper.MistakeBookMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.stream.IntStream;

/**
* <AUTHOR>
* @description 针对表【mistake_book】的数据库操作Service实现
* @createDate 2025-03-11 09:47:33
*/
@Service
public class MistakeBookServiceImpl extends ServiceImpl<MistakeBookMapper, MistakeBook>
    implements MistakeBookService{

    @Resource
    private QuestionKnowledgePointService questionKnowledgePointService;
    @Resource
    private StudyRecordMapper studyRecordMapper;
    @Resource
    private EduKnowLedgeHubBusinessService businessService;
    @Resource
    private ExamAnalyzeResultService resultService;
    @Resource
    private StudyRecordQuestionService studyRecordQuestionService;
    @Resource
    private QuestionAnswerFeedbackService feedbackService;
    @Override
    public void add(List<MistakeBookAddParam> mistakeBookAddParams) {
        List<MistakeBook> mistakeBookList = new ArrayList<>();
        mistakeBookAddParams.forEach(bookAddParam -> {
            CommonResponse.ERROR.assertNotNull(bookAddParam.getStudentId(), "studentId不能为空");
            CommonResponse.ERROR.assertNotNull(bookAddParam.getQuestionId(), "题目id不能为空！");
            CommonResponse.ERROR.assertNotNull(bookAddParam.getSource(), "来源不能为空！");
            CommonResponse.ERROR.assertNotNull(bookAddParam.getSubject(), "题目类型不能为空！");
            CommonResponse.ERROR.assertNotNull(bookAddParam.getStudyId(), "学习记录id不能为空！");
            //查询题目答案相关
            List<StudyRecordQuestionDetailsVo> questionDetailsVo = studyRecordMapper.getStudyQuestionAnswer(bookAddParam);
            CommonResponse.ERROR.assertCollNotNull(questionDetailsVo, "请勿加入错误题目！");
            CommonResponse.ERROR.assertNotNull(questionDetailsVo.get(0), "题目不完整！");
            CommonResponse.ERROR.assertNotEmpty(questionDetailsVo.get(0).getQuestionAnswer(), "题目答案不完整！");
            CommonResponse.ERROR.assertNotEmpty(questionDetailsVo.get(0).getQuestionAnswerContent(), "题目答案解析不完整！");
            MistakeBook mistakeBook = BeanUtil.copyProperties(bookAddParam, MistakeBook.class);
            mistakeBook.setSourceId(questionDetailsVo.get(0).getId());
            mistakeBook.setQuestionAnswer(questionDetailsVo.get(0).getQuestionAnswer());
            mistakeBook.setQuestionAnswerContent(questionDetailsVo.get(0).getQuestionAnswerContent());
            mistakeBook.setDifficulty(questionDetailsVo.get(0).getDifficulty());
            QueryWrapper<MistakeBook> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("student_id", mistakeBook.getStudentId());
            queryWrapper.eq("question_id", mistakeBook.getQuestionId());
            List<MistakeBook> mistakeBookBefore = this.baseMapper.selectList(queryWrapper);
            mistakeBook.setCreatedAt(new Date());
            if(CollectionUtils.isEmpty(mistakeBookBefore)){
                mistakeBookList.add(mistakeBook);
            }else {
                mistakeBook.setId(mistakeBookBefore.get(0).getId());
                mistakeBook.setUpdatedAt(new Date());
                //this.updateById(mistakeBook);
                baseMapper.editById(mistakeBook);

            }
        });
        //this.saveBatch(mistakeBookList);
        if (CollectionUtils.isNotEmpty(mistakeBookList)){
            baseMapper.batchInsert(mistakeBookList);
        }
    }

    @Override
    public Page pages(QueryMistakeBookParam param) {
        Page page = new Page(param.getCurrent(),param.getSize());
       List<MistakeBookVo> mistakeBookVoList=baseMapper.pages(page, param);
       mistakeBookVoList.forEach(mistakeBookVo -> {
           //处理知识点来源组装知识点
           if (MistakeBookSourceEnum.exam_diagnosis.equals(mistakeBookVo.getSource())){
               //获取题目图片
               mistakeBookVo.setFilesVos(baseMapper.getFilesByQuestionId(mistakeBookVo.getQuestionId()));
               mistakeBookVo.getFilesVos().forEach(filesVo -> {
                   PresignedUrlParam urlParam= new PresignedUrlParam();
                   urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
                   urlParam.setOssKey(filesVo.getOssUrl());
                   PresignedUrlVo presignedUrlVo = businessService.presignedUrl(urlParam);
                   if (presignedUrlVo != null&& presignedUrlVo.getData() != null){
                       filesVo.setOssUrl(presignedUrlVo.getData().getPresignedUrl());
                   }
               });
               //试卷诊断
               List<ExamAnalyzeResult> result = resultService.getByPersonalExamId(mistakeBookVo.getSourceId());
               if (ObjectUtil.isNotEmpty(result)){
                   List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePoints=questionKnowledgePointService.getListByExamIdAndQuestionId(result.get(0).getExamId(), mistakeBookVo.getQuestionId());
                   mistakeBookVo.setQuestionKnowledgePointList(knowledgePoints);
               }
           }else {
               //拍照时候
               List <StudyRecordQuestion> studyRecordQuestions= studyRecordQuestionService.getByStudyIdAndQuestionId(mistakeBookVo.getSourceId(), mistakeBookVo.getQuestionId());
               if (CollectionUtils.isNotEmpty(studyRecordQuestions)){
                   String knowledgePoint = studyRecordQuestions.get(0).getKnowledgePoint();
                  // 获取图片
                           List<String> list = Arrays.asList(studyRecordQuestions.get(0).getOssKeys().split(","));
                   List<FilesVo> filesVos = new ArrayList<>();
                           list.forEach(ossKey -> {
                               FilesVo filesVo = new FilesVo();
                               PresignedUrlParam urlParam= new PresignedUrlParam();
                               urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
                               urlParam.setOssKey(ossKey);
                               PresignedUrlVo presignedUrlVo = businessService.presignedUrl(urlParam);
                               if (presignedUrlVo != null&& presignedUrlVo.getData() != null){
                                   filesVo.setOssUrl(presignedUrlVo.getData().getPresignedUrl());
                               }
                               filesVos.add(filesVo);
                           });
                   mistakeBookVo.setFilesVos(filesVos);
                   if (StringUtils.isNotEmpty(knowledgePoint)){
                       ObjectMapper mapper = new ObjectMapper();
                       try {
                           List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointsList =
                                   mapper.readValue(knowledgePoint, new TypeReference<List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO>>(){});
                           mistakeBookVo.setQuestionKnowledgePointList(knowledgePointsList);
                       } catch (IOException e) {
                           log.error(e.getMessage());
                       }
                   }
               }
           }

         });
        page.setRecords(mistakeBookVoList);
        return page;
    }

    @Override
    public MistakeBookDetailsVo geDetails(QueryMistakeBookParam param) {
        CommonResponse.ERROR.assertNotNull(param.getId(), "错题id不能为空");
        MistakeBook mistakeBook = this.getById(param.getId());
        MistakeBookDetailsVo detailsVo = BeanUtil.copyProperties(mistakeBook, MistakeBookDetailsVo.class);
        //查询题目知识点根据来源获取
        if (MistakeBookSourceEnum.exam_diagnosis.equals(mistakeBook.getSource())){
            //试卷诊断
            List<ExamAnalyzeResult> result = resultService.getByPersonalExamId(mistakeBook.getSourceId());
            if (CollectionUtils.isNotEmpty(result)){
                List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePoints=questionKnowledgePointService.getListByExamIdAndQuestionId(result.get(0).getExamId(), mistakeBook.getQuestionId());
                detailsVo.setQuestionKnowledgePointList(knowledgePoints);
            }
        }else {
         List <StudyRecordQuestion> studyRecordQuestions= studyRecordQuestionService.getByStudyIdAndQuestionId(mistakeBook.getSourceId(), mistakeBook.getQuestionId());
          if (CollectionUtils.isNotEmpty(studyRecordQuestions)){
              String knowledgePoint = studyRecordQuestions.get(0).getKnowledgePoint();
              if (StringUtils.isNotEmpty(knowledgePoint)){
                  ObjectMapper mapper = new ObjectMapper();
                  try {
                      List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointsList =
                              mapper.readValue(knowledgePoint, new TypeReference<List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO>>(){});
                      detailsVo.setQuestionKnowledgePointList(knowledgePointsList);
                  } catch (IOException e) {
                      log.error(e.getMessage());
                  }
              }
          }
        }
        //查询题目详情和文件详情
        detailsVo.setQuestionVo(studyRecordMapper.getQuestionById(mistakeBook.getQuestionId()));
        PresignedUrlParam urlParam= new PresignedUrlParam();
        urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
        detailsVo.getQuestionVo().getFilesVos().forEach(filesVo -> {
            if (ObjectUtil.isNotEmpty(filesVo.getOssUrl())){
                urlParam.setOssKey(filesVo.getOssUrl());
                PresignedUrlVo presignedUrlVo = businessService.presignedUrl(urlParam);
                if (presignedUrlVo != null&& presignedUrlVo.getData() != null){
                    filesVo.setOssUrl(presignedUrlVo.getData().getPresignedUrl());
                }
            }
        });
        MistakeBookDetailsVo adjacentData =  baseMapper.getAdjacentData(param.getId(),mistakeBook.getStudentId());
        detailsVo.setCurrentIndex(adjacentData.getCurrentIndex());
        detailsVo.setNextId(adjacentData.getNextId());
        detailsVo.setBeforeId(adjacentData.getBeforeId());
        detailsVo.setQuestionTotal(adjacentData.getQuestionTotal());

        //是否点过赞
        List<QuestionAnswerFeedback> studyRecordQuestions = feedbackService.getIsUpvote(mistakeBook.getStudentId(), mistakeBook.getQuestionId());
        detailsVo.setIsUpvote(studyRecordQuestions.size()>0?1:0);
        return detailsVo;
    }

    @Override
    public Flux<String> getCompleteQuestionAnswerDto(Long id) {
        MistakeBook mistakeBook = this.getById(id);
        QuestionAnswerDto  questionAnswerDto= QuestionAnswerDto.builder()
                .answer(mistakeBook.getQuestionAnswer())
                .content(mistakeBook.getQuestionAnswerContent())
                .build();
        return businessService.createStreamFromQuestionAnswerDto(questionAnswerDto);
    }

    @Override
    public void examMistakeSave(ExamMistakeSaveParam param) {
        List<ExamMistakeInfoVo> examQuestionMisInfoList = this.baseMapper.getExamMistake(param.getStudentId(), param.getExamId());
        if(DataUtils.isNotEmpty(examQuestionMisInfoList)){
            for(ExamMistakeInfoVo eqmp : examQuestionMisInfoList){
                List<MistakeBook> mistakeBooks = this.baseMapper.selectList(Wrappers.lambdaQuery(MistakeBook.class)
                        .eq(MistakeBook::getStudentId, param.getStudentId()).eq(MistakeBook::getQuestionId, eqmp.getQuestionId()));
                if(DataUtils.isNotEmpty(mistakeBooks)){
                    MistakeBook mistakeBook = mistakeBooks.get(0);
                    if(DataUtils.isNotEmpty(eqmp.getDifficulty())){
                        mistakeBook.setDifficulty(eqmp.getDifficulty());
                    }
                    mistakeBook.setQuestionAnswer(eqmp.getQuestionAnswer());
                    mistakeBook.setQuestionAnswerContent(eqmp.getQuestionContent());
                    baseMapper.updateByMBId(mistakeBook);
                }else{
                    MistakeBook mistakeBook = new MistakeBook();
                    mistakeBook.setStudentId(param.getStudentId());
                    mistakeBook.setQuestionId(eqmp.getQuestionId());
                    mistakeBook.setSource(MistakeBookSourceEnum.exam_diagnosis);
                    mistakeBook.setSubject(SubjectEnum.math);
                    mistakeBook.setQuestionAnswer(eqmp.getQuestionAnswer());
                    mistakeBook.setQuestionAnswerContent(eqmp.getQuestionContent());
                    mistakeBook.setSourceId(eqmp.getPerExamId());
                    if(DataUtils.isNotEmpty(eqmp.getDifficulty())){
                        mistakeBook.setDifficulty(eqmp.getDifficulty());
                    }
                    this.baseMapper.insert(mistakeBook);
                }
            }
        }
    }

    @Override
    public void deleteById(Long id) {
        MistakeBook mistakeBook = new MistakeBook();
        mistakeBook.setUpdatedAt(new Date());
        mistakeBook.setDeletedAt(new Date());
        mistakeBook.setId(id);
        baseMapper.updateById(mistakeBook);
    }

    @Override
    public List<MistakeBook> getIsAddMistakesBook(Long studentId, UUID questionId, Long id, MistakeBookSourceEnum type) {
            LambdaQueryWrapper<MistakeBook> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MistakeBook::getStudentId, studentId);
            if (ObjectUtil.isNotEmpty(questionId)){
                queryWrapper.eq(MistakeBook::getQuestionId, questionId);

            }
            queryWrapper.eq(MistakeBook::getSourceId, id);
            queryWrapper.eq(MistakeBook::getSource, type.toString());
            queryWrapper.isNull(MistakeBook::getDeletedAt);
           return this.list(queryWrapper);
    }
}




