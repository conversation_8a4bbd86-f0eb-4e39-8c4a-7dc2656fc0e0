package com.joinus.study.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.mapper.ReadingPersonalPassagesMapper;
import com.joinus.study.mapper.ReadingPersonalPlanMapper;
import com.joinus.study.mapper.ReadingPersonalUserMapper;
import com.joinus.study.model.dto.ReadingWeakKnowledgePointDto;
import com.joinus.study.model.dto.ReadingWeakKnowledgePointsDto;
import com.joinus.study.model.entity.ReadingPersonalPlan;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.model.enums.GenreEnum;
import com.joinus.study.model.param.ReadingPlanPageParam;
import com.joinus.study.model.vo.ReadingPersonalPlanVo;
import com.joinus.study.service.ReadingPersonalHomePageService;
import com.joinus.study.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/28 10:47
 * description：阅读提分训练营首页相关
 */
@Service
@Slf4j
public class ReadingPersonalHomePageServiceImpl implements ReadingPersonalHomePageService {

    @Autowired
    private ReadingPersonalUserMapper readingPersonalUserMapper;
    @Autowired
    private ReadingPersonalPlanMapper readingPersonalPlanMapper;
    @Autowired
    private ReadingPersonalPassagesMapper readingPersonalPassagesMapper;


    @Override
    public void textbookAdd(ReadingPersonalUser vo) throws Exception {
        log.info("textbookAdd:{}", JSONUtil.toJsonStr(vo));
        CommonResponse.ERROR.assertNotNull(vo.getStudentId(), "学生id不能为空！");
        CommonResponse.ERROR.assertNotNull(vo.getGradeName(), "年级不能为空！");
        CommonResponse.ERROR.assertNotNull(vo.getSemester(), "学期不能为空！");
        CommonResponse.ERROR.assertNotNull(vo.getTextbook(), "教材不能为空！");
        CommonResponse.ERROR.assertNotNull(vo.getIsNew(), "isNew不能为空！");

        Integer grade = DataUtil.formatGrade(vo.getGradeName());
        vo.setGrade(grade);

        QueryWrapper<ReadingPersonalUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("student_id", vo.getStudentId());
        ReadingPersonalUser readingPersonalUser = readingPersonalUserMapper.selectOne(queryWrapper);
        if (readingPersonalUser != null) {
            UpdateWrapper<ReadingPersonalUser> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("student_id", vo.getStudentId());
            vo.setUpdatedAt(new Date());
            if (vo.getIsNew() != 1) {
                readingPersonalUserMapper.update(vo,updateWrapper);
            }
        } else {
            readingPersonalUserMapper.insert(vo);
        }
        log.info("textbookAdd success:{}", JSONUtil.toJsonStr(vo));
    }

    @Override
    public ReadingPersonalUser getTextbookInfo(Long studentId) throws Exception {
        QueryWrapper<ReadingPersonalUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("student_id", studentId);
        return readingPersonalUserMapper.selectOne(queryWrapper);
    }

    @Override
    public void planAdd(ReadingPersonalPlan vo) throws Exception {
        CommonResponse.ERROR.assertNotNull(vo.getPlanName(), "计划名称不能为空！");
        CommonResponse.ERROR.assertNotNull(vo.getStartDate(), "开始时间不能为空！");
        CommonResponse.ERROR.assertIsFalse(vo.getStartDate().getTime() > vo.getEndDate().getTime(), "开始时间不能大于结束时间！");
        CommonResponse.ERROR.assertIsFalse(vo.getStartDate().getTime() <= DateUtil.beginOfDay(new Date()).getTime(), "开始时间不能小于当前时间！");
        CommonResponse.ERROR.assertNotNull(vo.getStudentId(), "学生id不能为空！");
        CommonResponse.ERROR.assertNotNull(vo.getEndDate(), "结束时间不能为空！");
        CommonResponse.ERROR.assertNotNull(vo.getReadingCount(), "阅读次数不能为空！");
        //判断 一个周期只能有一个计划
        boolean isExist = readingPersonalPlanMapper.checkPlanByStudentIdAndDate(vo.getStudentId(), vo.getStartDate(), vo.getEndDate());
        CommonResponse.ERROR.assertIsFalse(isExist, "该时间段内已有计划，请重新选择时间段！");
        if(DataUtils.isNotEmpty(vo.getWeakKnowledgePointIds())){
            ReadingPersonalUser readingPersonalUser = readingPersonalUserMapper.selectOne(Wrappers.<ReadingPersonalUser>lambdaQuery()
                    .eq(ReadingPersonalUser::getStudentId, vo.getStudentId()));
            CommonResponse.ERROR.assertNotNull(readingPersonalUser, "学生信息错误！");
            Integer semester = readingPersonalUser.getSemester();
            Integer grade = readingPersonalUser.getGrade();
            List<String> genrePointList = Arrays.asList(vo.getWeakKnowledgePointIds().split(";"));
            List<ReadingWeakKnowledgePointDto> rwDTOs = new ArrayList<>();
            for(String genrePoint : genrePointList){
                String[] split = genrePoint.split(":");
                String genreKey = GenreEnum.getKeyByName(split[0]);
                List<UUID> pointIds = Arrays.stream(split[1].split(","))
                        .map(UUID::fromString).collect(Collectors.toList());
                ReadingWeakKnowledgePointDto rwDTO = new ReadingWeakKnowledgePointDto();
                rwDTO.setGrade(grade);
                rwDTO.setSemester(semester);
                rwDTO.setGenre(genreKey);
                rwDTO.setWeakKnowledgePointIds(pointIds);
                rwDTOs.add(rwDTO);
            }
            if(DataUtils.isNotEmpty(rwDTOs)){
                ReadingWeakKnowledgePointsDto readingWeakKnowledgePointsDto = new ReadingWeakKnowledgePointsDto();
                readingWeakKnowledgePointsDto.setPointsDtoList(rwDTOs);
                vo.setWeakKnowledgePoint(readingWeakKnowledgePointsDto);
            }
        }
        readingPersonalPlanMapper.insert(vo);
    }

    @Override
    public ReadingPersonalPlanVo getPlanInfoById(Long planId) throws Exception {
        ReadingPersonalPlan readingPersonalPlan = readingPersonalPlanMapper.selectById(planId);
        ReadingPersonalPlanVo readingPersonalPlanVo = new ReadingPersonalPlanVo();
        BeanUtils.copyProperties(readingPersonalPlan, readingPersonalPlanVo);
        if (DataUtils.isNotEmpty(readingPersonalPlanVo.getWeakKnowledgePoint())) {
            List<ReadingWeakKnowledgePointDto> pointsDtoList = readingPersonalPlanVo.getWeakKnowledgePoint().getPointsDtoList();
            List<String> idsList = pointsDtoList.stream()
                    .flatMap(dto -> dto.getWeakKnowledgePointIds().stream())
                    .distinct().map(UUID::toString).collect(Collectors.toList());
            List<Map<String, Object>> knowledgePointsNames = readingPersonalPlanMapper.getKnowledgePointsNames(idsList);
            Map<String, List<Map<String, Object>>> weakKnowledgePointNamesNew = new HashMap<>();
            for(ReadingWeakKnowledgePointDto pointDto : pointsDtoList){
                List<Map<String, Object>> list = new ArrayList<>();
                List<UUID> weakKnowledgePointIds = pointDto.getWeakKnowledgePointIds();
                for(UUID uid : weakKnowledgePointIds){
                    Map<String, Object> pointMap = knowledgePointsNames.stream()
                            .filter(map -> String.valueOf(uid).equals(String.valueOf(map.get("id"))))
                            .findFirst()
                            .orElse(null);
                    if (DataUtils.isNotEmpty(pointMap)) {
                        list.add(pointMap);
                    }
                }
                weakKnowledgePointNamesNew.put(GenreEnum.getByName(pointDto.getGenre()), list);
            }
            readingPersonalPlanVo.setWeakKnowledgePointNamesNew(weakKnowledgePointNamesNew);
        }
        /*int count = readingPersonalPlanMapper.selectPassagesByPlanId(planId);
        if (count >= readingPersonalPlan.getReadingCount()) {
            readingPersonalPlanVo.setFlag(1);
        }*/
        readingPersonalPlanVo.setStartDateStr(DateUtil.format(readingPersonalPlanVo.getStartDate(), "yyyy-MM-dd HH:mm:ss"));
        readingPersonalPlanVo.setEndDateStr(DateUtil.format( DateUtil.endOfDay(readingPersonalPlanVo.getEndDate()), "yyyy-MM-dd HH:mm:ss"));
        return readingPersonalPlanVo;
    }

    @Override
    public List<ReadingPersonalPlanVo> planUnderWayList(Long studentId) throws Exception {
        /*QueryWrapper<ReadingPersonalPlan> queryWrapper = new QueryWrapper<>();
        // 添加条件
        queryWrapper.eq("student_id", studentId)
                .ge("end_date", LocalDate.now())
                .gt("reading_count", LocalDate.now())
                .orderByAsc("ABS(EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - start_date)))")
                .last("LIMIT 3");
        // 执行查询
        List<ReadingPersonalPlan> plans = readingPersonalPlanMapper.selectList(queryWrapper);*/
        List<ReadingPersonalPlan> plans = readingPersonalPlanMapper.planUnderWayList(studentId);
        List<ReadingPersonalPlanVo> planVoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(plans)) {
            for (ReadingPersonalPlan plan : plans) {
                ReadingPersonalPlanVo readingPersonalPlanVo = new ReadingPersonalPlanVo();
                BeanUtils.copyProperties(plan, readingPersonalPlanVo);
                readingPersonalPlanVo.setStartDateStr(DateUtil.format(readingPersonalPlanVo.getStartDate(), "yyyy-MM-dd HH:mm:ss"));
                readingPersonalPlanVo.setEndDateStr(DateUtil.format( DateUtil.endOfDay(readingPersonalPlanVo.getEndDate()), "yyyy-MM-dd HH:mm:ss"));
                planVoList.add(readingPersonalPlanVo);
            }
        }
        return planVoList;
    }

    @Override
    public Page<ReadingPersonalPlanVo> planPages(ReadingPlanPageParam pageParam) {
        Page<ReadingPersonalPlanVo> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        List<ReadingPersonalPlanVo> list = readingPersonalPlanMapper.planPages(page, pageParam);
        if (!CollectionUtils.isEmpty(list)) {
            for (ReadingPersonalPlanVo plan : list) {
                plan.setStartDateStr(DateUtil.format(plan.getStartDate(), "yyyy-MM-dd HH:mm:ss"));
                plan.setEndDateStr(DateUtil.format(DateUtil.endOfDay(plan.getEndDate()), "yyyy-MM-dd HH:mm:ss"));
                if(DataUtils.isNotEmpty(plan.getPointsDtoList())){
                    plan.setIsHaveKnowledgePoint(1);
                }else{
                    plan.setIsHaveKnowledgePoint(0);
                }
            }
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public Map<String, List<Map<String, Object>>> getKnowledgePointsInfo(Long studentId) throws Exception {
        CommonResponse.ERROR.assertNotNull(studentId, "学生id不能为空！");
        QueryWrapper<ReadingPersonalUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("student_id", studentId);
        ReadingPersonalUser readingPersonalUser = readingPersonalUserMapper.selectOne(queryWrapper);
        CommonResponse.ERROR.assertNotNull(readingPersonalUser, "学生信息不存在！");
        List<Map<String, Object>> knowledgePointsInfoGroupByType =
                readingPersonalPlanMapper.getKnowledgePointsInfoGroupByType(readingPersonalUser.getGrade(), readingPersonalUser.getSemester());
        if(DataUtils.isNotEmpty(knowledgePointsInfoGroupByType)){
            Map<String, List<Map<String, Object>>> resultMap = knowledgePointsInfoGroupByType.stream()
                    .collect(Collectors.groupingBy(
                            entry -> (String) entry.get("genre"),
                            Collectors.mapping(entry -> {
                                Map<String, Object> idNameMap = new HashMap<>();
                                idNameMap.put("id", entry.get("id"));
                                idNameMap.put("name", entry.get("name"));
                                return idNameMap;
                            }, Collectors.toList())
                    )).entrySet().stream().collect(Collectors.toMap(
                            entry -> GenreEnum.getByName(String.valueOf(entry.getKey())),
                            Map.Entry::getValue
                    ));
            return resultMap;
        }
        return null;
        //return readingPersonalPlanMapper.getKnowledgePointsInfos();
    }
}
