package com.joinus.study.service;

import com.joinus.common.exception.BaseException;
import com.joinus.study.model.dto.ExamBookQuestionDetailDto;
import com.joinus.study.model.dto.ExamChapterQuestionDetailDto;
import com.joinus.study.model.dto.QuestionAnalyzeResultDto;
import com.joinus.study.model.dto.SpecializedTrainingUpdateExamDto;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description: 专项训练服务
 * @Author:  anpy
 * @date:  2025/4/28 17:33
 */
public interface SpecializedTrainingService {

    /**
     * 专项训练-根据知识点和题型生成题目
     * @param param 参数
     *              questionCount 题目数量
     *              knowledgePointId 知识点ID
     *              questionTypeIds 题型ID集合
     *
     * @return 生成的题目
     */
    SpecializedTrainingNewResultVo createQuestionByKnowledgeAndQuestionType(CreateQuestionByKnowledgeParam param);

    /**
     * 专项训练-根据题目解析结果
     * @param questionId 题目id
     * @return 解析结果
     */
    QuestionAnalyzeResultDto questionAnalyzeResult(UUID questionId,PublisherEnum publisher);

    /**
     * 专项训练-判断试卷是否存在专项训练-根据题目解析结果
     * @param qrStr 试卷id
     * @return 解析结果
     */
    CheckExamExsitenceVo examExist(String qrStr);
    /**
     * 专项训练-判断试卷是否存在专项训练-根据题目解析结果
     * @param personalExamId 个人试卷id
     * @return 解析结果
     */
    CheckExamExsitenceVo examExistByPersonalExamId(Long personalExamId);

    /**
     * 专项训练-保存试卷
     * @param param 参数
     *              examName 试卷名称
     *              questionIds 题目id集合
     * @return 试卷id
     */
    String saveExam(SpecializedTrainingCreateExamParam param);

    /**
     * 专项训练-更新试卷
     * @param param 参数参数
     *              examId 试卷id
     *              pdfUrl pdf文件key
     * @return
     */
    SpecializedTrainingUpdateExamDto updateExam(SpecializedTrainingUpdateExamParam param);

    SpecializedTrainingNewResultVoV2 createQuestionByKnowledgeAndQuestionTypeV2(CreateQuestionByKnowledgeParamV2 param) throws BaseException;

    String decodeContentV2(String content);

    ExamChapterQuestionDetailDto createQuestionByKnowledgeAndQuestionTypeV3(CreateQuestionByKnowledgeParamV2 param) throws BaseException;

    ExamChapterQuestionDetailDto createHolidayTrainingQuestion(CreateQuestionByKnowledgeParamV2 param) throws BaseException;

    List<ExamBookQuestionDetailDto> createHolidayTrainingBookQuestion(CreateQuestionByKnowledgeParamV2 param);
    /**
     * 考察范围-教材目录列表
     * @param examId 试卷id
     * @return 教材目录列表
     */
    TextbookCatalogLabelTree textbookCatalogue(Long analyzeReportId,UUID examId);

    /**
     * 更新考察范围
     * @param param 参数
     *              examScope 考察范围
     *              analyzeReportId 考情分析报告id
     */
    void updateExamScope(ExamScopeParam param);

    /**
     * 获取考察范围
     * @param analyzeReportId 考情分析报告id
     */
    TextbookCatalogLabelTree getExamScope(Long analyzeReportId);

    List<String> getManualExamScopeName(Long analyzeReportId);

    KnowledgePointBySelectVo getKnowledgePointBySection(Long analyzeReportId);

    /**
     * 专项训练-查询题目详情
     * @param questionId 专项训练-查询题目详情
     * @return
     */
    SpecializedTrainingQuestionVo getMathQuestionDetailById(UUID questionId, PublisherEnum publisher) throws BaseException;

    ExamChapterQuestionDetailDto selectSpecializedTrainingExamDetail(UUID examId);

    List<ExamBookQuestionDetailDto> selectSpecializedTextBookTrainingExamDetail(UUID examId);
}
