package com.joinus.study.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.common.exception.BaseException;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.mapper.ExamAnalyzeResultMapper;
import com.joinus.study.mapper.MathActivityWeekUnitMapper;
import com.joinus.study.mapper.MathActivityWeekUnitStudentMapper;
import com.joinus.study.model.bo.SpecialTrainingBo;
import com.joinus.study.model.dto.PresignedUrlDto;
import com.joinus.study.model.dto.SpecialTrainingDto;
import com.joinus.study.model.dto.SpecialTrainingPdfDto;
import com.joinus.study.model.entity.ExamAnalyzeResult;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.entity.QuestionKnowledgePoint;
import com.joinus.study.model.enums.ExamAnalyzeResultEnum;
import com.joinus.study.model.enums.ExamSourceType;
import com.joinus.study.model.enums.MathActivityWeekUnitTypeEnum;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.param.PresignedUrlParam;
import com.joinus.study.model.param.SpecialTrainingPdfParam;
import com.joinus.study.model.po.MathActivityWeekUnitPo;
import com.joinus.study.model.vo.ExamAnalyzeResultVo;
import com.joinus.study.model.vo.SpecialTrainingPdfVo;
import com.joinus.study.model.vo.SpecialTrainingVo;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.service.ExamAnalyzeResultService;
import com.joinus.study.service.PersonalExamService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExamAnalyzeResultServiceImpl extends ServiceImpl<ExamAnalyzeResultMapper, ExamAnalyzeResult> implements ExamAnalyzeResultService {

    @Resource
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Resource
    private PersonalExamService personalExamService;
    @Resource
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private MathActivityWeekUnitStudentMapper mathActivityWeekUnitStudentMapper;

    @Override
    public Page<ExamAnalyzeResultVo> situationAnalysisPage(Long parentId, Long studentId, Integer current, Integer size) {
        Page<ExamAnalyzeResultVo> page = new Page<ExamAnalyzeResultVo>(current == null ? 1 : current, size == null ? 10 : size);
        List<ExamAnalyzeResultVo> ExamAnalyzeResultVoList = this.baseMapper.selectExamAnalyzeResultList(page, studentId);
        ExamAnalyzeResultVoList.forEach(examAnalyzeResultVo -> {
            if (ExamAnalyzeResultEnum.INVALID.equals(examAnalyzeResultVo.getResult())) {
                String ossUrlString = examAnalyzeResultMapper.getOssUrlString(examAnalyzeResultVo.getExamId());
                if(DataUtils.isNotEmpty(ossUrlString)){
                    List<String> ossUrls = new ArrayList<>();
                    String[] split = ossUrlString.split(",");
                    for (String ossUrl : split) {
                        PresignedUrlParam param = new PresignedUrlParam();
                        param.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                        param.setOssKey(ossUrl);
                        PresignedUrlDto presignedUrlDto = eduKnowledgeHubService.presignedUrl(param);
                        if(DataUtils.isNotEmpty(presignedUrlDto) && DataUtils.isNotEmpty(presignedUrlDto.getData())){
                            ossUrls.add(presignedUrlDto.getData().getPresignedUrl());
                        }
                    }
                    examAnalyzeResultVo.setOssUrls(ossUrls);
                }
            }
        });
        page.setRecords(ExamAnalyzeResultVoList);
        return page;
    }

    @Override
    public Page<ExamAnalyzeResultVo> specializedRecordPage(Long parentId, Long studentId, Integer current, Integer size) {
        Page<ExamAnalyzeResultVo> page = new Page<ExamAnalyzeResultVo>(current == null ? 1 : current, size == null ? 10 : size);
        List<ExamAnalyzeResultVo> examAnalyzeResultVoList = this.baseMapper.selectSpecializedExamAnalyzeResultListV2(page, studentId);

        if (CollUtil.isEmpty(examAnalyzeResultVoList)) {
            return page;
        }
        List<UUID> holidayExamIds = examAnalyzeResultVoList.stream()
                .filter(examAnalyzeResultVo -> ExamSourceType.HOLIDAY_TRAINING.equals(examAnalyzeResultVo.getSource()))
                .map(ExamAnalyzeResultVo::getExamId)
                .collect(Collectors.toList());
        List<MathActivityWeekUnitPo> weekUnitExamIdPos = mathActivityWeekUnitStudentMapper.selectExamTypeByExamIds(holidayExamIds);

        Map<UUID, MathActivityWeekUnitTypeEnum> examIdWeenUnitTypeMap = weekUnitExamIdPos.stream()
                .collect(Collectors.toMap(
                        MathActivityWeekUnitPo::getExamId,
                        MathActivityWeekUnitPo::getType,
                        (existing, replacement) -> existing
                ));
        examAnalyzeResultVoList.forEach(examAnalyzeResultVo -> {
            if (ExamSourceType.HOLIDAY_TRAINING.equals(examAnalyzeResultVo.getSource())) {
                MathActivityWeekUnitTypeEnum mathActivityWeekUnitTypeEnum = examIdWeenUnitTypeMap.get(examAnalyzeResultVo.getExamId());
                examAnalyzeResultVo.setExamType(null != mathActivityWeekUnitTypeEnum ? mathActivityWeekUnitTypeEnum.getValue() : null);
            }
        });
        page.setRecords(examAnalyzeResultVoList);
        return page;
    }

    @Override
    public MathActivityWeekUnit getMathActivityWeekUnitByTrainingExamId(UUID examId) {
        return mathActivityWeekUnitStudentMapper.getMathActivityWeekUnitByTrainingExamId(examId);
    }

    @Override
    public SpecialTrainingVo specialTraining(Long examAnalyzeResultId) {
        ExamAnalyzeResult examAnalyzeResult = baseMapper.selectById(examAnalyzeResultId);
        List<QuestionKnowledgePoint> questionKnowledgePoints = personalExamService.listWeakKnowledgePoints(examAnalyzeResult.getPersonalExamId());
        if (CollUtil.isEmpty(questionKnowledgePoints)) {
            throw new BaseException("没有找到对应的薄弱点");
        }
        List<UUID> knowledgePointIds = questionKnowledgePoints.stream()
                .filter(questionKnowledgePoint -> null != questionKnowledgePoint.getKnowledgePointId())
                .map(questionKnowledgePoint -> questionKnowledgePoint.getKnowledgePointId())
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(knowledgePointIds)) {
            throw new BaseException("没有找到对应的知识点");
        }
        SpecialTrainingBo specialTrainingBo = SpecialTrainingBo.builder()
                .examId(examAnalyzeResult.getExamId())
                .knowledgePointsIds(knowledgePointIds)
                .build();
        SpecialTrainingDto specialTrainingDto = eduKnowledgeHubService.specialTraining(specialTrainingBo);
        return BeanUtil.copyProperties(specialTrainingDto, SpecialTrainingVo.class);
    }

    @Override
    public SpecialTrainingPdfVo specialTrainingPdf(SpecialTrainingPdfParam param) {
        ExamAnalyzeResult examAnalyzeResult = baseMapper.selectById(param.getExamAnalyzeResultId());
        SpecialTrainingBo specialTrainingBo = SpecialTrainingBo.builder()
                .examId(examAnalyzeResult.getExamId())
                .selectQuestion(param.getSelectQuestion())
                .selectAnswer(param.getSelectAnswer())
                .pdfUUID(param.getPdfUUID())
                .build();
        SpecialTrainingPdfDto specialTrainingDto = eduKnowledgeHubService.specialTrainingPdf(specialTrainingBo);
        return BeanUtil.copyProperties(specialTrainingDto, SpecialTrainingPdfVo.class);
    }

    @Override
    public SpecialTrainingPdfVo specialTrainingPdfPreview(SpecialTrainingPdfParam param) {
        ExamAnalyzeResult examAnalyzeResult = baseMapper.selectById(param.getExamAnalyzeResultId());
        SpecialTrainingBo specialTrainingBo = SpecialTrainingBo.builder()
                .examId(examAnalyzeResult.getExamId())
                .selectQuestion(param.getSelectQuestion())
                .selectAnswer(param.getSelectAnswer())
                .pdfUUID(param.getPdfUUID())
                .build();
        SpecialTrainingPdfDto specialTrainingDto = eduKnowledgeHubService.specialTrainingPdfPreview(specialTrainingBo);
        return BeanUtil.copyProperties(specialTrainingDto, SpecialTrainingPdfVo.class);
    }

    @Override
    public  List<ExamAnalyzeResult> getByPersonalExamId(Long sourceId) {
        LambdaQueryWrapper<ExamAnalyzeResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExamAnalyzeResult::getPersonalExamId, sourceId);
        queryWrapper.orderByDesc(ExamAnalyzeResult::getId);
        return this.list(queryWrapper);
    }

    @Override
    public ExamAnalyzeResult getExamAnalyzeResultById(Long examAnalyzeResultId) {
        return examAnalyzeResultMapper.selectById(examAnalyzeResultId);
    }


}
