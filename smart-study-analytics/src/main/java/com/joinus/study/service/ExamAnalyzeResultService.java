package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.entity.ExamAnalyzeResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.entity.MathActivityWeekUnit;
import com.joinus.study.model.param.SpecialTrainingPdfParam;
import com.joinus.study.model.vo.ExamAnalyzeResultVo;
import com.joinus.study.model.vo.SpecialTrainingPdfVo;
import com.joinus.study.model.vo.SpecialTrainingVo;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【exam_analyze_result】的数据库操作Service
* @createDate 2025-03-11 09:41:56
*/
public interface ExamAnalyzeResultService extends IService<ExamAnalyzeResult> {

    Page<ExamAnalyzeResultVo> situationAnalysisPage(Long parentId, Long studentId, Integer current, Integer size);

    SpecialTrainingVo specialTraining(Long param);

    SpecialTrainingPdfVo specialTrainingPdf(@Valid SpecialTrainingPdfParam param);

    SpecialTrainingPdfVo specialTrainingPdfPreview(SpecialTrainingPdfParam param);

    List<ExamAnalyzeResult> getByPersonalExamId(Long sourceId);

    ExamAnalyzeResult getExamAnalyzeResultById(Long examAnalyzeResultId);

    Page<ExamAnalyzeResultVo> specializedRecordPage(Long parentId, Long studentId, Integer current, Integer size);

    MathActivityWeekUnit getMathActivityWeekUnitByTrainingExamId(UUID examId);
}
