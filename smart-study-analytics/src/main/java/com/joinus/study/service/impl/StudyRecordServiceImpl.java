package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.joinus.common.model.response.CommonResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.model.dto.QuestionAnswerDto;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.enums.MistakeBookSourceEnum;
import com.joinus.study.model.enums.OssEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.*;
import com.joinus.study.mapper.StudyRecordMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【study_record】的数据库操作Service实现
* @createDate 2025-03-11 09:48:14
*/
@Service
public class StudyRecordServiceImpl extends ServiceImpl<StudyRecordMapper, StudyRecord>
    implements StudyRecordService{

    @Resource
   private QuestionKnowledgePointService questionKnowledgePointService;
    @Resource
    private StudyRecordQuestionService studyRecordQuestionService;
    @Resource
    private EduKnowLedgeHubBusinessService eduKnowLedgeHubBusinessService;
    @Autowired
    private   EduKnowledgeHubService eduKnowledgeHubService;

    @Resource
    private MistakeBookService mistakeBookService;
    @Resource
    private QuestionAnswerFeedbackService feedbackService;
    @Override
    public Page pages(QueryStudyRecordParam param) {
        //当size为null时，设置size为10
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生不能为空");
        Page page = new Page(null == param.getCurrent() ? 0 : param.getCurrent(), null == param.getSize() ? 10 : param.getSize());
        List<StudyRecordVo> studyRecordVos= this.baseMapper.pages(param, page);
        studyRecordVos.forEach(vo -> {
            if (StringUtils.isNotEmpty(vo.getOssUrl())){
                vo.setOssUrls(Arrays.asList(vo.getOssUrl().split(",")));
            }
            List<String> ossUrls = new ArrayList<>();
            vo.getOssUrls().forEach(ossUrl -> {
                PresignedUrlParam urlParam= new PresignedUrlParam();
                urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
                urlParam.setOssKey(ossUrl);
                PresignedUrlVo presignedUrlVo = eduKnowLedgeHubBusinessService.presignedUrl(urlParam);
                if (presignedUrlVo != null&& presignedUrlVo.getData() != null){
                    ossUrl = presignedUrlVo.getData().getPresignedUrl();
                    ossUrls.add(ossUrl);
                }
            });
            vo.setOssUrls(ossUrls);
        });
        page.setRecords(studyRecordVos);
        return page;
    }

    @Override
    public List<StudyRecordQuestionDetailsVo> detail(Long id) {
        List<StudyRecordQuestionDetailsVo> studyRecordVos= baseMapper.detail(id);
        studyRecordVos.forEach(vo -> {
            //查询是否加入过错题本
          List<MistakeBook> mistakeBooks =  mistakeBookService.getIsAddMistakesBook(vo.getStudentId(),vo.getQuestionId(), vo.getId(), MistakeBookSourceEnum.photo_question);
          //上面写成三元表达式
            vo.setIsAddMistakesBook(mistakeBooks.size()>0?1:0);
            //是否点过赞
            List<QuestionAnswerFeedback> studyRecordQuestions = feedbackService.getIsUpvote(vo.getStudentId(), vo.getQuestionId());
            vo.setIsUpvote(studyRecordQuestions.size()>0?1:0);
            //根据问题id查询问题
            String[] split = vo.getOssKeys().split(",");
            if (split.length>0){
                ArrayList<String> questionUrls = new ArrayList<>();
               for (String s : split) {
                   PresignedUrlParam urlParam= new PresignedUrlParam();
                   urlParam.setOssEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"));
                   urlParam.setOssKey(s);
                   PresignedUrlVo presignedUrlVo = eduKnowLedgeHubBusinessService.presignedUrl(urlParam);
                   if (presignedUrlVo != null&& presignedUrlVo.getData() != null){
                       questionUrls.add(presignedUrlVo.getData().getPresignedUrl());
                   }
               }
              vo.setQuestionUrl(questionUrls);
            }
            //根据问题id查询关联知识点
            String knowledgePoint = vo.getKnowledgePoint();
            if (StringUtils.isNotEmpty(knowledgePoint)){
                //knowledgePoint转换为jsonB
                ObjectMapper mapper = new ObjectMapper();
                try {
                    List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePointsList =
                            mapper.readValue(knowledgePoint, new TypeReference<List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO>>(){});
                    if (CollUtil.isNotEmpty(knowledgePointsList)){
                        vo.setQuestionKnowledgePointList(knowledgePointsList);
                    }
                } catch (IOException e) {
                    log.error(e.getMessage());
                }
            }else {
                //创建参数
                KnowledgePointsParams.KnowledgePointsParamsBuilder builder = KnowledgePointsParams.builder();
                builder.questionId(vo.getQuestionId())
                        .ossEnum(OssEnum.ofTypeAndBucket("aliyun", "edu-knowledge-hub"))
                        .studentId(vo.getStudentId()).studyRecordQuestionId(vo.getStudyRecordQuestionId()).fromExam(false);
                KnowledgePointsVO knowledgePointsVO = null;
                try {
                    knowledgePointsVO = eduKnowLedgeHubBusinessService.knowledgePoints(builder.build(), null, null);
                    if (knowledgePointsVO != null&& knowledgePointsVO.getData()!= null){
                        List<KnowledgePointsVO.DataDTO.KnowledgePointsDTO> knowledgePoints = knowledgePointsVO.getData().getKnowledgePoints();
                        if (CollUtil.isNotEmpty(knowledgePoints)){
                            vo.setQuestionKnowledgePointList(knowledgePoints);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }


        });
        return studyRecordVos;
    }

    @Override
    public Flux<String> questionDetail(Long studyId, String questionId, Long studentId) {
        Assert.isTrue(DataUtils.isNotEmpty(questionId), "问题信息不能为空！");
        Assert.isTrue(DataUtils.isNotEmpty(studyId), "学习记录信息不能为空！");
        Assert.isTrue(DataUtils.isNotEmpty(studentId), "学生信息不能为空！");
        List<Map<String, Object>> result = this.baseMapper.questionDetail(studyId, questionId, studentId);
        if(DataUtils.isNotEmpty(result)){
            QuestionAnswerDto content = QuestionAnswerDto.builder().answer(String.valueOf(result.get(0).get("questionAnswer")))
                    .content(String.valueOf(result.get(0).get("questionContent"))).build();
            return eduKnowLedgeHubBusinessService.createStreamFromQuestionAnswerDto(content);
        }else{
            //查看单题解析
            MistakeBookAddParam mistakeBookAddParam = new MistakeBookAddParam();
                mistakeBookAddParam.setStudentId(studentId);
                mistakeBookAddParam.setStudyId(studyId);
                mistakeBookAddParam.setQuestionId(UUID.fromString(questionId));
            List<StudyRecordQuestionDetailsVo> studyQuestionAnswer = baseMapper.getStudyQuestionAnswer(mistakeBookAddParam);
            if (CollectionUtils.isNotEmpty(studyQuestionAnswer)){
                SolveQuestionFromImgParam solveQuestionFromImgParam = new SolveQuestionFromImgParam();
                solveQuestionFromImgParam.setQuestionId(UUID.fromString(questionId));
                solveQuestionFromImgParam.setStudentId(studentId);
                solveQuestionFromImgParam.setStudyRecordQuestionId(studyQuestionAnswer.get(0).getStudyRecordQuestionId());
                //按照逗号分割图片key
                String[] split = studyQuestionAnswer.get(0).getOssKeys().split(",");
                solveQuestionFromImgParam.setObjectNames(Arrays.asList(split));
                solveQuestionFromImgParam.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                return  eduKnowLedgeHubBusinessService.getAiResultFluxData(solveQuestionFromImgParam);
            }
          return null;
        }
    }

    @Override
    public Flux<String> questionsDetail(QuestionDetailParam param) {
        List<String> result = this.baseMapper.questionsDetail(param.getStudyId(), param.getQuestionIds(), param.getStudentId());
        if(DataUtils.isNotEmpty(result)){
            String resultStr = String.join("\n", result);
            int chunkSize = 20; // 每块20字符
            Flux<String> streamFlux = Flux.range(0, (resultStr.length() + chunkSize - 1) / chunkSize)
                    .map(i ->
                            resultStr.substring(i * chunkSize,
                                    Math.min((i + 1) * chunkSize, resultStr.length())))
                    .delayElements(Duration.ofMillis(20));
            return streamFlux;
        }else{
            return null;
        }
    }


}




