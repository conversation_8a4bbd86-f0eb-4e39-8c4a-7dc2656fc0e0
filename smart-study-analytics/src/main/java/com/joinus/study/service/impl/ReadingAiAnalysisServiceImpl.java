package com.joinus.study.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.kafka.ReadingPersonalAnalysisReportSender;
import com.joinus.study.mapper.ReadingPersonalPassagesQuestionMapper;
import com.joinus.study.mapper.ReadingPersonalSuggestionAnalysisMapper;
import com.joinus.study.mapper.ReadingPersonalWeakKnowledgePointsAnalysisMapper;
import com.joinus.study.mapper.ReadingPersonalWeakQuestionTypeAnalysisMapper;
import com.joinus.study.model.entity.*;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.ReadingKnowledgePointVo;
import com.joinus.study.model.vo.ReadingQuestionTypeVo;
import com.joinus.study.service.ReadingAiAnalysisService;
import com.joinus.study.service.ReadingPersonalAnalysisPeriodicReportService;
import com.joinus.study.service.ReadingPersonalAnalysisReportService;
import com.joinus.study.service.ReadingPersonalPassagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/3/28 10:47
 * description：阅读提分训练营首页相关
 */
@Service
@Slf4j
public class ReadingAiAnalysisServiceImpl implements ReadingAiAnalysisService {

    @Value("${reading-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net/api/edu-knowledge-hub}")
    private String readingHubHostUrl;

    @Value("${reading-hub-common-url:/ai/reading/ability/common}")
    private String readingHubCommonUrl;

    @Resource
    @Lazy
    private ReadingPersonalAnalysisReportService reportService;

    @Resource
    @Lazy
    private ReadingPersonalAnalysisPeriodicReportService periodicReportService;

    @Resource
    private ReadingPersonalWeakKnowledgePointsAnalysisMapper weakKnowledgePointsAnalysisMapper;

    @Resource
    private ReadingPersonalSuggestionAnalysisMapper suggestionAnalysisMapper;

    @Resource
    private ReadingPersonalWeakQuestionTypeAnalysisMapper weakQuestionTypeAnalysisMapper;

    @Resource
    private ReadingPersonalAnalysisReportSender readingPersonalAnalysisReportSender;
    @Resource
    private ReadingPersonalPassagesQuestionMapper questionMapper;

    @Resource
    @Lazy
    private ReadingPersonalPassagesService personalPassagesService;

    @Override
    public void weakKnowledgePointsAnalysisAIRequest(ReadingAIAbilityParam param) {
        String url = readingHubHostUrl+"/ai/reading/ability/weak-knowledge-point/analysis";
        log.info("weakKnowledgePointsAnalysisAIRequest-url:{}- 参数：{}",url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.post(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .timeout(60000)
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("weakKnowledgePointsAnalysisAIRequest 返回结果：{}" ,result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    System.out.println("weakKnowledgePointsAnalysisAIRequest 调用AI能力接口成功");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void suggestionAnalysesAIRequest(ReadingAIAbilityParam param) {
        String url = readingHubHostUrl+"/ai/reading/ability/training-suggestion";
        log.info("suggestionAnalysesAIRequest-url:{}- 参数：{}", url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.post(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .timeout(60000)
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("weakKnowledgePointsAnalysisAIRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    System.out.println("suggestionAnalysesAIRequest 调用AI能力接口成功");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void weakQuestionTypeAnalysesAIRequest(ReadingAIAbilityParam param) {
        String url = readingHubHostUrl+"/ai/reading/ability/weak-question-type/analysis";
        log.info("weakQuestionTypeAnalysesAIRequest-url:{}- 参数：{}", url, param);
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.post(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .timeout(60000)
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("weakQuestionTypeAnalysesAIRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    System.out.println("weakQuestionTypeAnalysesAIRequest 调用AI能力接口成功");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void analysesAIRequest(ReadingAIAbilityParam param) {
        String url = readingHubHostUrl+readingHubCommonUrl;
        log.info("analysesAIRequest-url:{}- 参数：{}", url, JSONUtil.toJsonStr(param));
        try {
            // 创建HttpRequest对象，并设置请求URL和HTTP方法，设置请求头部并发送请求
            String result = HttpRequest.post(url) // 使用put方法创建HttpRequest对象
                    .body(JSONUtil.toJsonStr(param)) //请求参数
                    .timeout(3000)
                    .execute() // 执行请求
                    .body(); // 获取响应体
            log.info("analysesAIRequest 返回结果：{}", result);
            if (ObjectUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                if(jsonObject.getInt("code") == 200){
                    log.info("analysesAIRequest 调用AI能力接口成功");
                }
            }
        }catch (Exception e){
            log.error("analysesAIRequest 调用AI能力接口失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void weakKnowledgePointsAnalysisResults(ReadingAIWeakKnowledgePointsParam param) {
        log.info("weakKnowledgePointsAnalysisResults 接口入参：{}", param);
        CommonResponse.ERROR.assertNotNull(param.getId(), "业务id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getType(), "业务类型不能为空");
        CommonResponse.ERROR.assertNotNull(param.getItems(), "分析明细不能为空");

        //检查是否已有数据，有的话直接删除
        List<ReadingPersonalWeakKnowledgePointsAnalysis> weaPointsAnalysisList = weakKnowledgePointsAnalysisMapper.selectList(
                Wrappers.<ReadingPersonalWeakKnowledgePointsAnalysis>lambdaQuery()
                        .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getReportId, Long.valueOf(param.getId()))
                        .eq(ReadingPersonalWeakKnowledgePointsAnalysis::getAnalysisType, param.getType())
                        .isNull(ReadingPersonalWeakKnowledgePointsAnalysis::getDeletedAt));
        if(ObjectUtil.isNotEmpty(weaPointsAnalysisList)){
            for (ReadingPersonalWeakKnowledgePointsAnalysis item : weaPointsAnalysisList){
                item.setDeletedAt(new Date());
                item.setUpdatedAt(new Date());
                weakKnowledgePointsAnalysisMapper.updateById(item);
            }
        }

        ReadingPersonalKnowledgePointDataParam dataParam = new ReadingPersonalKnowledgePointDataParam();
        //确定业务类型
        if(param.getType() == 1){
            //单次训练分析报告
            ReadingPersonalAnalysisReport report = reportService.getById(Long.valueOf(param.getId()));
            CommonResponse.ERROR.assertNotNull(report, "找不到对应提分训练学情分析报告，id："+param.getId()+"异常");
            dataParam.setPersonalPassageId(report.getPersonalPassageId());
            dataParam.setStudentId(report.getStudentId());
            dataParam.setResult(0);
        }else{
            //月报或周报分析报告
            ReadingPersonalAnalysisPeriodicReport periodicReport = periodicReportService.getById(Long.valueOf(param.getId()));
            CommonResponse.ERROR.assertNotNull(periodicReport, "找不到对应学情分析报告，id："+param.getId()+"异常");
            dataParam.setStartDate(periodicReport.getStartDate());
            dataParam.setEndDate(periodicReport.getEndDate());
            dataParam.setStudentId(periodicReport.getStudentId());
            dataParam.setResult(0);
        }
        for (ReadingAIWeakKnowledgePointsItemParam item : param.getItems()) {
            dataParam.setKnowledgePointName(item.getKnowledgePoint());
            ReadingPersonalWeakKnowledgePointsAnalysis insertItem = new ReadingPersonalWeakKnowledgePointsAnalysis();
            //获取知识点统计数量数据
            log.info("weakKnowledgePointsAnalysisResults dataParam：{}", dataParam);
            List<ReadingKnowledgePointVo> knowledgePointDataList = reportService.queryPersonalPassageKnowledgePointData(dataParam);
            if(ObjectUtil.isEmpty(knowledgePointDataList)){
                continue;
            }
            log.info("weakKnowledgePointsAnalysisResults knowledgePointDataList：{}", knowledgePointDataList);
            ReadingKnowledgePointVo knowledgePointData = knowledgePointDataList.get(0);//根据知识点名称查询的 只能有一条
            insertItem.setReportId(Long.valueOf(param.getId()));
            insertItem.setWeakKnowledgePointId(knowledgePointData.getId());
            insertItem.setWeakKnowledgePointName(item.getKnowledgePoint());
            insertItem.setQuestionType(item.getQuestionType());
            insertItem.setWeaknessAnalysis(item.getWeaknessAnalysis());
            insertItem.setStrengths(item.getStrengths());
            insertItem.setSuggestions(item.getSuggestions());
            insertItem.setTotalQuestionCount(knowledgePointData.getTotalQuestions());
            insertItem.setWrongAnswerCount(knowledgePointData.getIncorrectCount());
            insertItem.setAnalysisType(param.getType());
            weakKnowledgePointsAnalysisMapper.insert(insertItem);
        }
        //判断报告是否分析完毕 如果分析完毕 更新状态
        updateReportStatus(Long.valueOf(param.getId()), param.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suggestionAnalysesResults(ReadingAISuggestionParam param) {
        log.info("suggestionAnalysesResults 接口入参：{}", param);
        CommonResponse.ERROR.assertNotNull(param.getId(), "业务id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getType(), "业务类型不能为空");
        CommonResponse.ERROR.assertNotNull(param.getItems(), "分析明细不能为空");

        //检查是否已有数据，有的话直接删除
        List<ReadingPersonalSuggestionAnalysis> suggestionAnalyses = suggestionAnalysisMapper.selectList(
                Wrappers.<ReadingPersonalSuggestionAnalysis>lambdaQuery()
                        .eq(ReadingPersonalSuggestionAnalysis::getReportId, Long.valueOf(param.getId()))
                        .eq(ReadingPersonalSuggestionAnalysis::getAnalysisType, param.getType())
                        .isNull(ReadingPersonalSuggestionAnalysis::getDeletedAt));
        if(ObjectUtil.isNotEmpty(suggestionAnalyses)){
            for (ReadingPersonalSuggestionAnalysis item : suggestionAnalyses){
                item.setDeletedAt(new Date());
                item.setUpdatedAt(new Date());
                suggestionAnalysisMapper.updateById(item);
            }
        }

        for (ReadingAISuggestionItemParam item : param.getItems()){
            ReadingPersonalSuggestionAnalysis insertItem = new ReadingPersonalSuggestionAnalysis();
            insertItem.setReportId(Long.valueOf(param.getId()));
            insertItem.setSuggestionTitle(item.getSuggestionTitle());
            insertItem.setContent(item.getContent());
            insertItem.setAnalysisType(param.getType());
            suggestionAnalysisMapper.insert(insertItem);
        }

        //判断报告是否分析完毕 如果分析完毕 更新状态
        updateReportStatus(Long.valueOf(param.getId()), param.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void weakQuestionTypeAnalysesResults(ReadingAIWeakQuestionTypeParam param) {
        log.info("weakQuestionTypeAnalysesResults 接口入参：{}", param);
        CommonResponse.ERROR.assertNotNull(param.getId(), "业务id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getType(), "业务类型不能为空");
        CommonResponse.ERROR.assertNotNull(param.getItems(), "分析明细不能为空");

        //检查是否已有数据，有的话直接删除
        List<ReadingPersonalWeakQuestionTypeAnalysis> weakQuestionTypeAnalysisList = weakQuestionTypeAnalysisMapper.selectList(
                Wrappers.<ReadingPersonalWeakQuestionTypeAnalysis>lambdaQuery()
                .eq(ReadingPersonalWeakQuestionTypeAnalysis::getReportId, Long.valueOf(param.getId()))
                .eq(ReadingPersonalWeakQuestionTypeAnalysis::getAnalysisType, param.getType())
                .isNull(ReadingPersonalWeakQuestionTypeAnalysis::getDeletedAt));
        if(ObjectUtil.isNotEmpty(weakQuestionTypeAnalysisList)){
            for (ReadingPersonalWeakQuestionTypeAnalysis item : weakQuestionTypeAnalysisList){
                item.setDeletedAt(new Date());
                item.setUpdatedAt(new Date());
                weakQuestionTypeAnalysisMapper.updateById(item);
            }
        }

        ReadingPersonalQuestionTypeDataParam dataParam = new ReadingPersonalQuestionTypeDataParam();

        //确定业务类型
        if(param.getType() == 1){
            //单次训练分析报告
            ReadingPersonalAnalysisReport report = reportService.getById(Long.valueOf(param.getId()));
            CommonResponse.ERROR.assertNotNull(report, "找不到对应提分训练学情分析报告，id："+param.getId()+"异常");
            dataParam.setPersonalPassageId(report.getPersonalPassageId());
            dataParam.setStudentId(report.getStudentId());
            dataParam.setResult(0);
        }else{
            //月报或周报分析报告
            ReadingPersonalAnalysisPeriodicReport periodicReport = periodicReportService.getById(Long.valueOf(param.getId()));
            CommonResponse.ERROR.assertNotNull(periodicReport, "找不到对应学情分析报告，id："+param.getId()+"异常");
            dataParam.setStartDate(periodicReport.getStartDate());
            dataParam.setEndDate(periodicReport.getEndDate());
            dataParam.setStudentId(periodicReport.getStudentId());
            dataParam.setResult(0);
        }

        for (ReadingAIWeakQuestionTypeItemParam item : param.getItems()){
            ReadingPersonalWeakQuestionTypeAnalysis insertItem = new ReadingPersonalWeakQuestionTypeAnalysis();
            insertItem.setReportId(Long.valueOf(param.getId()));
            insertItem.setQuestionType(item.getQuestionType());

            dataParam.setQuestionType(item.getQuestionType());
            //获取题型统计数量数据
            List<ReadingQuestionTypeVo> questionTypeDataList = reportService.queryPersonalPassageQuestionTypeData(dataParam);
            if(ObjectUtil.isEmpty(questionTypeDataList)){
                continue;
            }
            ReadingQuestionTypeVo questionTypeData = questionTypeDataList.get(0);//根据题型查询的 只能有一条

            insertItem.setErrorRate(questionTypeData.getErrorRate());
            insertItem.setSuggestion(item.getSuggestion());
            insertItem.setInterventionPlan(item.getInterventionPlan());
            insertItem.setAnalysisType(param.getType());
            weakQuestionTypeAnalysisMapper.insert(insertItem);
        }

        //判断报告是否分析完毕 如果分析完毕 更新状态
        updateReportStatus(Long.valueOf(param.getId()), param.getType());
    }

    @Override
    public void analysesResults(ReadingAICallBackParam param) {
        log.info("analysesResults 接口入参：{}", JSONUtil.toJsonStr(param));
        CommonResponse.ERROR.assertNotNull(param.getId(), "业务id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getReadingAIAbilityType(), "AI能力类型不能为空");
        if(param.getReadingAIAbilityType() == 5){
            dealReportAnalysisAnswerContrast(param);
        } else if (param.getReadingAIAbilityType() == 6){
            // 次报告分析
            dealReportAnalysisResult(param);
        } else if (param.getReadingAIAbilityType() == 7) {
            //周报分析
            dealPeriodicReportAnalysisResult(param);
        } else{
            CommonResponse.ERROR.assertNotNull(param.getResult(), "未知的类型暂不处理");
        }
    }

    public static void main(String[] args) {
        String result = "[\"9291acf1-da15-4771-996e-e295b8ac38c1###\\n{\\n  \\\"answerAdvice\\\": \\\"太棒了！你的答案完全正确，所有连线都匹配无误：直线爬行时间长对应蜜源距离远，腹部摆动频繁对应花蜜香甜，8字舞倾斜角度对应飞行方向。这说明你理解了蜜蜂舞蹈的含义，继续保持这种仔细阅读的好习惯哦！\\\",\\n  \\\"referenceAnswer\\\": \\\"直线爬行时间长→蜜源距离远；腹部摆动频繁→花蜜香甜；8字舞倾斜角度→飞行方向\\\"\\n}\",\"956ff1bb-e076-4e80-bb6f-3a743c4b64e3###\\n{\\n  \\\"answerAdvice\\\": \\\"你没有写出答案呢！参考答案概括了蜜蜂舞蹈的三大信息要素。下次勇敢地写下你的想法吧，我会帮你看看哪里写得好、哪里可以更完整，这样学习更有趣哦！\\\",\\n  \\\"referenceAnswer\\\": \\\"**具体概括**\\\"\\n}\",\"511f54f5-1aef-4d7b-91d9-e87a398ad0a4###{\\n  \\\"answerAdvice\\\": \\\"你没有填写流程图中的空格，参考答案是跳8字舞和接收振动信号。蜜蜂通过跳舞传递信息，接收振动信号确认位置。下次勇敢尝试填写，你会更理解蜜蜂的聪明行为哦！\\\",\\n  \\\"referenceAnswer\\\": \\\"**跳8字舞；接收振动信号**\\\"\\n}\",\"354096db-e842-4200-98d9-93b9b5616ca0###\\n{\\n  \\\"answerAdvice\\\": \\\"你的回答是‘正确’，但正确答案是‘错误’。题目说蜜蜂的8字舞倾斜角度与太阳方位无关，仅指示蜜源方向，所以这个说法是错误的。你答错了，但别灰心，下次仔细读题，多思考细节就能答对啦！\\\",\\n  \\\"referenceAnswer\\\": \\\"**×**\\\"\\n}\",\"3bd2aa37-02c4-4abe-9526-de993a8ced2f###{\\n  \\\"answerAdvice\\\": \\\"你选择了C，完全正确！本文主要说明信息传递，你准确地把握了文章的核心。这表明你认真阅读并理解了内容。在选择题中，仔细分析每个选项很重要，你做得很好。继续努力，保持这种准确性！\\\",\\n  \\\"referenceAnswer\\\": \\\"C\\\"\\n}\"]";
        JSONArray jsonArray = new JSONArray();
        Pattern pattern = Pattern.compile("([a-f0-9\\-]+)###(\\{.*?\\})");
        Matcher matcher = pattern.matcher(result);
        while (matcher.find()) {
            String uuid = matcher.group(1);
            String jsonString = matcher.group(2);
            JSONObject entries = new JSONObject(jsonString);
            entries.set("uuid", uuid);
            jsonArray.add(entries);
        }
        System.out.println();
    }

    public void dealReportAnalysisAnswerContrast(ReadingAICallBackParam param){
        log.info("dealReportAnalysisAnswerContrast 获取答案对比信息：{}", JSONUtil.toJsonStr(param));
        JSONArray jsonArray = new JSONArray();
        List<String> list = (List<String>) param.getResult();
        for(String str : list){
            String[] split = str.split("###");
            if(split.length > 1){
                JSONObject entries = new JSONObject();
                entries.set("uuid", split[0]);
                JSONObject resultObject = JSONUtil.parseObj(split[1]);
                entries.set("answerAdvice", resultObject.get("answerAdvice"));
                entries.set("referenceAnswer", resultObject.get("referenceAnswer"));
                jsonArray.add(entries);
            }
        }
        log.info("dealReportAnalysisAnswerContrast 处理结果信息：{}", JSONUtil.toJsonStr(jsonArray));
        List<ReadingPersonalPassagesQuestions> readingPersonalPassagesQuestions = questionMapper.selectList(
                Wrappers.<ReadingPersonalPassagesQuestions>lambdaQuery().eq(ReadingPersonalPassagesQuestions::getPersonalPassageId, Long.parseLong(param.getId())));
        if(DataUtils.isNotEmpty(readingPersonalPassagesQuestions)){
            readingPersonalPassagesQuestions.forEach(question -> {
                Optional<JSONObject> matchingObject = jsonArray.stream()
                        .map(obj -> (JSONObject) obj)
                        .filter(jsonObject -> jsonObject.getStr("uuid").equals(String.valueOf(question.getQuestionId())))
                        .findFirst();
                matchingObject.ifPresent(jsonObject -> {
                    question.setAnswerAdvice(jsonObject.getStr("answerAdvice"));
                    question.setReferenceAnswer(jsonObject.getStr("referenceAnswer"));
                    questionMapper.updateById(question);
                });
            });
        }
    }

    public void dealPeriodicReportAnalysisResult(ReadingAICallBackParam param){
        ReadingPersonalAnalysisPeriodicReport periodicReport = periodicReportService.getById(Long.valueOf(param.getId()));
        log.info("dealPeriodicReportAnalysisResult 获取报告信息：{}", periodicReport);
        CommonResponse.ERROR.assertNotNull(periodicReport, "数据异常，找不到对应报告信息");
        periodicReport.setStatus(1);
        periodicReport.setAnalysesResult(JSONUtil.toJsonStr(param.getResult()));
        periodicReportService.updateById(periodicReport);
    }

    // 语文次报告回调处理
    public void dealReportAnalysisResult(ReadingAICallBackParam param) {
        ReadingPersonalAnalysisReport report = reportService.getById(Long.valueOf(param.getId()));
        log.info("dealReportAnalysisResult 获取报告信息：{}", report);
        CommonResponse.ERROR.assertNotNull(report, "数据异常，找不到对应报告信息");
        report.setStatus(1);
        report.setAnalysesResult(JSONUtil.toJsonStr(param.getResult()));
        reportService.updateById(report);

        ReadingPersonalPassages personalPassages = personalPassagesService.getById(report.getPersonalPassageId());
        if (ObjectUtil.isNotEmpty(personalPassages)
                && DateUtil.compare(personalPassages.getEndAt(),new Date(2025,07,23,15,00,00)) < 0) {
            return;
        }
        //判断报告是否分析完毕 如果分析完毕 更新状态
        List<Map<String, Object>> passageInfo = reportService.queryPassageInfo(Long.valueOf(param.getId()));
        if(DataUtils.isNotEmpty(passageInfo)){
            //单次分析完成后  发送通知
            Map<String, String> message = Map.of(
                    "senderType", "system",
                    "studentId", String.valueOf(report.getStudentId()),
                    "messageType", "40",
                    "messageTitle", "学情分析报告生成提醒",
                    "content", "你练习的【" + String.valueOf(passageInfo.get(0).get("passageTitle")) + "】阅读理解习题学情报告已生成！",
                    "bizId", String.valueOf(Long.valueOf(param.getId())),
                    "isJPush", "1"
            );
            readingPersonalAnalysisReportSender.sendAnalysisReportMsgToQylMsg(message);
        }
    }

    private void updateReportStatus(Long reportId, Integer type){
        if(type == 1){
            if(reportService.checkReportIsAnalysisCompleted(reportId)){
                ReadingPersonalAnalysisReport report = reportService.getById(reportId);
                report.setStatus(1);
                report.setUpdatedAt(new Date());
                reportService.updateById(report);
                List<Map<String, Object>> passageInfo = reportService.queryPassageInfo(reportId);
                if(DataUtils.isNotEmpty(passageInfo)){
                    //单次分析完成后  发送通知
                    Map<String, String> message = Map.of(
                            "senderType", "system",
                            "studentId", String.valueOf(report.getStudentId()),
                            "messageType", "40",
                            "messageTitle", "学情分析报告生成提醒",
                            "content", "你练习的【" + String.valueOf(passageInfo.get(0).get("passageTitle")) + "】阅读理解习题学情报告已生成！",
                            "bizId", String.valueOf(reportId),
                            "isJPush", "1"
                    );
                    readingPersonalAnalysisReportSender.sendAnalysisReportMsgToQylMsg(message);
                }else{
                    log.info("单次练习分析完成回调未查询到分析完成数据 接口入参reportId：{}", reportId);
                }
            }
        }else{
            if(periodicReportService.checkPeriodicReportIsAnalysisCompleted(reportId)){
                ReadingPersonalAnalysisPeriodicReport report = periodicReportService.getById(reportId);
                report.setStatus(1);
                report.setUpdatedAt(new Date());
                periodicReportService.updateById(report);
            }
        }
    }
}
