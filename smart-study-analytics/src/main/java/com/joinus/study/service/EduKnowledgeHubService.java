package com.joinus.study.service;

import com.joinus.common.exception.BaseException;
import com.joinus.study.model.bo.QueryElectronicTextbookBo;
import com.joinus.study.model.bo.SpecialTrainingBo;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * @Description: AI服务逻辑
 * @Author:  anpy
 * @date:  2025/3/12 10:56
 */
public interface EduKnowledgeHubService {
    /**
     * 查询题目坐标信息
     */
    QuestionCoordinateDto questionCoordinateInfo(QueryQuestionCoordinateParam param);

    Flux<String> chatStream(SolveQuestionFromImgParam param);

    /**
     * 抹除题目手写笔迹
     */
    EraseHandwritingFromQuestionDto eraseHandwritingFromQuestion(EraseHandwritingFromQuestionParam param);

    QueryMultiQuestionResultDto queryMultiQuestionAnalysisResults(QueryMultiQuestionResultParam param);

    /**
     * 查询单题解析结果
     * @param param
     * @return
     */
    Flux<String> querySingleQuestionAnalysisResults(SolveQuestionFromImgParam param);

    /**
     * 试卷分析-试卷题目标注
     * @param param
     * @return
     */
    ExamAnalysisQuestionLabelDto examAnalysisQuestionLabel(ExamAnalysisQuestionLabelParam param);

    /**
     * 试卷诊断-判断试卷是否存在
     * @param param
     * @return
     */
    CheckExamExsitenceDto checkExamExistence(CheckExamExsitenceParam param);

    KnowledgePointsVO knowledgePoints(KnowledgePointsParams param);

    /**
     * 根据图片和坐标切题
     */
    CutImageFromPositionsDto cutImageFromPositions(SingleCutImageFromPositionsParam cutImageFromPositionsParam);

    OssTokenDto ossToken(OssTokenParam param);

    /**
     * 判断题目是否存在图片
     * @param param 题目文本
     * @return
     *      data：true/false
     */
    CheckExistGraphicsDto checkExistGraphics(CheckExistGraphicsParam param);

    /**
     * 获取图片临时链接
     */
    PresignedUrlDto presignedUrl(PresignedUrlParam param);

    /**
     * 切题后判断题目是否已存在
     */
    CheckExistQuestionDto checkExistQuestion(CheckExistQuestionParam param);


    SpecialTrainingDto specialTraining(SpecialTrainingBo param);

    SpecialTrainingPdfDto specialTrainingPdf(SpecialTrainingBo param);

    SpecialTrainingPdfDto specialTrainingPdfPreview(SpecialTrainingBo bo);

    UUID queryFlexiblyGenerating(FlexiblyQuestionParam param);

    /**
     * 生成试卷
     * @param param
     */
    CreateExamDto createExam(CreateExamParam param);

    ExamAnalyzeVo examAnalyze(AiAnalyticsExamParam param);

    ParseAnswerVo parseAnswer(ParseAnswerParam param);

    ExamHasKnowledgeDto examHasKnowledge(ExamHasKnowledgeParam param);

    CreateQuestionByKnowledgeVo createQuestionByKnowledgeAndQuestionType(CreateQuestionByKnowledgeParam param);
    CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeAndQuestionTypeV2(CreateQuestionByKnowledgeParamV2 param) throws BaseException;

    CreateQuestionByKnowledgeVoV2 createHolidayTrainingSectionQuestion(CreateQuestionByKnowledgeParamV2 param) throws BaseException;
    CreateQuestionByKnowledgeVoV2 createHolidayTrainingChapterQuestion(CreateQuestionByKnowledgeParamV2 param) throws BaseException;
    CreateTextBookQuestioVo createHolidayTrainingTextBookQuestion(CreateQuestionByKnowledgeParamV2 param) throws BaseException;

    /**
     * 根据知识点获取题型
     * @param knowledgePointIds 知识点id
     * @return 题型列表
     */
    SpecialTrainingQuestionTypesDto getSpecialTrainingQuestionTypes(String knowledgePointIds);

    /**
     * 根据题目id获取题型根据题目id获取题型
     * @param questionId 题目id
     */
    QuestionTypesDto getQuestionTypesByQuestionId(UUID questionId);

    MathExamVO getExamsByExamId(UUID examId);

    List<MathExamQuestionVO> getQuestionsByExamId(UUID examId);

    QuestionDetailDTO editQuestion(UUID examId, UUID questionId, AddExamQuestionParam param);

    QuestionDeleteDto deleteQuestions(UUID examId, UUID questionId);

    QuestionDetailDTO addQuestion(UUID examId, AddExamQuestionParam param);

    QuestionDetailDTO updateExamDetail(UUID examId);

    SpecializedTrainingCrateExamDto saveExam(SpecializedTrainingCreateExamParam param);

    SpecializedTrainingUpdateExamDto specializedTrainingUpdateExam(AISpecializedTrainingUpdateExamParam updateExamParam);


    /**
     * 试卷拆分
     * @param param
     * @return
     */
    ExamAnalyzeVo cutQuestions(AiAnalyticsExamParam param);

    CheckExamExsitenceDtoV2 checkExamExistenceV2(CheckExamExsitenceParam param);

    /**
     * 根据试卷id获取小节详细信息
     * @param examId 试卷id
     */
    TextBookCatalogueDto textbooksChaptersSections(UUID examId,PublisherEnum  publisher);

    /**
     * 根据小节列表获取知识点
     * @param sectionIds 小节id列表
     */
    KnowledgePointBySelectDto getKnowledgePointBySection(List<UUID> sectionIds);

    KnowledgePointQuestionTypesDtoV2New questionTypeV2(String knowledgePointIds);

    List<MathSectionDto> listMathSections(Integer grade, Integer semester, PublisherEnum publisher);

    List<MathChapterDto> listMathChapters();

    CreateQuestionByKnowledgeVoV2 selectSpecializedTrainingExamDetail(UUID examId);

    CreateTextBookQuestioVo selectSpecializedTextBookTrainingExamDetail(UUID examId);

    MathElecttronicTextbookVo getElectronicTextbook(QueryElectronicTextbookBo bo);

    List<QuerySectionVideoDto> querySectionVideos(List<UUID> sectionIdList);

    List<KnowledgePointDto> listKnowledgePoints(List<UUID> kpIds);

    void reanalyzeExamKnowledgePoint(AiAnalyticsExamParam examAnalyzeParam);
}
