package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.mapper.*;
import com.joinus.study.model.dto.ReadingActivityLastPracticeDto;
import com.joinus.study.model.dto.ReadingActivityWithStudentDTO;
import com.joinus.study.model.dto.ReadingSetsQuestionDto;
import com.joinus.study.model.dto.ReadingWeakKnowledgePointDto;
import com.joinus.study.model.entity.ReadingPersonalPassages;
import com.joinus.study.model.entity.ReadingPersonalPlan;
import com.joinus.study.model.entity.ReadingPersonalUser;
import com.joinus.study.model.enums.GenreEnum;
import com.joinus.study.model.enums.ReadingEntryTypeEnum;
import com.joinus.study.model.enums.ReadingQuestionTypeEnum;
import com.joinus.study.model.param.ReadingStudentPassagesQueryParam;
import com.joinus.study.model.vo.ReadingPassagesBackendVO;
import com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo;
import com.joinus.study.model.vo.ReadingPersonalPassagesVo;
import com.joinus.study.model.vo.ReadingWeekKnowledgePointVo;
import com.joinus.study.service.ReadingPersonalPassagesService;
import com.joinus.study.service.ReadingPersonalUserService;
import com.joinus.study.service.ReadingQuestionRecommendationService;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * @description: 语文阅读提分选题服务实现类
 * @author: lifengxu
 * @date: 2025/6/18 14:34
 */
@Slf4j
@Service
public class ReadingQuestionRecommendationServiceImpl implements ReadingQuestionRecommendationService {

    @Resource
    private ReadingPassagesMapper passagesMapper;
    @Resource
    private ReadingPersonalPassagesMapper personalPassagesMapper;
    @Resource
    private ReadingPersonalPassagesQuestionMapper personalPassagesQuestionMapper;
    @Resource
    private ReadingPersonalPassagesService personalPassagesService;
    @Resource
    private ReadingPersonalPlanMapper personalPlanMapper;
    @Resource
    private ReadingPassageQuestionSetsMapper passageQuestionSetsMapper;
    @Resource
    private ReadingActivityMapper activityMapper;
    @Resource(name = "taskExecutor")
    private Executor threadPoolTaskExecutor;
    @Resource
    private ReadingPersonalUserService personalUserService;

    private static final Random random = new Random();

    @Value("${qyl.host.url}")
    private String qylHostUrl;
    @Value("${get.student.isOpen.url}")
    private String getStudentIsOpenUrl;

    /**
     * @Description 【定向爆破v1】选题策略（废弃）
     * 第一题：选择知识点对应已做错题
     * 第二、三题（过滤做过的题目）：
     * 策略1：错误率高（高于80%）的知识点题目（优先）
     * 策略2：所有薄弱知识点题目（次优先）
     * 策略3：同知识点不同题型题目（保底）
     * <AUTHOR>
     * @date 2025/5/8
     */
    @Override
    public ReadingPersonalPassagesVo getDirectionalBlastingQuestions(ReadingStudentPassagesQueryParam param) throws BaseException {
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getEntryType(), "答题入口类型不能为空");
        CommonResponse.ERROR.assertNotNull(param.getKnowledgePointId(), "知识点id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getEntryType() == ReadingEntryTypeEnum.DIRECTIONAL_BLASTING.getCode(), "entryType错误");
        UUID weekKnowledgePointId = param.getKnowledgePointId();

        // 初始化结果集（3道题）
        ReadingPersonalPassagesVo result = new ReadingPersonalPassagesVo();
        List<ReadingPersonalPassageQuestionVo> questions = new ArrayList<>(3);

        // 【第一题】获取当前知识点错题
        List<ReadingPersonalPassageQuestionVo> currentKnowledgeErrorQuestions = personalPassagesQuestionMapper.getCurrentKnowledgeErrorQuestions(param);
        log.info("【定向爆破】获取当前知识点错题：{}", JSONUtil.toJsonStr(currentKnowledgeErrorQuestions));
        if (CollUtil.isEmpty(currentKnowledgeErrorQuestions)) {
            return null;
        }

        for (ReadingPersonalPassageQuestionVo question : currentKnowledgeErrorQuestions) {
            // 文章信息处理
            ReadingPassagesBackendVO passagesBackendVO = passagesMapper.query(question.getPassageId());
            if (passagesBackendVO != null && passagesBackendVO.getDeletedAt() == null
                    && passagesBackendVO.getIsEnabled() == 1 && passagesBackendVO.getIsAudit() == 1) {
                result.setPassageId(question.getPassageId());
                result.setTitle(passagesBackendVO.getTitle());
                result.setGenre(passagesBackendVO.getGenre());
                result.setUnitId(passagesBackendVO.getUnitId());
                result.setContent(passagesBackendVO.getContent());
                result.setGenreName(GenreEnum.getByName(passagesBackendVO.getGenre()));

                // 将题添加到结果集中
                param.setPassageId(question.getPassageId());
                question.setQuestionType(ReadingQuestionTypeEnum.getByName(question.getQuestionType()));
                questions.add(question);
                break;
            }
        }
        log.info("【定向爆破】当前错题{}", JSONUtil.toJsonStr(questions));
        if (CollUtil.isEmpty(questions)) {
            return null;
        }

        // 获取学生薄弱知识点集合
        List<ReadingWeekKnowledgePointVo> studentWeekKnowledgePointList = personalPassagesMapper.queryStudentWeekKnowledgePointList(param.getStudentId(), null);
        log.info("【定向爆破】获取学生薄弱知识点集合：{}", JSONUtil.toJsonStr(studentWeekKnowledgePointList));
        if (CollUtil.isEmpty(studentWeekKnowledgePointList)) {
            return null;
        }

        // 已经获取题目的知识点集合
        Set<UUID> questionExistKnowledgeIds = new HashSet<>();
        questionExistKnowledgeIds.add(weekKnowledgePointId);
        // 已经获取题目的ID集合
        Set<UUID> questionExistQuestionIds = questions.stream().map(ReadingPersonalPassageQuestionVo::getQuestionId).collect(Collectors.toSet());
        // 已经获取题目的类型集合
        Set<String> questionExistQuestionTypes = questions.stream().map(ReadingPersonalPassageQuestionVo::getQuestionType).collect(Collectors.toSet());

        // 已做题目ID集合
        List<UUID> doQuestionIds = personalPassagesQuestionMapper.getStudentDoQuestionIds(param.getStudentId(), ReadingEntryTypeEnum.DIRECTIONAL_BLASTING.getCode());
        log.info("【定向爆破】已做题目ID集合：{}", JSONUtil.toJsonStr(doQuestionIds));
        if (CollUtil.isNotEmpty(doQuestionIds)) {
            questionExistQuestionIds.addAll(doQuestionIds);
        }

        // 【第二、三题】循环执行策略
        int target = 3;
        // 前两种策略此参数需为null
        param.setKnowledgePointId(null);
        // 策略1：错误率高的知识点题目（优先处理高频错误）
        BigDecimal minErrorRate = new BigDecimal("0.8");
        List<UUID> highErrorRateList = studentWeekKnowledgePointList.stream()
                .filter(item -> item.getErrorRate().compareTo(minErrorRate) >= 0)
                .map(ReadingWeekKnowledgePointVo::getId)
                .collect(Collectors.toList());
        tryAddQuestion(questions, param, highErrorRateList, questionExistKnowledgeIds, questionExistQuestionIds, null, target - questions.size());
        if (questions.size() < target) {
            // 策略2：所有薄弱知识点题目（次优先）
            List<UUID> allWeakList = studentWeekKnowledgePointList.stream()
                    .map(ReadingWeekKnowledgePointVo::getId)
                    .collect(Collectors.toList());
            tryAddQuestion(questions, param, allWeakList, questionExistKnowledgeIds, questionExistQuestionIds, null, target - questions.size());
            if (questions.size() < target) {
                // 策略3：同知识点不同题型题目（最后保底）
                List<UUID> sameList = List.of(weekKnowledgePointId);
                tryAddQuestion(questions, param, sameList, null, questionExistQuestionIds, questionExistQuestionTypes, target - questions.size());
            }
        }

        // 不足3道题时
        log.info("【定向爆破】已找到题目：{}", JSONUtil.toJsonStr(questions));
        if (questions.size() < target) {
            return null;
        }

        // 获取题目ID集合
        List<UUID> questionIds = questions.stream().map(ReadingPersonalPassageQuestionVo::getQuestionId).collect(Collectors.toList());
        // 获取题目信息
        List<ReadingPersonalPassageQuestionVo> questionsByParam = passageQuestionSetsMapper.getQuestionsByParam(null, questionIds);
        log.info("【定向爆破】获取题目信息：{}", JSONUtil.toJsonStr(questionsByParam));
        // 题目信息处理
        if (CollUtil.isEmpty(questionsByParam) || questionsByParam.size() < target) {
            return null;
        }
        // questions按questionIds排序
        Map<UUID, ReadingPersonalPassageQuestionVo> questionMap = questionsByParam.stream().collect(Collectors.toMap(ReadingPersonalPassageQuestionVo::getQuestionId, item -> item));
        questions = questionIds.stream().map(questionMap::get).collect(Collectors.toList());

        // 处理题目信息
        questions.forEach(item -> {
            item.setQuestionType(ReadingQuestionTypeEnum.getByName(item.getQuestionType()));
            if (StrUtil.isNotBlank(item.getOptionStr())) {
                item.setOptions(JSONUtil.parseArray(item.getOptionStr()));
            }
        });

        // 添加题目到结果集中
        result.setQuestions(questions);
        // 题目信息处理
        if (CollUtil.isEmpty(questions) || questions.size() < target) {
            return null;
        }

        // 保存练习记录
        result.setStudentId(param.getStudentId());
        personalPassagesService.saveOrUpdatePersonalPassage(result, param.getEntryType());
        return result;
    }

    /**
     * @Description 【定向爆破v1】执行选题策略
     * <AUTHOR>
     * @date 2025/5/8
     */
    private void tryAddQuestion(List<ReadingPersonalPassageQuestionVo> questions,
                                ReadingStudentPassagesQueryParam param, List<UUID> knowledgeIds,
                                Set<UUID> existKnowledgeIds, Set<UUID> existQuestionIds,
                                Set<String> existQuestionTypes, Integer count) throws BaseException {
        if (CollUtil.isNotEmpty(knowledgeIds)) {
            if (CollUtil.isNotEmpty(existKnowledgeIds)) {
                // 过滤掉已经获取过的知识点
                knowledgeIds = knowledgeIds.stream().filter(item -> !existKnowledgeIds.contains(item)).collect(Collectors.toList());
            }
            param.setWeakKnowledgePointIds(knowledgeIds);
        }

        // 执行查询
        List<ReadingPersonalPassageQuestionVo> result = personalPassagesQuestionMapper.getQuestionsByKnowledgePoint(param, existQuestionIds, existQuestionTypes, count);

        // 处理查询结果
        if (CollUtil.isNotEmpty(result)) {
            result.forEach(question -> {
                if (CollUtil.isNotEmpty(question.getKnowledgePointIds())) {
                    existKnowledgeIds.addAll(question.getKnowledgePointIds());
                }
                if (CollUtil.isNotEmpty(existQuestionIds)) {
                    existQuestionIds.add(question.getQuestionId());
                }
                if (CollUtil.isNotEmpty(existQuestionTypes)) {
                    existQuestionTypes.add(question.getQuestionType());
                }
                questions.add(question);
            });
        }
    }

    /**
     * @description: 【定向爆破v3】选题策略
     * 第一、第二、三题（不过滤已做过题）：按年级、学期、薄弱知识点、同篇文章下随机获取三道题（不足能取几道取几道）
     * @author: lifengxu
     * @date: 2025/6/17 16:26
     */
    @Override
    public ReadingPersonalPassagesVo getDirectionalBlastingQuestionsTwo(ReadingStudentPassagesQueryParam param) throws BaseException {
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getEntryType(), "答题入口类型不能为空");
        CommonResponse.ERROR.assertNotNull(param.getKnowledgePointId(), "知识点id不能为空");
        CommonResponse.ERROR.assertNotNull(param.getEntryType() == ReadingEntryTypeEnum.DIRECTIONAL_BLASTING.getCode(), "答题入口类型错误");

        UUID weekKnowledgePointId = param.getKnowledgePointId();
         //增加不是会员校验
        /*if (param.getEntryType() == ReadingEntryTypeEnum.DIRECTIONAL_BLASTING.getCode()){
            Map<String, Object> mapParams =new HashMap<>();
            mapParams.put("studentId", param.getStudentId());
            mapParams.put("subjectType", 5);
            mapParams.put("parentId", param.getParentId());
            log.info("调用青于蓝获取学生开通会员信息接口参数：{}", mapParams);
            String response = LocalHttpUtil.get(qylHostUrl + getStudentIsOpenUrl, mapParams);
            log.info("调用青于蓝获取学生开通会员信息接口返回结果：{}", response);
            if (response != null&& response.contains("\"code\":\"200\"")){
                JSONObject jsonObject = JSONUtil.parseObj(response);
                if (ObjectUtil.isNotEmpty(jsonObject.get("data"))){
                    JSONObject data = JSONUtil.parseObj(jsonObject.get("data"));
                    String isOpen = data.get("isOpen").toString();
                    CommonResponse.ERROR.assertIsTrue("1".equals(isOpen), "学生未开通会员");
                }
            }
        }*/
        ReadingPersonalUser personalUser = personalUserService.getByStudentId(param.getStudentId());
        CommonResponse.ERROR.assertNotNull(personalUser, "学生练习信息不存在");
        param.setGrade(personalUser.getGrade());
        param.setSemester(personalUser.getSemester());

        // 初始化结果集（3道题）
        int target = 3;
        ReadingPersonalPassagesVo result = new ReadingPersonalPassagesVo();
        List<ReadingPersonalPassageQuestionVo> questions = new ArrayList<>(3);

        // 选择薄弱知识点
        param.setWeakKnowledgePointIds(Collections.singletonList(param.getKnowledgePointId()));

        // 已经获取题目的ID集合
        List<UUID> doQuestionIds = personalPassagesQuestionMapper.getStudentDoQuestionIds(param.getStudentId(), ReadingEntryTypeEnum.DIRECTIONAL_BLASTING.getCode());
        log.info("[定向爆破3]已做题目ID集合：{}", JSONUtil.toJsonStr(doQuestionIds));
        if (CollUtil.isNotEmpty(doQuestionIds)) {
            param.setDoQuestionIds(doQuestionIds);
        }

        // 筛选题目和文章
        List<ReadingPersonalPassageQuestionVo> questionVoList = personalPassagesQuestionMapper.getDirectionalBlastingQuestionIds(param, null);
        log.info("[定向爆破3]获取题目及文章信息（过滤已做过题）：{}", CollUtil.isEmpty(questionVoList) ? 0 : questionVoList.size());
        if (CollUtil.isEmpty(questionVoList)) {
            param.setDoQuestionIds(null);
            questionVoList = personalPassagesQuestionMapper.getDirectionalBlastingQuestionIds(param, null);
            log.info("[定向爆破3]获取题目及文章信息（不过滤已做过题）：{}", CollUtil.isEmpty(questionVoList) ? 0 : questionVoList.size());
        }
        if (CollUtil.isEmpty(questionVoList)) {
            return null;
        }

        // 根据文章ID分组，按出现次数排序
        Map<UUID, Long> questionMap = questionVoList.stream()
                .collect(Collectors.groupingBy(ReadingPersonalPassageQuestionVo::getPassageId, Collectors.counting()));
        // 获取题目数量大于等于3的文章ID
        List<UUID> passageIds = questionMap.entrySet().stream()
                .filter(entry -> entry.getValue() != null && entry.getValue() >= 3L)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(passageIds)) {
            passageIds = questionMap.entrySet().stream()
                   .filter(entry -> entry.getValue()!= null && entry.getValue() >= 2L)
                   .map(Map.Entry::getKey)
                   .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(passageIds)) {
            passageIds = questionMap.entrySet().stream()
                  .filter(entry -> entry.getValue()!= null && entry.getValue() >= 1L)
                  .map(Map.Entry::getKey)
                  .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(passageIds)) {
            return null;
        }
        // 随机获取文章ID
        UUID passageId = passageIds.get(random.nextInt(passageIds.size()));
        param.setPassageId(passageId);
        // 获取出现次数最多的文章ID,对应的题目ID
        List<ReadingPersonalPassageQuestionVo> passageQuestionVos = questionVoList.stream()
                .filter(questionVo -> questionVo.getPassageId().equals(passageId))
                .collect(Collectors.toList());
        // 从passageQuestionVos中随机选择三道题,随机打乱原列表
        Collections.shuffle(passageQuestionVos);
        // 防止元素不足时抛出异常
        if (target > passageQuestionVos.size()) {
            target = passageQuestionVos.size();
        }
        // 截取前target个元素
        questions = passageQuestionVos.subList(0, target);
        if (questions.isEmpty()) {
            return null;
        }

        // 文章信息处理
        ReadingPassagesBackendVO passagesBackendVO = passagesMapper.query(param.getPassageId());
        if (ObjectUtil.isNotEmpty(passagesBackendVO)) {
            result.setPlanId(param.getPlanId());
            result.setPassageId(param.getPassageId());
            result.setTitle(passagesBackendVO.getTitle());
            result.setGenre(passagesBackendVO.getGenre());
            result.setUnitId(passagesBackendVO.getUnitId());
            result.setContent(passagesBackendVO.getContent());
            result.setGenreName(GenreEnum.getByName(passagesBackendVO.getGenre()));
        }

        // 获取题目ID集合
        List<UUID> questionIds = questions.stream().map(ReadingPersonalPassageQuestionVo::getQuestionId).collect(Collectors.toList());
        // 获取题目信息
        List<ReadingPersonalPassageQuestionVo> questionsByParam = passageQuestionSetsMapper.getQuestionsByParam(null, questionIds);
        // 题目类型处理
        processByQuestionType(questionsByParam);
        result.setQuestions(questionsByParam);

        // 保存练习记录
        result.setStudentId(param.getStudentId());
        personalPassagesService.saveOrUpdatePersonalPassage(result, param.getEntryType());
        return result;
    }

    /**
     * @Description 【阅读训练营或练习计划】获取练习题目
     * <AUTHOR>
     * @date 2025/5/13
     */
    @Override
    public ReadingPersonalPassagesVo getPracticeQuestions(ReadingStudentPassagesQueryParam param) throws BaseException {
        CommonResponse.ERROR.assertNotNull(param.getEntryType(), "答题入口类型不能为空");
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生ID不能为空");
        if (param.getEntryType() == ReadingEntryTypeEnum.READING_TRAINING.getCode()) {
            return this.getReadingTrainingQuestions(param);
        } else if (param.getEntryType() == ReadingEntryTypeEnum.EXERCISE_PLAN.getCode()) {
            return this.getExercisePlanQuestions(param);
        } else {
            throw new BaseException("答题入口类型错误");
        }
    }

    /**
     * @Description 【阅读训练】选题
     * 套题（过滤已做过的题）：根据年级、学期、薄弱知识点、随机获取一套题
     * <AUTHOR>
     * @date 2025/5/12
     */
    public ReadingPersonalPassagesVo getReadingTrainingQuestions(ReadingStudentPassagesQueryParam param) throws BaseException {
        CommonResponse.ERROR.assertNotNull(param.getUnitId(), "请选择单元");

        // 获取学生薄弱知识点
        List<UUID> weakKnowledgePointIds = personalPassagesService.getStudentWeakKnowledgePointIds(param.getStudentId());
        // 获取学生练习计划
        ReadingPersonalPlan plan;
        if (ObjectUtil.isNotEmpty(param.getPlanId())) {
            plan = personalPlanMapper.selectById(param.getPlanId());
        } else {
            plan = personalPlanMapper.getPlanInfoByStudentId(param.getStudentId());
        }
        // 根据计划处理
        if (ObjectUtil.isNotEmpty(plan)) {
            if (ObjectUtil.isNotEmpty(plan.getWeakKnowledgePointIds())) {
                // 练习计划选择薄弱知识点，合并到学生薄弱知识点
                weakKnowledgePointIds.addAll(convertStringToUUIDList(plan.getWeakKnowledgePointIds()));
            } else {
                // 练习计划未选择薄弱知识点，此次出题归属该计划
                param.setPlanId(plan.getId());
            }
        }

        // 薄弱点去重
        weakKnowledgePointIds = weakKnowledgePointIds.stream().distinct().collect(Collectors.toList());
        param.setWeakKnowledgePointIds(weakKnowledgePointIds);

        // 根据条件参数，获取套题ID
        param.setDoSetsIds(personalPassagesQuestionMapper.getStudentDoSetsIds(param.getStudentId(), ReadingEntryTypeEnum.READING_TRAINING.getCode()));
        List<UUID> setsIds = passageQuestionSetsMapper.getReadingTrainingSetsIds(param);
        if (ObjectUtil.isEmpty(setsIds)) {
            param.setDoSetsIds(null);
            setsIds  = passageQuestionSetsMapper.getReadingTrainingSetsIds(param);
        }
        if (CollUtil.isEmpty(setsIds)) {
            return null;
        }

        // 处理结果集
        UUID setId = setsIds.get(random.nextInt(setsIds.size()));
        ReadingPersonalPassagesVo result = buildResult(setId, param);
        if (result == null) {
            return null;
        }

        // 保存练习记录
        result.setStudentId(param.getStudentId());
        personalPassagesService.saveOrUpdatePersonalPassage(result, param.getEntryType());
        return result;
    }

    /**
     * @Description 【阅读训练】String转UUIDList
     * <AUTHOR>
     * @date 2025/5/12
     */
    private List<UUID> convertStringToUUIDList(String input) throws BaseException {
        if (StringUtils.isBlank(input)) {
            return Collections.emptyList();
        }
        return Arrays.stream(input.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(UUID::fromString)
                .collect(Collectors.toList());
    }

    /**
     * @Description 【练习计划】选题
     * 套题（过滤已做过的题）：
     * 根据计划中包含的年级、学期、文体、知识点,获取套题ID及题目ID
     * 选择套题ID出现次数最多的套题ID
     * <AUTHOR>
     * @date 2025/5/12
     */
    public ReadingPersonalPassagesVo getExercisePlanQuestions(ReadingStudentPassagesQueryParam param) throws BaseException {
        CommonResponse.ERROR.assertNotNull(param.getPlanId(), "请选择计划");
        ReadingPersonalPlan plan = personalPlanMapper.selectById(param.getPlanId());
        CommonResponse.ERROR.assertNotNull(plan, "计划不存在");

        List<UUID> doQuestionIds = personalPassagesQuestionMapper.getStudentDoQuestionIds(param.getStudentId(), ReadingEntryTypeEnum.EXERCISE_PLAN.getCode());
        List<ReadingSetsQuestionDto> setsQuestionIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(plan.getWeakKnowledgePoint())) {
            // 等待所有任务完成再返回结果
            CompletableFuture.allOf(
                    plan.getWeakKnowledgePoint().getPointsDtoList().stream()
                            .map(question -> CompletableFuture.runAsync(() -> {
                                                ReadingStudentPassagesQueryParam planParam = getReadingStudentPassagesQueryParam(param, question);
                                                planParam.setDoQuestionIds(doQuestionIds);
                                                setsQuestionIds.addAll(passageQuestionSetsMapper.getExercisePlanSetsIds(planParam));
                                            },
                                            threadPoolTaskExecutor)
                            )
                            .toArray(CompletableFuture[]::new)
            ).join();
            if (CollUtil.isEmpty(setsQuestionIds)) {
                return null;
            }

            // 筛选出setsQuestionIds中题目ID出现次数最多的套题ID
            Map<UUID, Long> countMap = setsQuestionIds.stream()
                    .collect(Collectors.groupingBy(ReadingSetsQuestionDto::getSetsId, Collectors.counting()));

            // 处理结果集
            UUID setId = countMap.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(null);
            ReadingPersonalPassagesVo result = buildResult(setId, param);
            if (result == null) {
                return null;
            }

            // 保存练习记录
            result.setStudentId(param.getStudentId());
            personalPassagesService.saveOrUpdatePersonalPassage(result, param.getEntryType());
            return result;
        } else {
            // 练习计划未选择薄弱知识点，默认选择学生薄弱知识点
            CommonResponse.ERROR.assertNotNull(param.getUnitId(), "请选择单元");
            return getReadingTrainingQuestions(param);
        }
    }

    /**
     * @Description 【练习计划】构建查询参数，获取套题ID及题目ID
     * <AUTHOR>
     * @date 2025/5/12
     */
    @NotNull
    private static ReadingStudentPassagesQueryParam getReadingStudentPassagesQueryParam(ReadingStudentPassagesQueryParam param, ReadingWeakKnowledgePointDto item) throws BaseException {
        ReadingStudentPassagesQueryParam planParam = new ReadingStudentPassagesQueryParam();
        planParam.setStudentId(param.getStudentId());
        planParam.setGrade(item.getGrade());
        planParam.setSemester(item.getSemester());
        planParam.setGenre(item.getGenre());
        planParam.setWeakKnowledgePointIds(item.getWeakKnowledgePointIds());
        return planParam;
    }

    /**
     * @description: 【暑期训练营】获取假期练习题目
     * @author: lifengxu
     * @date: 2025/5/28
     */
    @Override
    public ReadingPersonalPassagesVo getActivityPracticeQuestions(ReadingStudentPassagesQueryParam param) throws BaseException {
        // 校验当前时间如果是周日则返回null
        LocalDateTime currentDateTime = LocalDateTime.now();
        if (currentDateTime.getDayOfWeek().getValue() == 7) {
            throw new BaseException("今日休息");
        }

        // 答题入口类型
        int entryType;
        if (ObjectUtil.isNotEmpty(param.getPersonalPassageId())) {
            // 换题
            ReadingPersonalPassages personalPassages = personalPassagesMapper.selectById(param.getPersonalPassageId());
            CommonResponse.ERROR.assertNotNull(personalPassages, "未查询到练习记录");
            CommonResponse.ERROR.assertNotNull(personalPassages.getStatus() < 2, "已提交不可更换题目");

            entryType = personalPassages.getEntryType();
            param.setStudentId(personalPassages.getStudentId());
            param.setPlanId(personalPassages.getPlanId());
        } else {
            CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生ID不能为空");
            CommonResponse.ERROR.assertNotNull(param.getPlanId(), "假期计划ID不能为空");
            // 校验当天是否有未完成练习记录
            Long personalPassageId = personalPassagesMapper.getPendingPersonalPassageIds(param, LocalDate.now());
            if (ObjectUtil.isNotEmpty(personalPassageId)) {
                // 存在未完成练习记录，返回该记录
                return personalPassagesService.getpersonalPassagesById(personalPassageId);
            }

            // 获取学生今日做题类型
            entryType = personalPassagesService.getActivityEntryType(param.getPlanId(), param.getStudentId());
            log.info("getActivityPracticeQuestions,studentId:{},planId:{},entryType:{}", param.getStudentId(), param.getPlanId(), entryType);
        }

        // 根据ID获取假期练习计划
        ReadingActivityWithStudentDTO activity = activityMapper.getActivityWithStudent(param.getPlanId(), param.getStudentId());
        CommonResponse.ERROR.assertNotNull(activity, "活动不存在");
        // 校验当前时间是否在假期训练时间段内
        CommonResponse.ERROR.assertNotNull(currentDateTime.isBefore(activity.getActivityBeginTime()), "活动未开始");
        CommonResponse.ERROR.assertNotNull(currentDateTime.isAfter(activity.getActivityEndTime()), "活动已结束");
        CommonResponse.ERROR.assertNotNull(activity.getGrade(), "年级不能为空");
        CommonResponse.ERROR.assertNotNull(activity.getSemester(), "学段不能为空");
        CommonResponse.ERROR.assertNotNull(activity.getDailyPassageNum(), "目标题数不能为空");
        CommonResponse.ERROR.assertNotNull(entryType, "训练类型不能为空");
        param.setEntryType(entryType);
        param.setGrade(activity.getGrade());
        param.setSemester(activity.getSemester());
        param.setDailyPassageNumber(activity.getDailyPassageNum());

        if (entryType == ReadingEntryTypeEnum.HOLIDAY_INTENSIVE_TRAINING.getCode()) {
            // 假期训练
            return this.activityIntensivePractice(param);
        } else if (entryType == ReadingEntryTypeEnum.HOLIDAY_CONSOLIDATION_EXERCISES.getCode()) {
            // 巩固练习
            return this.activityConsolidationExercises(param);
        } else {
            throw new BaseException("答题入口类型错误");
        }
    }

    /**
     * @description: 【暑期训练营-强化训练】选题
     * 套题（过滤已做过的套题）：
     * 1.每天两套套题
     * 2.单元内选择一套套题
     * 换题策略不变，更新练习记录
     * @author: lifengxu
     * @date: 2025/5/29
     */
    private ReadingPersonalPassagesVo activityIntensivePractice(ReadingStudentPassagesQueryParam param) throws BaseException {
        // 校验学生当天是否已经完成强化训练
        List<UUID> currentDayDoSetsIds = personalPassagesQuestionMapper.getActivityStudentDoSetsIds(param, param.getPersonalPassageId(), LocalDate.now(), 2);
        if (ObjectUtil.isNotEmpty(currentDayDoSetsIds) && currentDayDoSetsIds.size() >= param.getDailyPassageNumber()) {
            throw new BaseException("您已完成今日目标");
        }

        // 获取待出题单元ID
        UUID pendingUnitId = getPendingUnitId(param);
        if (ObjectUtil.isEmpty(pendingUnitId)) {
            return null;
        }
        param.setUnitId(pendingUnitId);

        // 获取学生假期训练已练习套题ID
        List<UUID> doSetsIds = personalPassagesQuestionMapper.getActivityStudentDoSetsIds(param, null, null, null);
        if (ObjectUtil.isNotEmpty(doSetsIds)) {
            // 过滤掉已经获取过的套题ID
            param.setDoSetsIds(doSetsIds);
        }

        // 获取套题ID
        List<UUID> setsIds = passageQuestionSetsMapper.getHolidayIntensiveTrainingSetsIds(param);
        if (ObjectUtil.isEmpty(setsIds)) {
            // 不过滤已做过的套题ID
            param.setDoSetsIds(doSetsIds);
            setsIds = passageQuestionSetsMapper.getHolidayIntensiveTrainingSetsIds(param);
        }
        if (ObjectUtil.isEmpty(setsIds)) {
            return null;
        }

        // 处理结果集
        UUID setId = setsIds.get(random.nextInt(setsIds.size()));
        ReadingPersonalPassagesVo result = buildResult(setId, param);
        if (result == null) {
            return null;
        }
        // 保存或更新练习记录
        result.setStudentId(param.getStudentId());
        if (ObjectUtil.isNotEmpty(param.getPersonalPassageId())) {
            result.setId(param.getPersonalPassageId());
        }
        personalPassagesService.saveOrUpdatePersonalPassage(result, param.getEntryType());
        return result;
    }

    /**
     * @description: 【暑期训练营-强化训练】获取待出题单元ID
     * @author: lifengxu
     * @date: 2025/6/5
     */
    private UUID getPendingUnitId(ReadingStudentPassagesQueryParam param) throws BaseException {
        // 根据练习记录，筛选出要已完成的单元（答完5道题的单元）
        List<UUID> completedUnitIds = personalPassagesQuestionMapper.getCompletedUnitIds(param, 5);
        param.setUnitIds(completedUnitIds);
        // 根据年级+学期 筛选出待出题单元
        UUID pendingUnitId = personalPassagesQuestionMapper.getActivityPracticePendingUnitIds(param);
        // 待出题单元为null
        if (ObjectUtil.isEmpty(pendingUnitId)) {
            // 从已完成单元中随机抽取套题
            if (ObjectUtil.isEmpty(completedUnitIds)) {
                return null;
            } else {
                pendingUnitId = completedUnitIds.get(random.nextInt(completedUnitIds.size()));
            }
        }
        return pendingUnitId;
    }

    /**
     * @description: 【暑期训练营-巩固练习】选题
     * 三道题（过滤已做过的题）：
     * 1.获取上次练习产生的薄弱知识点
     * 2.一致则使用所有薄弱知识点（取适配度最高的文章及题目）
     * 3.不一致，则根据错误率排序选择前三个知识点
     * 4.根据年级、学期、薄弱知识点、选择频次最高的套题ID
     * 5.优先选取符合薄弱知识点的题目
     * 6.如果薄弱知识点题目不足3道，则补充套题中其他题目
     * @author: lifengxu
     * @date: 2025/5/29
     */
    private ReadingPersonalPassagesVo activityConsolidationExercises(ReadingStudentPassagesQueryParam param) throws BaseException {
        // 获取当日活动巩固练习记录IDs
        List<Long> todayPersonalPassageIds = personalPassagesQuestionMapper.getActivityStudentDoPersonalPassageIds(param, LocalDate.now(),2);
        if (ObjectUtil.isNotEmpty(todayPersonalPassageIds) && todayPersonalPassageIds.size() >= param.getDailyPassageNumber()) {
            throw new BaseException("您已完成今日目标");
        }

        // 获取学生假期训练已完成的练习题目ID
        List<UUID> doQuestionIds = personalPassagesQuestionMapper.getActivityStudentDoQuestionIds(param);
        param.setDoQuestionIds(doQuestionIds);

        // 获取上次练习产生的薄弱知识点
        List<UUID> weekKnowledgePointList = null;
        // 获取上次练习记录信息
        ReadingActivityLastPracticeDto activityPractice = personalPassagesService.getActivityLastPractice(param.getPlanId(), param.getStudentId());
        log.info("activityConsolidationExercises,getActivityPractice:{}", JSONUtil.toJsonStr(activityPractice));
        if (ObjectUtil.isNotEmpty(activityPractice)) {
            // 上次练习产生的薄弱知识点
            List<ReadingWeekKnowledgePointVo> practiceWeekKnowledgePoints = activityPractice.getWeekKnowledgePoints();
            log.info("activityConsolidationExercises,practiceWeekKnowledgePoints:{}", JSONUtil.toJsonStr(practiceWeekKnowledgePoints));

            if (ObjectUtil.isEmpty(practiceWeekKnowledgePoints)) {
                return null;
            } else {
                // 判断薄弱知识点错误率是否一致
                if (isErrorRateConsistent(practiceWeekKnowledgePoints)) {
                    // 一致则使用所有薄弱知识点（取适配度最高的文章及题目）
                    weekKnowledgePointList = practiceWeekKnowledgePoints.stream().map(ReadingWeekKnowledgePointVo::getId).collect(Collectors.toList());
                } else {
                    // 不一致，则根据错误率排序选择前三个知识点
                    weekKnowledgePointList = practiceWeekKnowledgePoints.stream()
                            .sorted(Comparator.comparing(ReadingWeekKnowledgePointVo::getErrorRate))
                            .limit(3)
                            .map(ReadingWeekKnowledgePointVo::getId).collect(Collectors.toList());
                }
            }
        }
        log.info("activityConsolidationExercises,weekKnowledgePointList.size:{}", weekKnowledgePointList.size());
        if (ObjectUtil.isEmpty(weekKnowledgePointList)) {
            return null;
        }
        param.setWeakKnowledgePointIds(weekKnowledgePointList);

        // 初始化结果集（3道题）
        int target = 3;
        ReadingPersonalPassagesVo result = new ReadingPersonalPassagesVo();
        List<ReadingPersonalPassageQuestionVo> questions = new ArrayList<>(3);
        // 已经获取题目的ID集合
        Set<UUID> questionExistQuestionIds = new HashSet<>();

        // 构建查询参数，获取套题及题目ID
        ReadingStudentPassagesQueryParam planParam = new ReadingStudentPassagesQueryParam();
        planParam.setGrade(param.getGrade());
        planParam.setDoQuestionIds(doQuestionIds);
        planParam.setSemester(param.getSemester());
        planParam.setStudentId(param.getStudentId());
        planParam.setWeakKnowledgePointIds(weekKnowledgePointList);
        List<ReadingSetsQuestionDto> setsQuestionList = passageQuestionSetsMapper.getExercisePlanSetsIds(planParam);
        if (ObjectUtil.isEmpty(setsQuestionList)) {
            return null;
        }

        // 处理setsQuestionList，筛选出出现次数最多的套题ID及对应的题目ID集合
        Map<UUID, Long> countMap = setsQuestionList.stream().collect(Collectors.groupingBy(ReadingSetsQuestionDto::getSetsId, Collectors.counting()));
        // 筛选出出现次数最多的套题ID
        UUID setsId = countMap.entrySet().stream().max(Map.Entry.comparingByValue()).map(Map.Entry::getKey).orElse(null);
        // 筛选出套题ID对应的题目ID集合
        Set<UUID> questionIds = setsQuestionList.stream().filter(item -> item.getSetsId().equals(setsId)).map(ReadingSetsQuestionDto::getQuestionId).collect(Collectors.toSet());
        // 获取套题ID包含的所有题目
        List<ReadingPersonalPassageQuestionVo> setsQuestionsList = passageQuestionSetsMapper.getQuestionsByParam(setsId, null);
        if (ObjectUtil.isNotEmpty(setsQuestionsList)) {
            // 遍历套题题目集合，获取高频次题目信息
            for (ReadingPersonalPassageQuestionVo question : setsQuestionsList) {
                if (questions.size() < target) {
                    if (questionIds.contains(question.getQuestionId())) {
                        questions.add(question);
                        questionExistQuestionIds.add(question.getQuestionId());
                    }
                } else {
                    break;
                }
            }

            // 如果题目数量不足3道题，则补充题目
            if (questions.size() < target) {
                // 套题中获取其他题目
                List<ReadingPersonalPassageQuestionVo> otherSetsQuestionsList = setsQuestionsList.stream().filter(
                                item -> !questionIds.contains(item.getQuestionId()))
                        .collect(Collectors.toList());

                if (ObjectUtil.isNotEmpty(otherSetsQuestionsList)) {
                    for (ReadingPersonalPassageQuestionVo question : otherSetsQuestionsList) {
                        if (questions.size() < target) {
                            if (!questionExistQuestionIds.contains(question.getQuestionId())) {
                                questions.add(question);
                                questionExistQuestionIds.add(question.getQuestionId());
                            }
                        } else {
                            break;
                        }
                    }
                }
            }
        }
        if (questions.size() < target) {
            return null;
        }

        // 文章信息处理
        UUID passageId = questions.get(0).getPassageId();
        ReadingPassagesBackendVO passagesBackendVO = passagesMapper.query(passageId);
        if (ObjectUtil.isNotEmpty(passagesBackendVO)) {
            result.setPlanId(param.getPlanId());
            result.setPassageId(passageId);
            result.setTitle(passagesBackendVO.getTitle());
            result.setGenre(passagesBackendVO.getGenre());
            result.setUnitId(passagesBackendVO.getUnitId());
            result.setContent(passagesBackendVO.getContent());
            result.setGenreName(GenreEnum.getByName(passagesBackendVO.getGenre()));
        }

        // 题目类型处理
        processByQuestionType(questions);
        result.setQuestions(questions);

        // 保存练习记录
        result.setStudentId(param.getStudentId());
        if (ObjectUtil.isNotEmpty(param.getPersonalPassageId())) {
            result.setId(param.getPersonalPassageId());
        }
        personalPassagesService.saveOrUpdatePersonalPassage(result, param.getEntryType());
        return result;
    }

    /**
     * @param knowledgePointVos 包含 ReadingWeekKnowledgePointVo 对象的列表
     * @return 如果所有 errorRate 一致返回 true，否则返回 false
     * 【暑期训练营-巩固练习】判断薄弱知识点错误率是否一致
     * 判断列表中所有 ReadingWeekKnowledgePointVo 对象的 errorRate 是否一致
     */
    private boolean isErrorRateConsistent(List<ReadingWeekKnowledgePointVo> knowledgePointVos) throws BaseException {
        if (knowledgePointVos == null || knowledgePointVos.isEmpty()) {
            return true;
        }

        // 获取第一个元素的 errorRate 作为基准
        BigDecimal firstErrorRate = knowledgePointVos.get(0).getErrorRate();
        // 使用 Stream API 跳过第一个元素并检查所有元素的 errorRate 是否与基准相等
        return knowledgePointVos.stream()
                .skip(1)
                .allMatch(vo -> {
                    BigDecimal currentErrorRate = vo.getErrorRate();
                    return firstErrorRate == null ? currentErrorRate == null : firstErrorRate.compareTo(currentErrorRate) == 0;
                });
    }

    /**
     * @Description 【公共】构建结果集
     * <AUTHOR>
     * @date 2025/5/12
     */
    private ReadingPersonalPassagesVo buildResult(UUID setId, ReadingStudentPassagesQueryParam param) throws BaseException {
        // 根据套题ID, 获取文章信息
        ReadingPersonalPassagesVo result = passageQuestionSetsMapper.getPassagesBySetsId(setId);
        if (result == null) {
            return null;
        }

        // 根据套题ID, 获取题目信息
        List<ReadingPersonalPassageQuestionVo> questions = passageQuestionSetsMapper.getQuestionsByParam(setId, null);
        if (CollUtil.isEmpty(questions)) {
            return null;
        }

        // 题目类型名称转换
        processByQuestionType(questions);

        result.setQuestions(questions);
        result.setPlanId(param.getPlanId());
        result.setStudentId(param.getStudentId());
        result.setGenreName(GenreEnum.getByName(result.getGenre()));
        return result;
    }

    /**
     * @description: 【公共】题目类型转换
     * @author: lifengxu
     * @date: 2025/6/17 17:25
     */
    private void processByQuestionType(List<ReadingPersonalPassageQuestionVo> questionsByParam) throws BaseException {
        questionsByParam.forEach(item -> {
            item.setQuestionType(ReadingQuestionTypeEnum.getByName(item.getQuestionType()));
            if (StrUtil.isNotBlank(item.getOptionStr())) {
                item.setOptions(JSONUtil.parseArray(item.getOptionStr()));
            }
        });
    }
}
