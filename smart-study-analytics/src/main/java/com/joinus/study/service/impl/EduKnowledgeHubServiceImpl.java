package com.joinus.study.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.CommonResponse;
import com.joinus.study.interceptor.util.DataUtils;
import com.joinus.study.model.bo.QueryElectronicTextbookBo;
import com.joinus.study.model.bo.SpecialTrainingBo;
import com.joinus.study.model.dto.*;
import com.joinus.study.model.enums.PublisherEnum;
import com.joinus.study.model.param.*;
import com.joinus.study.model.vo.CreateQuestionByKnowledgeVo;
import com.joinus.study.model.vo.ExamAnalyzeVo;
import com.joinus.study.model.vo.KnowledgePointsVO;
import com.joinus.study.model.vo.ParseAnswerVo;
import com.joinus.study.model.vo.*;
import com.joinus.study.service.EduKnowledgeHubService;
import com.joinus.study.utils.LocalHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EduKnowledgeHubServiceImpl implements EduKnowledgeHubService {

    @Value("${edu-knowledge-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net}")
    private String eduKnowLedgeHubHostUrl;

    @Qualifier("eduKnowledgeHubWebClient")
    @Autowired
    private WebClient webClient;


    @Override
    public QuestionCoordinateDto questionCoordinateInfo(QueryQuestionCoordinateParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("ossKey", param.getOssKey());
        params.put("ossEnum", param.getOssEnum());
        params.put("paperSubjectType", param.getPaperSubjectType());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/coordinate-point/multi", param);
        return JSONUtil.toBean(response, QuestionCoordinateDto.class);
    }

    @Override
    public Flux<String> chatStream(SolveQuestionFromImgParam param) {
        return webClient.post()
                .uri("/api/edu-knowledge-hub/ai/ability/text/stream")
                .contentType(MediaType.APPLICATION_JSON)
                .body(Mono.just(param), SolveQuestionFromImgParam.class)
                .accept(MediaType.APPLICATION_STREAM_JSON)
                .retrieve()
                .bodyToFlux(String.class);
    }

    @Override
    public EraseHandwritingFromQuestionDto eraseHandwritingFromQuestion(EraseHandwritingFromQuestionParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/oss/erase-pen-marks", param);
        return JSONUtil.toBean(response, EraseHandwritingFromQuestionDto.class);
    }

    @Override
    public QueryMultiQuestionResultDto queryMultiQuestionAnalysisResults(QueryMultiQuestionResultParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("imgUrl", param.getImgUrl());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/xxx", params);
        return JSONUtil.toBean(response, QueryMultiQuestionResultDto.class);
    }

    @Override
    public Flux<String> querySingleQuestionAnalysisResults(SolveQuestionFromImgParam param) {
        log.info("edu-knowledge-hub solve stream  {}", JSONUtil.toJsonStr(param));
        return webClient.post()
                .uri("/api/edu-knowledge-hub/ai/ability/math/problem/solve/stream")
                .contentType(MediaType.APPLICATION_JSON)
                .body(Mono.just(param), SolveQuestionFromImgParam.class)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class);
    }

    @Override
    public ExamAnalysisQuestionLabelDto examAnalysisQuestionLabel(ExamAnalysisQuestionLabelParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("imgUrl", param.getImgUrlList());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/xxx", params);
        return JSONUtil.toBean(response, ExamAnalysisQuestionLabelDto.class);
    }

    /**
     * 检测是否试卷是否存在
     */
    @Override
    public CheckExamExsitenceDto checkExamExistence(CheckExamExsitenceParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exist-exam", param);
        return JSONUtil.toBean(response, CheckExamExsitenceDto.class);
    }

    @Override
    public CheckExamExsitenceDtoV2 checkExamExistenceV2(CheckExamExsitenceParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exist-exam/V2", param);
        return JSONUtil.toBean(response, CheckExamExsitenceDtoV2.class);
    }

    @Override
    public KnowledgePointsVO knowledgePoints(KnowledgePointsParams params) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/problem/knowledge-points", params);
        return JSONUtil.toBean(response, KnowledgePointsVO.class);
    }

    @Override
    public CutImageFromPositionsDto cutImageFromPositions(SingleCutImageFromPositionsParam cutImageFromPositionsParam) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/img/cut", cutImageFromPositionsParam);
        return JSONUtil.toBean(response, CutImageFromPositionsDto.class);
    }

    @Override
    public OssTokenDto ossToken(OssTokenParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("ossEnum", param.getOssEnum());
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/oss/ali/token", params);
        return JSONUtil.toBean(response, OssTokenDto.class);
    }

    @Override
    public CheckExistGraphicsDto checkExistGraphics(CheckExistGraphicsParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("questionText", param.getQuestionText());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exist-graphics", params);
        return JSONUtil.toBean(response, CheckExistGraphicsDto.class);
    }

    @Override
    public PresignedUrlDto presignedUrl(PresignedUrlParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("ossEnum", param.getOssEnum());
        params.put("ossKey", param.getOssKey());
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/oss/presigned-url", params);
        return JSONUtil.toBean(response, PresignedUrlDto.class);
    }

    @Override
    public SpecialTrainingDto specialTraining(SpecialTrainingBo bo) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training", bo);
        return JSONUtil.toBean(response, SpecialTrainingDto.class);
    }

    @Override
    public SpecialTrainingPdfDto specialTrainingPdf(SpecialTrainingBo bo) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/pdf", bo);
        return JSONUtil.toBean(response, SpecialTrainingPdfDto.class);
    }

    @Override
    public SpecialTrainingPdfDto specialTrainingPdfPreview(SpecialTrainingBo bo) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/pdf/preview", bo);
        return JSONUtil.toBean(response, SpecialTrainingPdfDto.class);
    }

    @Override
    public UUID queryFlexiblyGenerating(FlexiblyQuestionParam param) {
        Map<String, Object> params = new HashMap<>();
        params.put("questionId", param.getQuestionId());
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/problem/generate", params);
        if (DataUtils.isNotEmpty(response)) {
            FlexiblyGenerateDto bean = JSONUtil.toBean(response, FlexiblyGenerateDto.class);
            return bean.getData();
        } else {
            return null;
        }
    }

    @Override
    public CheckExistQuestionDto checkExistQuestion(CheckExistQuestionParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exist-question", param);
        return JSONUtil.toBean(response, CheckExistQuestionDto.class);
    }

    @Override
    public CreateExamDto createExam(CreateExamParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exam/cut/v2", param);
        return JSONUtil.toBean(response, CreateExamDto.class);
    }

    @Override
    public ExamAnalyzeVo examAnalyze(AiAnalyticsExamParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exam/analyze", param);
        return JSONUtil.toBean(response, ExamAnalyzeVo.class);
    }

    @Override
    public ParseAnswerVo parseAnswer(ParseAnswerParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/parse-answer", param);
        return JSONUtil.toBean(response, ParseAnswerVo.class);
    }

    @Override
    public ExamHasKnowledgeDto examHasKnowledge(ExamHasKnowledgeParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/check/exam/exist-knowledge-points", param);
        return JSONUtil.toBean(response, ExamHasKnowledgeDto.class);
    }

    @Override
    public CreateQuestionByKnowledgeVo createQuestionByKnowledgeAndQuestionType(CreateQuestionByKnowledgeParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/questions", param);
        return JSONUtil.toBean(response, CreateQuestionByKnowledgeVo.class);
    }

    @Override
    public CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeAndQuestionTypeV2(CreateQuestionByKnowledgeParamV2 param)  throws BaseException{
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/questions/V2", param);
        CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateQuestionByKnowledgeVoV2.class);
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

        // 过滤掉knowledgePoints中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> filteredKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().stream()
                    .filter(knowledgePoint -> CollUtil.isNotEmpty(knowledgePoint.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setKnowledgePoints(filteredKnowledgePoints);
        }
        // 过滤掉questionTypes中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> filteredQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().stream()
                    .filter(questionType -> CollUtil.isNotEmpty(questionType.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setQuestionTypes(filteredQuestionTypes);
        }
        return createQuestionByKnowledgeVo;
    }

    @Override
    public CreateQuestionByKnowledgeVoV2 createHolidayTrainingSectionQuestion(CreateQuestionByKnowledgeParamV2 param)  throws BaseException{
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/holiday-training/questions/section", param);
        CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateQuestionByKnowledgeVoV2.class);
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

        // 过滤掉knowledgePoints中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> filteredKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().stream()
                    .filter(knowledgePoint -> CollUtil.isNotEmpty(knowledgePoint.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setKnowledgePoints(filteredKnowledgePoints);
        }
        // 过滤掉questionTypes中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> filteredQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().stream()
                    .filter(questionType -> CollUtil.isNotEmpty(questionType.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setQuestionTypes(filteredQuestionTypes);
        }
        return createQuestionByKnowledgeVo;
    }
    @Override
    public CreateQuestionByKnowledgeVoV2 createHolidayTrainingChapterQuestion(CreateQuestionByKnowledgeParamV2 param)  throws BaseException{
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/holiday-training/questions/chapter", param);
        CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateQuestionByKnowledgeVoV2.class);
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

        // 过滤掉knowledgePoints中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> filteredKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().stream()
                    .filter(knowledgePoint -> CollUtil.isNotEmpty(knowledgePoint.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setKnowledgePoints(filteredKnowledgePoints);
        }
        // 过滤掉questionTypes中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> filteredQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().stream()
                    .filter(questionType -> CollUtil.isNotEmpty(questionType.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setQuestionTypes(filteredQuestionTypes);
        }
        return createQuestionByKnowledgeVo;
    }

    @Override
    public CreateQuestionByKnowledgeVoV2 selectSpecializedTrainingExamDetail(UUID examId) {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exams/"+examId+"/training", null);
        CreateQuestionByKnowledgeVoV2 createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateQuestionByKnowledgeVoV2.class);
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createQuestionByKnowledgeVo.getData(), "生成题目失败");

        // 过滤掉knowledgePoints中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getKnowledgePoints())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.KnowledgePointsDTO> filteredKnowledgePoints = createQuestionByKnowledgeVo.getData().getKnowledgePoints().stream()
                    .filter(knowledgePoint -> CollUtil.isNotEmpty(knowledgePoint.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setKnowledgePoints(filteredKnowledgePoints);
        }
        // 过滤掉questionTypes中questionIds为空的数据
        if (CollUtil.isNotEmpty(createQuestionByKnowledgeVo.getData().getQuestionTypes())) {
            List<CreateQuestionByKnowledgeVoV2.DataDTO.QuestionTypesDTO> filteredQuestionTypes = createQuestionByKnowledgeVo.getData().getQuestionTypes().stream()
                    .filter(questionType -> CollUtil.isNotEmpty(questionType.getQuestions()))
                    .collect(Collectors.toList());
            createQuestionByKnowledgeVo.getData().setQuestionTypes(filteredQuestionTypes);
        }
        return createQuestionByKnowledgeVo;
    }

    @Override
    public CreateTextBookQuestioVo selectSpecializedTextBookTrainingExamDetail(UUID examId) {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exams/"+examId, null);
        CreateTextBookQuestioVo createQuestionByKnowledgeVo = JSONUtil.toBean(response, CreateTextBookQuestioVo.class);
        return createQuestionByKnowledgeVo;
    }

    @Override
    public MathElecttronicTextbookVo getElectronicTextbook(QueryElectronicTextbookBo bo) {
        String url = StrUtil.format(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/electronic-textbook?publisher={}&grade={}&semester={}", bo.getPublisher(), bo.getGrade(), bo.getSemester());
        String responseBody = LocalHttpUtil.get(url, null);
        JSONObject jsonObject = JSONUtil.parseObj(responseBody);
        if (jsonObject.containsKey("data")) {
            JSONObject data = jsonObject.getJSONObject("data");
            return JSONUtil.toBean(data, MathElecttronicTextbookVo.class);
        }
        return null;
    }

    @Override
    public List<QuerySectionVideoDto> querySectionVideos(List<UUID> sectionIdList) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/section-videos", sectionIdList);
        if (StrUtil.isBlank(response)) {
            return new ArrayList<>();
        }
        JSONObject responseBody = JSONUtil.parseObj(response);
        List<QuerySectionVideoDto> sectionVideoDtos = JSONUtil.toList(responseBody.getJSONArray("data"), QuerySectionVideoDto.class);
        return sectionVideoDtos;
    }

    @Override
    public List<KnowledgePointDto> listKnowledgePoints(List<UUID> kpIds) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/api/math/knowledge-points/list" , kpIds);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getStr("data"),  KnowledgePointDto.class);
    }

    @Override
    public void reanalyzeExamKnowledgePoint(AiAnalyticsExamParam examAnalyzeParam) {
        LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exam/knowledge-point/reanalyze", examAnalyzeParam);
    }

    @Override
    public CreateTextBookQuestioVo createHolidayTrainingTextBookQuestion(CreateQuestionByKnowledgeParamV2 param)  throws BaseException{
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/holiday-training/questions/textbook", param);
        CreateTextBookQuestioVo createTextBookQuestioVo = JSONUtil.toBean(response, CreateTextBookQuestioVo.class);
        CommonResponse.ERROR.assertNotNull(createTextBookQuestioVo, "生成题目失败");
        CommonResponse.ERROR.assertNotNull(createTextBookQuestioVo.getData(), "生成题目失败");

        return createTextBookQuestioVo;
    }

    @Override
    public SpecialTrainingQuestionTypesDto getSpecialTrainingQuestionTypes(String knowledgePointIds) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("knowledgePointIds", knowledgePointIds);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/question-types", params);
        return JSONUtil.toBean(response, SpecialTrainingQuestionTypesDto.class);
    }

    @Override
    public QuestionTypesDto getQuestionTypesByQuestionId(UUID questionId) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("id", questionId);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/questions/" + questionId + "/knowledge-points-and-question-types", null);
        return JSONUtil.toBean(response, QuestionTypesDto.class);
    }


    @Override
    public MathExamVO getExamsByExamId(UUID examId) {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId, null);
        return JSONUtil.toBean(response, KnowLedgeExamsDTO.class).getData();
    }

    @Override
    public List<MathExamQuestionVO> getQuestionsByExamId(UUID examId) {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId + "/questions", null);
        return JSONUtil.toBean(response, KnowLedgeMathQuestionDTO.class).getData();
    }

    @Override
    public QuestionDetailDTO editQuestion(UUID examId, UUID questionId, AddExamQuestionParam param) {
        String response = LocalHttpUtil.put(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId + "/questions/" + questionId, JSONUtil.toJsonStr(param));
        return JSONUtil.toBean(response, QuestionDetailDTO.class);
    }

    @Override
    public QuestionDeleteDto deleteQuestions(UUID examId, UUID questionId) {
        String response = LocalHttpUtil.delete(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId + "/questions/" + questionId, null);
        return JSONUtil.toBean(response, QuestionDeleteDto.class);
    }

    @Override
    public QuestionDetailDTO addQuestion(UUID examId, AddExamQuestionParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId + "/questions", JSONUtil.toJsonStr(param));
        return JSONUtil.toBean(response, QuestionDetailDTO.class);
    }

    @Override
    public QuestionDetailDTO updateExamDetail(UUID examId) {
        Map<String, Object> params = new HashMap<>();
        params.put("state", "HUMAN_RECOGNIZED");
        String response = LocalHttpUtil.put(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + examId, JSONUtil.toJsonStr(params));
        return JSONUtil.toBean(response, QuestionDetailDTO.class);
    }

    @Override
    public SpecializedTrainingCrateExamDto saveExam(SpecializedTrainingCreateExamParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/by-questions", param);
        return JSONUtil.toBean(response, SpecializedTrainingCrateExamDto.class);
    }

    @Override
    public SpecializedTrainingUpdateExamDto specializedTrainingUpdateExam(AISpecializedTrainingUpdateExamParam updateExamParam) {
        String response = LocalHttpUtil.put(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/math/exams/" + updateExamParam.getExamId(), JSONUtil.toJsonStr(updateExamParam));
        return JSONUtil.toBean(response, SpecializedTrainingUpdateExamDto.class);
    }

    @Override
    public ExamAnalyzeVo cutQuestions(AiAnalyticsExamParam param) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/exam/cut-questions" , JSONUtil.toJsonStr(param));
        return JSONUtil.toBean(response,  ExamAnalyzeVo.class);
    }

    @Override
    public TextBookCatalogueDto textbooksChaptersSections(UUID examId,PublisherEnum  publisher) {
        String url = eduKnowLedgeHubHostUrl
                + "/api/edu-knowledge-hub/ai/ability/math/books-chapters-sections/exams/"+examId;
        if(publisher != null){
            url = url + "?publisher="+publisher.getValue();
        }
        String response = LocalHttpUtil.get(url,null);
        return JSONUtil.toBean(response,  TextBookCatalogueDto.class);
    }

    @Override
    public KnowledgePointBySelectDto getKnowledgePointBySection(List<UUID> sectionIds) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("sectionIds", sectionIds);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/sections/knowledge-points",params);
        return JSONUtil.toBean(response,  KnowledgePointBySelectDto.class);
    }

    @Override
    public KnowledgePointQuestionTypesDtoV2New questionTypeV2(String knowledgePointIds) {
        String response = LocalHttpUtil.post(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/special-training/question-types/V2" , knowledgePointIds);
        return JSONUtil.toBean(response,  KnowledgePointQuestionTypesDtoV2New.class);
    }

    @Override
    public List<MathSectionDto> listMathSections(Integer grade, Integer semester, PublisherEnum publisher) {
        HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.put("grade", grade);
        requestMap.put("semester", semester);
        requestMap.put("publisher", publisher);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/sections" , requestMap);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getStr("data"),  MathSectionDto.class);
    }

    @Override
    public List<MathChapterDto> listMathChapters() {
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/chapters" , null);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getStr("data"),  MathChapterDto.class);
    }
}
