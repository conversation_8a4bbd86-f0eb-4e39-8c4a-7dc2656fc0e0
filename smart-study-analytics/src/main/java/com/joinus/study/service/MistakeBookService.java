package com.joinus.study.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.study.model.dto.QuestionAnswerDto;
import com.joinus.study.model.entity.MistakeBook;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.study.model.enums.MistakeBookSourceEnum;
import com.joinus.study.model.param.ExamMistakeSaveParam;
import com.joinus.study.model.param.MistakeBookAddParam;
import com.joinus.study.model.param.QueryMistakeBookParam;
import com.joinus.study.model.vo.MistakeBookDetailsVo;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【mistake_book】的数据库操作Service
* @createDate 2025-03-11 09:47:33
*/
public interface MistakeBookService extends IService<MistakeBook> {

    /**
     * 增加错题
     * @param mistakeBooks
     */
    void add(List<MistakeBookAddParam> mistakeBooks) ;

    /**
     * 分页查询错题
     * @param param
     * @return
     */
    Page pages(QueryMistakeBookParam param);

    /**
     * 查询错题详情
     * @param param
     * @return
     */
    MistakeBookDetailsVo geDetails(QueryMistakeBookParam param);

    /**
     * 获取题目答案完整信息详情
     * @param id
     * @return reactor.core.publisher.Flux<java.lang.String>
     *
     *
    */
    Flux<String> getCompleteQuestionAnswerDto(Long id);

    /**
     * 试卷错题记录存储
     * @return
     */
    void examMistakeSave(ExamMistakeSaveParam param);

    /**
     * 删除错题
     * @param id
     */
    void deleteById(Long id);

    List<MistakeBook> getIsAddMistakesBook(Long studentId, UUID questionId, Long id, MistakeBookSourceEnum photo_question);
}
