<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPersonalPassagesQuestionMapper">
    <update id="updateResultCode">
        update reading_personal_passage_questions
        set result = #{result},
            updated_at = now()
            where personal_passage_id = #{personalPassageId}
            and question_id = #{questionId}
    </update>
    <select id="selectPersonalPassageQuestionVoList"
            resultType="com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo">
        select q.id                 as questionId,
               q.passage_id,
               q.question_type      as questionType,
               q.content            as questionContent,
               a.answer             as answer,
               a.content            as answerContent,
               rqa.answer           as formulaAnswer,
               raf.formula_template as formulaTemplate
        from reading_passage_questions q
                left join reading_question_answers a on a.question_id = q.id
            AND a.deleted_at IS NULL
            AND a.answering_formula_id IS NULL
                 left join reading_question_answers rqa on rqa.question_id = q.id
            AND rqa.deleted_at is null
            AND rqa.answering_formula_id is not null
                 left join reading_answering_formulas raf on raf.id = rqa.answering_formula_id
        where q.deleted_at is null
          and q.passage_id = #{passageId}
          and q.id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </select>

    <select id="getPersonalPassageQuestionVoList" resultType="com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo">
        select q.id            as questionId,
               q.passage_id,
               q.question_type as questionType,
               (a.answer :: JSONB) ->> '答案' AS answer,
               (q.CONTENT :: JSONB) ->> '问题' AS questionContent,
               (q.CONTENT :: JSONB) ->> '选项' AS optionStr,
               a.content as answerContent,
               (rqa.answer :: JSONB) ->> '答案' as formulaAnswer,
               raf.formula_template as formulaTemplate
        from reading_passage_questions q
            inner join reading_question_answers a on a.question_id = q.id
            AND a.deleted_at IS NULL
            AND a.answering_formula_id IS NULL
            left join reading_question_answers rqa on rqa.question_id = q.id
            AND rqa.deleted_at is null
            AND rqa.answering_formula_id is not null
            left join reading_answering_formulas raf on raf.id = rqa.answering_formula_id
        where q.deleted_at is null
          and q.passage_id = #{passageId}
          and q.id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </select>

    <select id="getPointIdsByQuestionIds" resultType="com.joinus.study.model.param.ReadingQuestionknowledgePintVo">
        select
            q.question_id,
            k.id as knowledgePointId,
            k.name as name
        from reading_question_knowledge_points q
        inner join reading_knowledge_points k on k.id = q.knowledge_point_id
        where k.deleted_at is null and q.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </select>
    <select id="getPassagesQuestionsListByStudentId"
            resultType="com.joinus.study.model.entity.ReadingPersonalPassagesQuestions">
        select
            p.id as id,
            q.question_id as questionId,
            q.question_type as questionType,
            q.result as result,
            q.user_answer as userAnswer
        from reading_personal_passage_questions q
            inner join reading_personal_passages p on p.id = q.personal_passage_id
        where q.deleted_at is null
            and p.student_id = #{studentId}
            and q.result != 1
            and p.created_at::date <![CDATA[ >= ]]> #{startDate}
            and p.created_at::date <![CDATA[ <= ]]> #{endDate}
    </select>

    <select id="getStudentDoQuestionIds" resultType="java.util.UUID">
        select distinct p.question_id
        from reading_personal_passages q
                 inner join reading_personal_passage_questions p
                            on p.personal_passage_id = q.id and p.deleted_at is null
        where q.deleted_at is null
          and q.is_discard = 0
          and q.student_id = #{studentId}
        <if test="entryType!= null">
            and q.entry_type = #{entryType}
        </if>
    </select>

    <select id="getStudentDoSetsIds" resultType="java.util.UUID">
        select distinct sets_id
        from reading_personal_passages
        where deleted_at is null
          and is_discard = 0
          and sets_id is not null
          and student_id = #{studentId}
        <if test="entryType!= null">
            and entry_type = #{entryType}
        </if>
    </select>

    <select id="selectPersonalPassageQuestionVoByPassageId"
            resultType="com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo">
        select
            rpp.id,
            rpp.user_answer as userAnswer,
            rpp.result,
            q.id as questionId,
            q.passage_id,
            rpp.question_type as questionType,
            rpp.is_feedback as isFeedback,
            q.content as questionContent
        from reading_personal_passage_questions rpp
        inner join reading_passage_questions q on rpp.question_id = q.id
        where q.deleted_at is null and rpp.personal_passage_id = #{personalPassageId}
        order by q.passage_id,rpp.question_no
    </select>
    <select id="selectKnowledgePointsByid" resultType="java.lang.String">
        select
            string_agg(distinct k.name,',')
        from reading_personal_passage_questions pq
                 inner join  reading_question_knowledge_points q on pq.question_id = q.question_id
                 inner join reading_knowledge_points k on k.id = q.knowledge_point_id
        where pq.personal_passage_id = #{id} and pq.result != 1
    </select>

    <!--[定向爆破]获取学生当前知识点错题-->
    <select id="getCurrentKnowledgeErrorQuestions" resultType="com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo">
        SELECT rpp.passage_id          AS passageId,
               rppq.question_id        AS questionId,
               rppq.question_type      AS questionType,
               rqkp.knowledge_point_id AS knowledgePointId
        FROM reading_personal_passages rpp
                 LEFT JOIN reading_personal_passage_questions rppq ON rpp.ID = rppq.personal_passage_id
            AND rppq.deleted_at IS NULL
            AND rppq."result" != 1
            AND rppq.is_blocked = 0
                INNER JOIN reading_passage_questions rpq ON rpq.id = rppq.question_id
                AND rpq.deleted_at IS NULL AND rpq.is_enabled = 1 AND rpq.is_audit = 1
                 INNER JOIN reading_question_knowledge_points rqkp ON rqkp.question_id = rppq.question_id
            AND rqkp.knowledge_point_id = #{param.knowledgePointId}
        WHERE rpp.deleted_at IS NULL
          AND rpp.status = 3
          AND rpp.student_id = #{param.studentId}
        ORDER BY RANDOM() LIMIT 3
    </select>

    <!--[定向爆破]根据知识点获取文章关联题目-->
    <select id="getQuestionsByKnowledgePoint" resultType="com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo">
        SELECT rpq.passage_id          AS passageId,
               rpq."id"                AS questionId,
               rpq.question_type       AS questionType,
               ARRAY_AGG(rqkp.knowledge_point_id) AS knowledgePointIds
        FROM reading_question_knowledge_points rqkp
                 INNER JOIN reading_passage_questions rpq
                            ON rpq."id" = rqkp.question_id
                                AND rpq.deleted_at IS NULL
                                AND rpq.is_enabled = 1
                                AND rpq.is_audit = 1
        WHERE rpq.passage_id = #{param.passageId}
        <if test="param.weakKnowledgePointIds != null and param.weakKnowledgePointIds.size() > 0 ">
            AND rqkp.knowledge_point_id in
            <foreach collection="param.weakKnowledgePointIds" open="(" close=")" separator="," item="knowledgeId">
                #{knowledgeId}::uuid
            </foreach>
        </if>
        <if test="existQuestionIds != null and existQuestionIds.size() > 0 ">
            AND rpq."id" not in
            <foreach collection="existQuestionIds" open="(" close=")" separator="," item="questionId">
                #{questionId}::uuid
            </foreach>
        </if>
        <if test="existQuestionTypes != null and existQuestionTypes.size() > 0 ">
            AND rpq.question_type not in
            <foreach collection="existQuestionTypes" open="(" close=")" separator="," item="questionType">
                #{questionType}
            </foreach>
        </if>
        GROUP BY
        rpq.passage_id,rpq."id",rpq.question_type
        ORDER BY RANDOM()
        LIMIT #{count}
    </select>

    <!--[强化训练]获取学生假期训练已练习套题ID-->
    <select id="getActivityStudentDoSetsIds" resultType="java.util.UUID">
        select sets_id
        from reading_personal_passages
        where deleted_at is null
        and is_discard = 0
        and entry_type = 4
        and student_id = #{param.studentId}
        and plan_id = #{param.planId}
        <if test="status != null">
            AND status >= #{status}
        </if>
        <if test="personalPassageId!= null">
            and id != #{personalPassageId}
        </if>
        <if test="localDate!= null">
            and created_at::date = #{localDate}
        </if>
    </select>

    <!--[强化训练]获取学生已经完成单元ID-->
    <select id="getCompletedUnitIds" resultType="java.util.UUID">
        SELECT
            rp.unit_id
        FROM
            reading_personal_passages rpp
                INNER JOIN reading_passages rp ON rp.id = rpp.passage_id
        WHERE
            rpp.deleted_at IS NULL
          AND rpp.is_discard = 0
          AND rpp.status > 1
          AND rpp.student_id = #{param.studentId}
        GROUP BY
            rp.unit_id
        <if test="completedNumber != null and completedNumber != 0">
            HAVING
            COUNT(rp.unit_id) >= #{completedNumber}
        </if>
    </select>

    <!--[强化训练]获取待出题单元ID-->
    <select id="getActivityPracticePendingUnitIds" resultType="java.util.UUID">
        SELECT
            ru.id
        FROM
            reading_units ru
        WHERE
            ru.deleted_at IS NULL
          AND ru.grade = #{param.grade}
          AND ru.semester = #{param.semester}
          <if test="param.unitIds!= null and param.unitIds.size() > 0">
            AND ru.id NOT IN
            <foreach collection="param.unitIds" item="unitId" open="(" close=")" separator=",">
                #{unitId}
            </foreach>
          </if>
        ORDER BY
            ru.order_no
        LIMIT 1
    </select>

    <!--[巩固练习]获取学生假期训练已练习题ID-->
    <select id="getActivityStudentDoPersonalPassageIds" resultType="java.lang.Long">
        SELECT "id"
        FROM reading_personal_passages
        WHERE deleted_at IS NULL
          AND is_discard = 0
          AND entry_type = 5
          AND student_id = #{param.studentId}
          AND plan_id = #{param.planId}
          AND created_at::date = #{localDate}
        <if test="status!= null">
            AND status >= #{status}
        </if>
        <if test="param.personalPassageId!= null">
            and id!= #{param.personalPassageId}
        </if>
    </select>

    <!--[巩固练习]获取学生假期训练已练习题目ID-->
    <select id="getActivityStudentDoQuestionIds" resultType="java.util.UUID">
        SELECT DISTINCT rppq.question_id
        FROM reading_personal_passages rpp
                 inner join reading_personal_passage_questions rppq on rppq.personal_passage_id = rpp.id
            AND rppq.deleted_at IS NULL AND rppq.is_blocked = 0
        WHERE rpp.deleted_at IS NULL
          AND rpp.is_discard = 0
          AND rpp.entry_type = 5
          AND rpp.student_id = #{param.studentId}
          AND rpp.plan_id = #{param.planId}
    </select>

    <!--[定向爆破]获取文章ID、题目ID-->
    <select id="getDirectionalBlastingQuestionIds" resultType="com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo">
        SELECT rp."id" as passageId,
        rpq."id" AS questionId
        FROM reading_passage_question_sets rpqs
        LEFT JOIN reading_passage_question_set_entries rpqse ON rpqse.set_id = rpqs."id"
        INNER JOIN reading_passage_questions rpq ON rpq."id" = rpqse.question_id
        AND rpq.deleted_at IS NULL
        AND rpq.is_enabled = 1
        AND rpq.is_audit = 1
        <if test="param.doQuestionIds != null and param.doQuestionIds.size() > 0">
            AND rpq.id NOT IN
            <foreach collection="param.doQuestionIds" item="doQuestionId" open="(" separator="," close=")">
                #{doQuestionId}
            </foreach>
        </if>
        INNER JOIN reading_passages rp ON rp."id" = rpqs.passage_id
        AND rp.deleted_at IS NULL
        AND rp.is_audit = 1
        AND rp.is_enabled = 1
        <if test="param.passageId!= null">
            AND rp."id" = #{param.passageId}
        </if>
        INNER JOIN reading_units ru ON ru.id = rp.unit_id AND ru.deleted_at IS NULL
        <if test="param.grade != null">
            AND ru.grade = #{param.grade}
        </if>
        <if test="param.semester != null">
            AND ru.semester = #{param.semester}
        </if>
        <if test="param.weakKnowledgePointIds != null and param.weakKnowledgePointIds.size() > 0">
            INNER JOIN (
            SELECT rqkp.question_id FROM reading_question_knowledge_points rqkp
            WHERE 1=1
            <if test="param.weakKnowledgePointIds != null and param.weakKnowledgePointIds.size() > 0">
                AND rqkp.knowledge_point_id IN
                <foreach collection="param.weakKnowledgePointIds" item="knowledgePoint" open="(" separator=","
                         close=")">
                    #{knowledgePoint}
                </foreach>
            </if>
            )kp ON kp.question_id = rpq."id"
        </if>
        WHERE rpqs.deleted_at IS NULL
        AND rpqs.is_audit = 1
        AND rpqs.is_enabled = 1
        <if test="count != null">
            ORDER BY RANDOM() LIMIT #{count}
        </if>
    </select>

</mapper>
