<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.PersonalExamQuestionMapper">

    <insert id="insertPersonalExamQuestion" parameterType="com.joinus.study.model.entity.PersonalExamQuestion">
        INSERT INTO personal_exam_question
        (
            personal_exam_id,
            question_id,
            question_type,
            result,
            sort_no
        )
        VALUES
            (
                #{entity.personalExamId},
                #{entity.questionId}::uuid,
                #{entity.questionType}::question_type_enum,
                #{entity.result}::result_enum,
                #{entity.sortNo}
            )
    </insert>
    <select id="selectQuestionInfoByknowledgePointId"
            resultType="com.joinus.study.model.vo.ExamQuestionFileVo">
        select
            mq.id as "id", mq.sort_no,
            mq.personal_exam_id as personalExamId,
            mq.question_type as "questionType",
            mq.question_id as "questionId",
            mq.result as "result",
            mqs.content as "questionContent",
            STRING_AGG(f.oss_url,',' ORDER BY qf.sort_no ASC ) as "questionOssUrl"
        from personal_exam_question mq
                 INNER join question_knowledge_point qkp on mq.question_id = qkp.question_id and qkp.deleted_at is null
                 left JOIN math_question_files qf on mq.question_id = qf.question_id  and qkp.deleted_at is null and qf.type = 1
                 left join files f on f.id = qf.file_id   and f.deleted_at is null
                 left join math_questions mqs on mqs.id = mq.question_id
        where qkp.knowledge_point_id = #{knowledgePointId}::uuid
          and mq.personal_exam_id = #{personalExamId}
          and qkp.exam_id is not null
          and mq.deleted_at is null
        GROUP BY
          mq.id, mq.sort_no,
          mq.personal_exam_id,
          mq.question_type,
          mq.question_id,
          mqs.content,
          mq.result
    </select>
    <select id="selectErrorQuestionNosByPersonalExamId" resultType="java.lang.String">
        select
                STRING_AGG(peq.sort_no::TEXT, ',')
        FROM personal_exam_question peq
        WHERE peq.result != 'correct' and peq.personal_exam_id =#{personalExamId}
        <if test="questionType != null">
            and peq.question_type = #{questionType}::question_type_enum
        </if>
    </select>
</mapper>
