<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPassageQuestionSetsMapper">

    <select id="queryQuestionSetsList" resultType="com.joinus.study.model.vo.ReadingPassageQuestionSetsVo">
        SELECT
            qs.id,
            qs.name,
            qs.is_audit,
            qs.is_enabled,
            TO_CHAR(qs.created_at, 'YYYY-MM-DD HH24:MI:SS') as created_at,
            count(DISTINCT qse.question_id) as question_count,
            STRING_AGG(DISTINCT q.question_type, '/') AS question_types,
            STRING_AGG(DISTINCT kp.name, '/') AS knowledge_points
        FROM
            reading_passage_question_sets qs
                inner join reading_passage_question_set_entries qse on qs.id = qse.set_id
                inner join reading_passage_questions q on q.id = qse.question_id
                left join reading_question_knowledge_points qkp on qkp.question_id = qse.question_id
                left join reading_knowledge_points kp on kp.id = qkp.knowledge_point_id
        where qs.passage_id = #{passageId}
          AND qs.deleted_at is null
        group by qs.id,qs.name,qs.is_audit,qs.is_enabled,qs.created_at
        order by qs.created_at asc
    </select>
    <select id="getQuestionSetsItemInfo" resultType="com.joinus.study.model.vo.ReadingQuestionSetsItemViewVo">
        select q.id,
               q.content,
               q.question_type,
               qa.answer            as answer,
               qa.content           as answerContent,
               q.is_audit,
               q.is_enabled,
               q.question_type,
               q.question_type,
               rqa.answer           as formulaAnswer,
               raf.formula_template as formulaTemplate
        from reading_passage_question_set_entries qse
                 inner join reading_passage_questions q on qse.question_id = q.id
                 inner join reading_question_answers qa on qa.question_id = q.id
            AND qa.deleted_at IS NULL
            AND qa.answering_formula_id IS NULL
                 left join reading_question_answers rqa on rqa.question_id = q.id
            AND rqa.deleted_at is null
            AND rqa.answering_formula_id is not null
                 left join reading_answering_formulas raf on raf.id = rqa.answering_formula_id
                 inner join reading_passage_question_sets qs on qs.id = qse.set_id
        where qse.set_id = #{questionSetId}
        order by qse.order_no
    </select>
    <select id="getKnowledgePoints" resultType="java.util.Map">
        select
            kp.id,
            kp.name
        from reading_knowledge_points kp
        inner join reading_question_knowledge_points qkp on qkp.knowledge_point_id = kp.id
        where kp.deleted_at is null
            and qkp.question_id = #{questionId}
    </select>

    <!--[阅读训练]获取套题集合-->
    <select id="getReadingTrainingSetsIds" resultType="java.util.UUID">
        SELECT rpqs."id"
        FROM reading_passage_question_sets rpqs
        LEFT JOIN reading_passage_question_set_entries rpqse ON rpqse.set_id = rpqs."id"
        INNER JOIN reading_passage_questions rpq ON rpq."id" = rpqse.question_id
        AND rpq.deleted_at IS NULL
        AND rpq.is_enabled = 1
        AND rpq.is_audit = 1
        INNER JOIN reading_passages rp ON rp."id" = rpqs.passage_id
        AND rp.deleted_at IS NULL
        AND rp.is_audit = 1
        AND rp.is_enabled = 1
        <if test="param.passageId != null">
            AND rp."id" = #{param.passageId}
        </if>
        <if test="param.unitId != null">
            AND rp.unit_id = #{param.unitId}
        </if>
        <if test="param.weakKnowledgePointIds != null and param.weakKnowledgePointIds.size() > 0">
            INNER JOIN (
            SELECT rqkp.question_id FROM reading_question_knowledge_points rqkp
            WHERE rqkp.knowledge_point_id IN
            <foreach collection="param.weakKnowledgePointIds" item="knowledgePoint" open="(" separator="," close=")">
                #{knowledgePoint}
            </foreach>
            )kp ON kp.question_id = rpq."id"
        </if>
        WHERE rpqs.deleted_at IS NULL
        AND rpqs.is_audit = 1
        AND rpqs.is_enabled = 1
        <if test="param.doSetsIds != null and param.doSetsIds.size() > 0">
            AND rpqs.id NOT IN
            <foreach collection="param.doSetsIds" item="doSetsId" open="(" separator="," close=")">
                #{doSetsId}
            </foreach>
        </if>
        GROUP BY rpqs."id"
        ORDER BY COUNT(rpqse.question_id) desc
    </select>

    <!--根据套题id获取套题文章-->
    <select id="getPassagesBySetsId" resultType="com.joinus.study.model.vo.ReadingPersonalPassagesVo">
        SELECT rp."id"    AS passageId,
               rp.title   AS title,
               rp.content AS content,
               rp.genre   AS genre,
               rp.unit_id AS unitId,
               ru.grade   AS grade,
               rpqs."id"  AS setsId
        FROM reading_passage_question_sets rpqs
                 inner join reading_passages rp ON rp."id" = rpqs.passage_id
                 inner join reading_units ru ON ru."id" = rp.unit_id
            AND rp.deleted_at IS NULL
            AND rp.is_audit = 1
            AND rp.is_enabled = 1
        WHERE rpqs.ID = #{setsId}
    </select>

    <!--根据套题id获取套题题目-->
    <select id="getQuestionsByParam" resultType="com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo">
        SELECT rpq.passage_id    AS passageId,
               rpq."id"          AS questionId,
               rpq.question_type AS questionType,
               rqa.CONTENT       AS answerContent,
               (rqa.answer :: JSONB) ->> '答案' AS answer,
               (rpq.CONTENT :: JSONB) ->> '问题' AS questionContent,
               (rpq.CONTENT :: JSONB) ->> '选项' AS optionStr,
               (rqas.answer :: JSONB) ->> '答案' AS answer,
               raf.formula_template AS formulaTemplate
        FROM
            reading_passage_question_sets rpqs
            LEFT JOIN reading_passage_question_set_entries rpqse
        ON rpqse.set_id = rpqs."id"
            INNER JOIN reading_passage_questions rpq ON rpq."id" = rpqse.question_id
            AND rpq.deleted_at IS NULL
            AND rpq.is_enabled = 1
            AND rpq.is_audit = 1
            LEFT JOIN reading_question_answers rqa ON rpq."id" = rqa.question_id
            AND rqa.deleted_at IS NULL
            AND rqa.answering_formula_id IS NULL
            left join reading_question_answers rqas on rpq."id" = rqas.question_id
            AND rqas.deleted_at is null
            AND rqas.answering_formula_id is not null
            left join reading_answering_formulas raf on raf.id = rqas.answering_formula_id
        WHERE 1=1
        <if test="setsId != null">
            AND rpqs.ID = #{setsId}
        </if>
        <if test="questionIds != null and questionIds.size() > 0">
            AND rpq."id" IN
            <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
                #{questionId}
            </foreach>
        </if>
        order by rpqse.order_no
    </select>

    <!--[练习计划]获取套题ID、题目ID-->
    <select id="getExercisePlanSetsIds" resultType="com.joinus.study.model.dto.ReadingSetsQuestionDto">
        SELECT rpqs."id" as setsId,
        rpq."id" AS questionId
        FROM reading_passage_question_sets rpqs
        LEFT JOIN reading_passage_question_set_entries rpqse ON rpqse.set_id = rpqs."id"
        INNER JOIN reading_passage_questions rpq ON rpq."id" = rpqse.question_id
        AND rpq.deleted_at IS NULL
        AND rpq.is_enabled = 1
        AND rpq.is_audit = 1
        <if test="param.doQuestionIds != null and param.doQuestionIds.size() > 0">
            AND rpq.id NOT IN
            <foreach collection="param.doQuestionIds" item="doQuestionId" open="(" separator="," close=")">
                #{doQuestionId}
            </foreach>
        </if>
        INNER JOIN reading_passages rp ON rp."id" = rpqs.passage_id
        AND rp.deleted_at IS NULL
        AND rp.is_audit = 1
        AND rp.is_enabled = 1
        <if test="param.genres != null and param.genres.size() > 0">
            AND rp.genre IN
            <foreach collection="param.genres" item="genre" open="(" separator="," close=")">
                #{genre}
            </foreach>
        </if>
        INNER JOIN reading_units ru ON ru.id = rp.unit_id AND ru.deleted_at IS NULL
        <if test="param.grade != null">
            AND ru.grade = #{param.grade}
        </if>
        <if test="param.semester != null">
            AND ru.semester = #{param.semester}
        </if>
        <if test="param.weakKnowledgePointIds != null and param.weakKnowledgePointIds.size() > 0">
            INNER JOIN (
            SELECT rqkp.question_id FROM reading_question_knowledge_points rqkp
            WHERE rqkp.knowledge_point_id IN
            <foreach collection="param.weakKnowledgePointIds" item="knowledgePoint" open="(" separator="," close=")">
                #{knowledgePoint}
            </foreach>
            )kp ON kp.question_id = rpq."id"
        </if>
        WHERE rpqs.deleted_at IS NULL
        AND rpqs.is_audit = 1
        AND rpqs.is_enabled = 1
    </select>

    <!--[假期强化练习]获取套题ID-->
    <select id="getHolidayIntensiveTrainingSetsIds" resultType="java.util.UUID">
        SELECT rpqs."id"
        FROM reading_passage_question_sets rpqs
                 INNER JOIN reading_passages rp ON rp."id" = rpqs.passage_id
            AND rp.deleted_at IS NULL
            AND rp.is_audit = 1
            AND rp.is_enabled = 1
            AND rp.unit_id = #{param.unitId}
        WHERE rpqs.deleted_at IS NULL
          AND rpqs.is_audit = 1
          AND rpqs.is_enabled = 1
        <if test="param.doSetsIds != null and param.doSetsIds.size() > 0">
            AND rpqs.id NOT IN
            <foreach collection="param.doSetsIds" item="doSetsId" open="(" separator="," close=")">
                #{doSetsId}
            </foreach>
        </if>
        ORDER BY RANDOM() LIMIT 1
    </select>
</mapper>
