<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.QuestionKnowledgePointMapper">

    <select id="selectKnowledgePointStatisticsByExamId"
            resultType="com.joinus.study.model.vo.KnowledgePointStatisticsVo">
        SELECT qkp.exam_id,
               qkp.knowledge_point_name name,
               qkp.knowledge_point_id,
               COUNT(DISTINCT peq.question_id) totalQuestionCount,
               STRING_AGG(peq.question_type||' ' || peq.sort_no::TEXT, ',') AS questionNos,
               SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS totalCorrectCount
        FROM
            personal_exam pe
                JOIN
            personal_exam_question peq ON pe.ID = peq.personal_exam_id
                JOIN
            question_knowledge_point qkp ON peq.question_id = qkp.question_id AND pe.exam_id=qkp.exam_id and pe.publisher=qkp.publisher
        WHERE
            pe.id =#{id}
        GROUP BY
            qkp.knowledge_point_id ,qkp.exam_id,
            qkp.knowledge_point_name


    </select>
    <select id="getStudentIds" resultType="com.joinus.study.model.vo.StudentInfoVo">

        SELECT
            vas.student_id,vas.class_id
        FROM
            view_active_students vas
        WHERE
                class_id = ( SELECT class_id FROM view_active_students WHERE student_id =#{studentId} )
    </select>
    <select id="getClassKnowledgePointStatics"
            resultType="com.joinus.study.model.vo.KnowledgePointStatisticsVo">
        SELECT
            qkp.knowledge_point_name name,
            qkp.knowledge_point_id,
            COUNT(peq.question_id) totalQuestionCount,
            SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS totalCorrectCount
        FROM
            personal_exam pe
                JOIN
            personal_exam_question peq ON pe.id = peq.personal_exam_id
                JOIN
            question_knowledge_point qkp ON peq.question_id = qkp.question_id
        WHERE
            qkp.exam_id = #{examId}
          <if test="studentIds != null and studentIds.size() > 0">
          AND pe.student_id IN
          <foreach collection="studentIds" item="studentId" separator="," open="(" close=")">
            #{studentId}
          </foreach>
          </if>
        GROUP BY
            qkp.knowledge_point_id,
            qkp.exam_id,
            qkp.knowledge_point_name
    </select>

</mapper>
