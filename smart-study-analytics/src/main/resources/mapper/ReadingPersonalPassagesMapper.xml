<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPersonalPassagesMapper">
    <update id="updatePlanIdIsNullById">
        UPDATE reading_personal_passages
        SET plan_id = null
        WHERE id = #{id}
    </update>

    <!--    管理后台-分页 -->
    <select id="pages" resultType="com.joinus.study.model.vo.ReadingPersonalPassagesBackendVO">
        SELECT
            tpp.id,
            tpu.student_name,
            tpu.student_id,
            tpu.tel_num AS phone,
            CASE WHEN tu.grade = 1 THEN '一年级'
            WHEN tu.grade = 2 THEN '二年级'
            WHEN tu.grade = 3 THEN '三年级'
            WHEN tu.grade = 4 THEN '四年级'
            WHEN tu.grade = 5 THEN '五年级'
            WHEN tu.grade = 6 THEN '六年级'
            WHEN tu.grade = 7 THEN '七年级'
            WHEN tu.grade = 8 THEN '八年级'
            WHEN tu.grade = 9 THEN '九年级'
            ELSE '高中' END AS grade_name,
            CASE WHEN tu.semester = 1 THEN '上学期'
            ELSE '下学期' END AS semester,
            tu.name AS unit_name,
            tp.genre,
            tp.title,
            tp.content,
            tpp.knowledge_points,
            tpp.created_at,
            tppq.correct_number,
            tppq.error_number,
            tppq.accuracy,
            tpp.entry_type
        FROM reading_personal_passages tpp
            LEFT JOIN reading_personal_user tpu ON tpp.student_id = tpu.student_id
            LEFT JOIN reading_passages tp ON tpp.passage_id = tp.id
            LEFT JOIN reading_units tu ON tp.unit_id = tu.id
            LEFT JOIN (SELECT personal_passage_id,
            sum(result) correct_number,
            count(id) - sum(result) AS error_number,
            TO_CHAR(sum(result)::NUMERIC * 100 /  NULLIF(count(id), 0),'FM999999990%') AS accuracy
            FROM reading_personal_passage_questions
            GROUP BY personal_passage_id) tppq ON tppq.personal_passage_id = tpp.id
        WHERE tpp.deleted_at IS NULL
        <if test="null != pageParam.studentName and '' != pageParam.studentName">
            AND tpu.student_name ILIKE concat('%',#{pageParam.studentName},'%')
        </if>
        <if test="null != pageParam.phone and '' != pageParam.phone">
            AND tpu.tel_num ILIKE concat('%',#{pageParam.phone},'%')
        </if>
        <if test="null != pageParam.grade">
            AND tu.grade = #{pageParam.grade}
        </if>
        <if test="null != pageParam.semester">
            AND tu.semester = #{pageParam.semester}
        </if>
        <if test="null != pageParam.unitId">
            AND tu.id = #{pageParam.unitId}
        </if>
        <if test="null != pageParam.genre and '' != pageParam.genre">
            AND tp.genre ILIKE concat('%',#{pageParam.genre},'%')
        </if>
        <if test="null != pageParam.title and '' != pageParam.title">
            AND tp.title ILIKE concat('%',#{pageParam.title},'%')
        </if>
        <if test="null != pageParam.knowledgePoints and '' != pageParam.knowledgePoints">
            AND tpp.knowledge_points ILIKE concat('%',#{pageParam.knowledgePoints},'%')
        </if>
        <if test="null != pageParam.timeStart and '' != pageParam.timeStart">
            AND tpp.created_at >= (#{pageParam.timeStart})::timestamp
        </if>
        <if test="null != pageParam.timeEnd and '' != pageParam.timeEnd">
            AND tpp.created_at &lt;= (#{pageParam.timeEnd} ||' 23:59:59.999')::timestamp
        </if>
        <if test="null != pageParam.studentId">
            AND tpu.student_id = #{pageParam.studentId}
        </if>
        ORDER BY tpp.id DESC
    </select>

    <!--    管理后台-详情 -->
    <resultMap id="resultMap" type="com.joinus.study.model.vo.ReadingPersonalPassagesBackendVO">
        <result column="title" property="title"/>
        <result column="genre" property="genre"/>
        <result column="content" property="content"/>
        <result column="knowledge_points" property="knowledgePoints"/>
        <collection property="questionList" ofType="com.joinus.study.model.vo.ReadingPassageQuestionsAnswersVO">
            <result column="question_no" property="questionNo"/>
            <result column="question_type" property="questionType"/>
            <result column="question_content" property="content"/>
            <result column="answer" property="answer"/>
            <result column="answer_content" property="answerContent"/>
            <result column="user_answer" property="userAnswer"/>
            <result column="result" property="result"/>
            <result column="formula_answer" property="formulaAnswer"/>
            <result column="formula_template" property="formulaTemplate"/>
        </collection>
    </resultMap>
    <select id="query" resultMap="resultMap">
        SELECT tp.title,
               tp.genre,
               tp.content,
               tpp.knowledge_points,
               tppq.question_id,
               tppq.result,
               tppq.user_answer,
               tppq.question_no,
               tpq.question_type,
               tpq.content question_content,
               tqa.id AS   answer_id,
               tqa.answer,
               tqa.content answer_content,
               rqa.answer formula_answer,
               raf.formula_template formula_template
        FROM reading_personal_passages tpp
                 LEFT JOIN reading_passages tp ON tpp.passage_id = tp.id
                 LEFT JOIN reading_personal_passage_questions tppq ON tppq.personal_passage_id = tpp.id
                 LEFT JOIN reading_passage_questions tpq ON tppq.question_id = tpq.id
                 LEFT JOIN reading_question_answers tqa ON tqa.question_id = tpq.id
            AND tqa.deleted_at IS NULL
            AND tqa.answering_formula_id IS NULL
                 LEFT JOIN reading_question_answers rqa ON rqa.question_id = tpq.id
            AND rqa.deleted_at is null
            AND rqa.answering_formula_id is not null
                 LEFT JOIN reading_answering_formulas raf ON raf.id = rqa.answering_formula_id
        WHERE tpp.id = #{id}
        ORDER BY tppq.question_no ASC NULLS LAST, tppq.id ASC
    </select>

    <!--    错题本-分页 -->
    <select id="pagesOfErrorBook" resultType="com.joinus.study.model.vo.ReadingErrorBookVO">
        SELECT
            tpp.id,
            tp.title,
            tpp.knowledge_points,
            tpp.created_at,
            tppq.question_type,
            tppq.accuracy,
            tpp.passage_id
        FROM reading_personal_passages tpp
                 LEFT JOIN reading_personal_user tpu ON tpp.student_id = tpu.student_id
                 LEFT JOIN reading_passages tp ON tpp.passage_id = tp.id
                 LEFT JOIN (SELECT personal_passage_id,
                                   string_agg(question_type, ',') AS question_type,
                                   sum(result) correct_number,
                                   count(id) - sum(result) AS error_number,
                                   TO_CHAR(sum(result)::NUMERIC * 100 /  NULLIF(count(id), 0),'FM999999990%') AS accuracy
                            FROM reading_personal_passage_questions
                            GROUP BY personal_passage_id) tppq ON tppq.personal_passage_id = tpp.id
        WHERE tpp.deleted_at IS NULL AND tpp.is_discard = 0
          AND tppq.error_number > 0
        <if test="null != pageParam.studentId">
            AND tpu.student_id = #{pageParam.studentId}
        </if>
        <if test="null != pageParam.questionType and '' != pageParam.questionType">
            AND tppq.question_type ILIKE concat('%',#{pageParam.questionType},'%')
        </if>
        <if test="null != pageParam.knowledgePoints and '' != pageParam.knowledgePoints">
            AND tpp.knowledge_points ILIKE concat('%',#{pageParam.knowledgePoints},'%')
        </if>
        ORDER BY tpp.end_at DESC NULLS LAST, tpp.id DESC
    </select>
    <!--    学生训练列表 -->
    <select id="studentPersonalList" resultType="com.joinus.study.model.vo.StudentReadingPersonalPassagesVo">
        SELECT
        pp.*,
        pp.end_at as endTime,
        CASE WHEN pp.pdf_url IS NOT NULL THEN 1  ELSE 0  END AS print_status,
        pp.is_discard,
        pr.accuracy_rate ,
        p.title,
        p.unit_id,
        p."content",
        case when pp.entry_type = 1 or pp.entry_type = 3 then rpp.plan_name
        else ra.name end as planName,
        pp.entry_type,
        pp.plan_id as planId
        FROM
        reading_personal_passages pp
        INNER JOIN reading_passages p ON pp.passage_id = p.id
        LEFT JOIN reading_personal_analysis_report pr ON pr.personal_passage_id = pp.id
        LEFT JOIN reading_personal_plan rpp ON rpp.id = pp.plan_id and pp.entry_type in (1, 3)
        left join reading_activity ra on ra.id = pp.plan_id and pp.entry_type in (4, 5)
        where p.deleted_at IS NULL and pp.deleted_at IS NULL
        <if test="param.studentId != null">
            AND pp.student_id = #{param.studentId}
        </if>
        <if test="param.id != null">
            AND pp.id = #{param.id}
        </if>
        <choose>
            <when test="param.status == 1">
                AND pp.status IN (0, 1)
            </when>
            <when test="param.status == 2">
                AND pp.status >= 2
            </when>
        </choose>
        <if test="param.planId != null">
            AND pp.plan_id = #{param.planId}
        </if>
        order by pp.id desc
    </select>

    <select id="queryPersonalPassagesQuestionList"
            resultType="com.joinus.study.model.vo.ReadingPassageQuestionsAnswersVO">
        SELECT PP.ID                                "personalPassageId",
               PPQ.ID                               "personalPassageQuestionId",
               PPQ.QUESTION_TYPE,
               PQ.CONTENT,
               PQ.ORDER_NO,
               QA.ANSWER,
               QA.CONTENT                           "answerContent",
               PPQ.USER_ANSWER,
               COALESCE(PPQ.is_error_correction, 0) "isErrorCorrection",
               COALESCE(PPQ.is_feedback, 0)         "isFeedback",
               PPQ.RESULT,
               rqa.ANSWER                            "formulaAnswer",
               raf.FORMULA_TEMPLATE                  "formulaTemplate",
                PPQ.answer_advice                   "answerAdvice",
                PPQ.reference_Answer                 "referenceAnswer"
        FROM READING_PERSONAL_PASSAGES PP
                 INNER JOIN READING_PERSONAL_PASSAGE_QUESTIONS PPQ ON PP.ID = PPQ.PERSONAL_PASSAGE_ID
            AND PPQ.DELETED_AT IS NULL
                 INNER JOIN READING_PASSAGE_QUESTIONS PQ ON PPQ.QUESTION_ID = PQ.ID
                 LEFT JOIN READING_QUESTION_ANSWERS QA ON QA.QUESTION_ID = PQ.ID
            AND QA.deleted_at IS NULL
            AND QA.answering_formula_id IS NULL
                 left join reading_question_answers rqa on rqa.question_id = PQ.id
            AND rqa.deleted_at is null
            AND rqa.answering_formula_id is not null
                 left join reading_answering_formulas raf on raf.id = rqa.answering_formula_id
                 INNER JOIN READING_PASSAGES P ON P.ID = PP.PASSAGE_ID
        WHERE PP.ID = #{id}
          AND PP.deleted_at IS NULL
        order by PPQ.question_no
    </select>
    <select id="listStudentsWithTrainingRecords" resultType="java.lang.Long">
        SELECT
            DISTINCT student_id
        FROM
            reading_personal_passages
        WHERE
            created_at::date = #{date}::date
            AND status = 3
            AND deleted_at IS NULL
    </select>
    <select id="selectReadingPersonalPassagesVoList"
            resultType="com.joinus.study.model.vo.ReadingPersonalPassagesVo">
        select
        pp.*,
        tp.title,
        tp.genre,
        tp.unit_id,
        tp.content
        FROM reading_personal_passages pp
        INNER JOIN reading_passages tp ON pp.passage_id = tp.id
        WHERE pp.id is not null and pp.deleted_at IS NULL
        <if test="param.studentId != null">
            AND pp.student_id = #{param.studentId}
        </if>
        <if test="param.id != null">
            AND pp.id = #{param.id}
        </if>
        <choose>
            <when test="param.status == 1">
                AND pp.status IN (0, 1) and pp.is_discard = 0
            </when>
            <when test="param.status == 2">
                AND pp.status >= 2
            </when>
        </choose>
        <if test="param.planId != null">
            AND pp.plan_id = #{param.planId}
        </if>
        <if test="param.unitId != null">
            AND tp.unit_id = #{param.unitId}
        </if>
    </select>
    <select id="getPassageInfoByPersonalPassagesId" resultType="com.joinus.study.model.entity.ReadingPassages">
        select
        tp.id,
        tp.title,
        tp.unit_id
        FROM reading_personal_passages tpp
             INNER JOIN reading_passages tp ON tpp.passage_id = tp.id
        WHERE tpp.id = #{id}
    </select>
    <select id="queryStudentWeekKnowledgePointList"
            resultType="com.joinus.study.model.vo.ReadingWeekKnowledgePointVo">
        SELECT
            kp.id,
            kp.name,
            COUNT(DISTINCT ppq.id) AS "totalQuestions",
            COUNT(DISTINCT CASE WHEN ppq.result = 1 THEN ppq.id END) AS "correctCount",
            COUNT(DISTINCT CASE WHEN ppq.result <![CDATA[ < ]]> 1 THEN ppq.id END) AS "incorrectCount",
            ROUND(1 - sum(ppq.result) / NULLIF(COUNT(ppq.id), 0), 2) as errorRate
        FROM
            reading_personal_passages pp
                INNER JOIN reading_personal_passage_questions ppq
                           ON pp.id = ppq.personal_passage_id
                               AND ppq.deleted_at IS NULL
                INNER JOIN reading_passages p
                           ON p.id = pp.passage_id
                INNER JOIN reading_question_knowledge_points qkp
                           ON qkp.question_id = ppq.question_id
                INNER JOIN reading_knowledge_points kp
                           ON kp.id = qkp.knowledge_point_id
        WHERE
            pp.deleted_at IS NULL
            AND pp.status = 3
            AND pp.student_id = #{studentId}
            <if test="excludePersonalPassageId != null">
                AND pp.id != #{excludePersonalPassageId}
            </if>
        GROUP BY
            kp.id, kp.name ,kp.created_at
        HAVING
            ROUND(1 - sum(ppq.result) / NULLIF(COUNT(ppq.id), 0), 2) > 0.2
        ORDER BY
            errorRate desc, kp.created_at desc
    </select>

    <select id="getPersonalPassagesById" resultType="com.joinus.study.model.vo.ReadingPersonalPassagesVo">
        SELECT rpp."id"   AS id,
               rp."id"    AS passageId,
               rp.title   AS title,
               rp.content AS content,
               rp.genre   AS genre,
               rp.unit_id AS unitId,
               rpp.plan_id AS planId,
               rpp.entry_type AS entryType,
               rpp.student_id AS studentId
        FROM reading_personal_passages rpp
                 inner join reading_passages rp ON rp."id" = rpp.passage_id
        WHERE rpp.ID = #{id}
    </select>

    <select id="getPersonalPassageQuestionList" resultType="com.joinus.study.model.vo.ReadingPersonalPassageQuestionVo">
        SELECT rppq."id"                AS id,
               rpq.passage_id           AS passageId,
               rppq.question_id         AS questionId,
               rppq.question_type       AS questionType,
               rppq.user_answer         AS userAnswer,
               rppq.is_feedback         AS isFeedback,
               rppq.is_error_correction AS isErrorCorrection,
               rppq.result              AS result,
               rqa.content              AS answerContent,
               rppq.answer_advice        as answerAdvice,
               rppq.reference_Answer    as referenceAnswer,
               (rqa.answer :: JSONB) ->> '答案' AS answer,
               (rpq.CONTENT :: JSONB) ->> '问题' AS questionContent,
               (rpq.CONTENT :: JSONB) ->> '选项' AS optionStr,
               (rqas.answer :: JSONB) ->> '答案' AS formulaAnswer,
               raf.formula_template AS formulaTemplate
        FROM
            reading_personal_passage_questions rppq
            INNER JOIN reading_passage_questions rpq
        ON rpq."id" = rppq.question_id
            LEFT JOIN reading_question_answers rqa ON rqa.question_id = rpq."id"
            AND rqa.deleted_at IS NULL
            AND rqa.answering_formula_id IS NULL
            left join reading_question_answers rqas on rqas.question_id = rpq."id"
            AND rqas.deleted_at is null
            AND rqas.answering_formula_id is not null
            left join reading_answering_formulas raf on raf.id = rqas.answering_formula_id
        WHERE
            rppq.personal_passage_id = #{id}
        order by rppq.question_no
    </select>
    <select id="getAccuracyRateByParam" resultType="java.math.BigDecimal">
        SELECT
            ROUND(sum(ppq.result) / NULLIF(COUNT(ppq.id), 0), 2) AS accuracyRate
        FROM
            reading_personal_passages pp
            inner join reading_personal_passage_questions ppq on pp.id = ppq.personal_passage_id
        WHERE
            pp.deleted_at is null
            and pp.status = 3
            <if test="startDate != null">
                and pp.end_at::date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and pp.end_at::date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="studentId != null">
                and pp.student_id = #{studentId}
            </if>
            <if test="personalPassageId != null">
                and pp.id = #{personalPassageId}
            </if>
    </select>

    <!--获取学生假期训练上次练习类型-->
    <select id="getActivityLastPractice" resultType="com.joinus.study.model.dto.ReadingActivityLastPracticeDto">
        SELECT rpp.id         AS personalPassagesId,
               rpp.entry_type AS entryType,
               rpp.created_at::date AS createdDate
        FROM reading_personal_passages rpp
        WHERE rpp.deleted_at IS NULL
          AND rpp.is_discard = 0
          AND rpp.status = 3
          AND rpp.entry_type IN (4, 5)
          AND rpp.plan_id = #{planId}
          AND rpp.student_id = #{studentId}
          AND rpp.created_at::date <![CDATA[ < ]]> CURRENT_DATE
        ORDER BY rpp.created_at DESC
    </select>

    <!--[假期训练]获取当日未完成练习记录-->
    <select id="getPendingPersonalPassageIds" resultType="java.lang.Long">
        SELECT rp.ID
        FROM reading_personal_passages rp
        WHERE rp.deleted_at IS NULL
          AND rp.is_discard = 0
          AND rp.entry_type IN (4, 5)
          AND rp.status <![CDATA[ < ]]> 2
          AND rp.student_id = #{ param.studentId }
          AND rp.plan_id = #{ param.planId }
          AND rp.created_at::date = #{localDate}
        ORDER BY RANDOM()
            LIMIT 1
    </select>

    <!--[假期训练]获取学生假期训练上次训练错误知识点-->
    <select id="getActivityStudentWeakKnowledgePoint" resultType="com.joinus.study.model.vo.ReadingWeekKnowledgePointVo">
        SELECT kp.id,
               kp.name,
               COUNT(DISTINCT ppq.id)                                   AS "totalQuestions",
               COUNT(DISTINCT CASE WHEN ppq.result = 1 THEN ppq.id END) AS "correctCount",
               COUNT(DISTINCT CASE WHEN ppq.result <![CDATA[ < ]]> 1 THEN ppq.id END) AS "incorrectCount",
               ROUND(1 - sum(ppq.result) / NULLIF(COUNT(ppq.id), 0), 2) as errorRate
        FROM reading_personal_passages pp
                 INNER JOIN reading_personal_passage_questions ppq
                            ON pp.id = ppq.personal_passage_id
                                AND ppq.deleted_at IS NULL
                 INNER JOIN reading_passages p
                            ON p.id = pp.passage_id
                 INNER JOIN reading_question_knowledge_points qkp
                            ON qkp.question_id = ppq.question_id
                 INNER JOIN reading_knowledge_points kp
                            ON kp.id = qkp.knowledge_point_id
        WHERE pp.deleted_at IS NULL
          AND pp.is_discard = 0
          AND pp.status = 3
          AND pp.id IN
          <foreach collection="personalPassageIds" item="id" index="index" open="(" close=")" separator=",">
              #{id}
          </foreach>
        GROUP BY kp.id, kp.name, kp.created_at
        HAVING ROUND(1 - sum(ppq.result) / NULLIF(COUNT(ppq.id), 0), 2) > 0.2
        ORDER BY errorRate desc, kp.created_at desc
    </select>
    <select id="getPersistDays" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT pp.end_at::date) AS date_count
        FROM
            reading_personal_passages pp
        WHERE
            pp.deleted_at is null
            and pp.status = 3
            and pp.student_id = #{studentId}
    </select>

    <select id="getAllTimeSpent" resultType="java.lang.Integer">
        select
            sum(pp.time_spent) as "timeSpent"
        from
            reading_personal_passages pp
        where
            pp.deleted_at is null
            and pp.status = 3
            <if test="startDate != null">
                and pp.end_at::date <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                and pp.end_at::date <![CDATA[ <= ]]> #{endDate}
            </if>
            <if test="studentId != null">
                and pp.student_id = #{studentId}
            </if>
    </select>
    <select id="getPrintTimes" resultType="java.lang.Integer">

        SELECT COUNT(*) AS printTimes
        FROM (
                 SELECT passage_id, question_ids
                 FROM reading_personal_print
                 WHERE student_id = #{studentId}
                 AND created_at &gt;= date_trunc('month', CURRENT_DATE)
                 AND created_at &lt; date_trunc('month', CURRENT_DATE) + INTERVAL '1 month'
                 GROUP BY passage_id, question_ids
             ) AS grouped_entries

    </select>
    <select id="getCountByStudentId" resultType="com.joinus.study.model.vo.ReadingPersonalUserVO">
        SELECT
            (SELECT COUNT(*) FROM reading_personal_passages WHERE student_id = #{studentId} AND entry_type = 1) AS readingTrainingCampCount,
            (SELECT COUNT(*) FROM reading_personal_passages WHERE student_id = #{studentId} AND entry_type = 2) AS directionalBlastCount,
            (SELECT COUNT(*) FROM reading_personal_passages WHERE student_id = #{studentId} AND entry_type in (4, 5)) AS summerTrainingCampCount
    </select>
    <select id="getSummerTrainingCampDays" resultType="java.lang.Integer">

        SELECT COUNT(*) AS training_days
        FROM (
                 SELECT DISTINCT DATE(created_at)
            FROM reading_personal_passages
        WHERE student_id = #{studentId} AND  entry_type in (4, 5)
        GROUP BY DATE(created_at)
            ) AS unique_days;

    </select>

    <select id="getWeekReadingTimeSpent" resultType="com.joinus.study.model.vo.ReadingWeekTimeStatisticsVO">
        SELECT
            '0' AS "maxMinFlag",
            updated_at::date AS "readingDate",
            SUM(time_spent) AS "totalTimeSpent"
        FROM
            reading_personal_passages
        WHERE status = 3 AND is_discard = 0
          AND entry_type in (1,2)
          AND student_id = #{studentId}
          AND updated_at::date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY updated_at::date
        ORDER BY updated_at::date
    </select>

    <select id="getWeekReadingGrowthCycle" resultType="com.joinus.study.model.vo.ReadingGrowthCycleVO">
        SELECT rpp.updated_at::date AS "readingDate",
            COUNT(DISTINCT rpp.id) AS "readingTestSetCount",
            COUNT(rppq.id) AS "totalQuestions",
            SUM(CASE WHEN rppq.result = 1 THEN 1 ELSE 0 END) AS "correctCount",
            SUM(CASE WHEN rppq.result = 0 THEN 1 ELSE 0 END) AS "incorrectCount"
        FROM reading_personal_passages rpp
                 INNER JOIN reading_personal_passage_questions rppq ON rpp.id = rppq.personal_passage_id
        WHERE
            rpp.student_id = #{studentId} AND rpp.status = 3 AND rpp.is_discard = 0 AND rpp.entry_type in (1,2)
          AND rpp.updated_at::date BETWEEN #{startDate}::date AND #{endDate}::date
        GROUP BY rpp.updated_at::date
        ORDER BY rpp.updated_at::date
    </select>
</mapper>
