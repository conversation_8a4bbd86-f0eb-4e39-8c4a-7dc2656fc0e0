<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingPersonalPlanMapper">

    <select id="planPages" resultType="com.joinus.study.model.vo.ReadingPersonalPlanVo">
        select rpp.*,rpp.weak_knowledge_point -> 'pointsDtoList' as pointsDtoList from reading_personal_plan rpp
        where 1=1
        <if test="null != pageParam.studentId">
            AND rpp.student_id = #{pageParam.studentId}
        </if>
        order by rpp.created_at desc
    </select>

    <select id="getKnowledgePointsNames" resultType="java.util.Map">
        select id,name
        from reading_knowledge_points
        where id in
        <foreach collection="knowledgePointIds" item="id" open="(" separator="," close=")">
            #{id}::uuid
        </foreach>
    </select>
    <select id="checkPlanByStudentIdAndDate" resultType="java.lang.Boolean">
        SELECT count(1) > 0
        FROM reading_personal_plan rpp
        WHERE student_id = #{studentId} and (
            (#{startDate} BETWEEN rpp.start_date AND rpp.end_date OR #{endDate} BETWEEN rpp.start_date AND rpp.end_date)
                OR (rpp.start_date BETWEEN #{startDate} AND #{endDate} OR rpp.end_date BETWEEN #{startDate} AND #{endDate})
            )
    </select>
    <select id="getKnowledgePointsInfos" resultType="java.util.Map">
        select id,name,content
        from reading_knowledge_points
    </select>

    <select id="queryPlanStartRemindersList" resultType="com.joinus.study.model.entity.ReadingPersonalPlan">
        SELECT
            id,
            max(plan_name) AS planName,
            student_id AS studentId
        FROM reading_personal_plan
        WHERE
        start_date <![CDATA[ <= ]]> now()::date
        AND end_date <![CDATA[ >= ]]> now()::date
        AND deleted_at is null
        group by id, student_id
    </select>
    <select id="planUnderWayList" resultType="com.joinus.study.model.entity.ReadingPersonalPlan">
        select rpp.*
        from reading_personal_plan rpp
        where rpp.student_id = #{studentId}
          and rpp.end_date >= current_date
        order by ABS(EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - start_date))) ASC limit 3
    </select>
    <select id="selectPassagesByPlanId" resultType="java.lang.Integer">
        select count(pass.*) from reading_personal_passages pass where pass.plan_id = #{planId} and pass.status in (2,3)
    </select>
    <select id="getPlanInfoByStudentId" resultType="com.joinus.study.model.entity.ReadingPersonalPlan">
        select * from reading_personal_plan where student_id = #{studentId} and current_date BETWEEN start_date AND end_date limit 1
    </select>
    <select id="getKnowledgePointsInfoGroupByType" resultType="java.util.Map">
        SELECT d.genre,a.id,a.name
        from public.reading_knowledge_points a JOIN (
            SELECT
                b.genre ,
                UNNEST(string_to_array(b.knowledge_point_id, ',')) AS kp_id
            FROM
                public.reading_unit_genres b
                    JOIN
                public.reading_units c ON c.id = b.unit_id
            WHERE
                c.grade = #{grade} AND c.semester = #{semester}) d ON CAST(d.kp_id AS uuid) = a.id
        group by genre,a.id,a.name
        order by genre,a.id
    </select>
</mapper>
