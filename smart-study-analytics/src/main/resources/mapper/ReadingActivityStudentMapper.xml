<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ReadingActivityStudentMapper">


    <select id="getStudentJoinActivityInfo" resultType="com.joinus.study.model.entity.ReadingActivityStudent">
        select ras.*
        from reading_activity_student ras
        inner join reading_activity ra on ra.id = ras.activity_id
        where ra.name='暑期训练营活动'
          and ras.student_id=#{studentId}
    </select>
    <select id="getJoinTrainingCampCount" resultType="java.lang.Integer">
        select  count(DISTINCT(student_id)) from  reading_activity_student WHERE activity_id=1 AND deleted_at is null
    </select>
</mapper>
