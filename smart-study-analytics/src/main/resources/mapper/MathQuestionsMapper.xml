<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathQuestionsMapper">

    <!--    根据questionId从学习反馈中获取最受欢迎的答案-->
    <select id="selectAnswersByQuestionId" resultType="com.joinus.study.model.dto.QuestionAnswerDto">
        select mqa.answer, mqa.content
        from math_question_answers qar,
             math_answers mqa
        where qar.question_id = #{questionId}
          and qar.answer_id = mqa.id
        order by created_at desc
        limit  1
    </select>
    <select id="selectAnswerIdByRequestIdForFeedback" resultType="com.joinus.study.model.dto.AnswerIdForFeedback">
        SELECT qaf.question_answer_id,
               SUM(CASE WHEN qaf.type = 'upvote_count' THEN 1 ELSE 0 END) AS upvote_count
        FROM question_answer_feedback qaf
        WHERE qaf.question_id = #{questionId}
        and qaf.question_answer_id is not null
        GROUP BY qaf.question_answer_id
        ORDER BY upvote_count DESC
        LIMIT 1
    </select>

    <select id="selectAnswersById" resultType="com.joinus.study.model.dto.QuestionAnswerDto">
        select mqa.answer,mqa.content from math_answers mqa where mqa.id = #{answerId}
    </select>

    <select id="selectQuestionInfo" resultType="com.joinus.study.model.vo.FlexiblyGeneratedVo">
        select mq.id as "questionId",
               mq.difficulty as "questionDif",
               mq.question_type as "questionType",
               mq.content as "questionContent",
               mqa.answer as "questionAnswer",
               mqa.content as "questionAnswerContent",
               f.oss_url as "questionOssUrl",
               string_agg(distinct concat(qkp.knowledge_point_id, ':',mkp.name), ',') as "knowledgePoints"
        from public.math_questions mq
                 left join public.math_question_answers qar on qar.question_id = mq.id
                 left join public.math_answers mqa on mqa.id = qar.answer_id
                 left join math_question_files qf on qf.question_id = mq.id
                 left join files f on f.id = qf.file_id
                 left join math_knowledge_point_questions qkp on qkp.question_id = mq.id
                 left join math_knowledge_points mkp on qkp.knowledge_point_id = mkp.id
        where mq.id = #{questionId}::uuid
        group by mq.id, mq.difficulty, mq.question_type, mq.content, mqa.answer, mqa.content, f.oss_url, mq.created_at
        order by mq.created_at desc
    </select>

    <select id="selectPastExamQuestionDto" resultType="com.joinus.study.model.dto.PastExamQuestionDto">
        SELECT
        id AS "examId",
        year AS "pastExamPaperYear",
        region AS "pastExamPaperRegion",
        CASE WHEN region IS NOT NULL THEN true ELSE false END AS "isPastExamPaper"
        FROM math_exams
        WHERE deleted_at IS NULL  and region is not  null
        <choose>
            <!-- 优先使用 examId -->
            <when test="examId != null">
                AND id = CAST(#{examId} AS UUID)
            </when>
            <!-- 当没有 examId 时使用 questionId -->
            <when test="questionId != null">
                AND id IN (
                SELECT exam_id
                FROM math_exam_questions
                WHERE deleted_at IS NULL
                AND question_id = CAST(#{questionId} AS UUID)
                )
            </when>
        </choose>
    </select>
</mapper>
