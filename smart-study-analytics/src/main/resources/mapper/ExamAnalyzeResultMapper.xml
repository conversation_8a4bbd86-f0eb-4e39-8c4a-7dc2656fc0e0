<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.ExamAnalyzeResultMapper">

    <select id="insertExamAnalyzeResult" parameterType="com.joinus.study.model.entity.ExamAnalyzeResult" resultType="long">
        INSERT INTO exam_analyze_result
        (exam_id,
         student_id,
         overall_score,
         correct_rate,
         percentile,
         total_knowledge_points,
         mastered_knowledge_points,
         weak_knowledge_points,
         result,
         personal_exam_id,
         parent_id)
        VALUES (#{entity.examId}::uuid,
                #{entity.studentId},
                #{entity.overallScore}::overall_score_enum,
                #{entity.correctRate}::jsonb,
                #{entity.percentile},
                #{entity.totalKnowledgePoints},
                #{entity.masteredKnowledgePoints},
                #{entity.weakKnowledgePoints},
                #{entity.result}::exam_analyze_result_result_enum,
                #{entity.personalExamId},
                #{entity.parentId})
        RETURNING id
    </select>


    <select id="calculatePercentile" resultType="java.math.BigDecimal">
        WITH student_class AS (
        -- 获取指定学生的班级ID
        SELECT class_id
        FROM view_active_students
        WHERE student_id = #{studentId}
        ),
        latest_results AS (
        SELECT DISTINCT ON (ear.student_id)
        ear.student_id,
        ear.correct_rate,
        vas.class_id,
        ear.created_at
        FROM exam_analyze_result ear
        JOIN view_active_students vas ON ear.student_id = vas.student_id
        WHERE ear.exam_id = #{examId}
        AND ear.deleted_at IS NULL
        AND ear.student_id != #{studentId}
        AND vas.class_id = (SELECT class_id FROM student_class)
        ORDER BY ear.student_id, ear.created_at DESC
        )
        SELECT
        COALESCE(
        ROUND(
        COUNT(CASE
        WHEN CAST(REGEXP_REPLACE(lr.correct_rate ->> 'percentile', '[^0-9.]', '', 'g') AS DECIMAL(10, 2)) &lt;= CAST(#{percentile} AS DECIMAL(10, 2))
        THEN 1
        END) * 100.0 /
        NULLIF(COUNT(*), 0),
        100
        ),
        100
        ) AS percentile
        FROM latest_results lr
    </select>

    <select id="selectExamAnalyzeResultList" resultType="com.joinus.study.model.vo.ExamAnalyzeResultVo">
        SELECT ear.id AS "examAnalyzeResultId",
               COALESCE(met.value, me.name) AS "examName",
               ear.exam_id AS "examId",
               ear.result AS "result",
               TO_CHAR(ear.created_at, 'YYYY/MM/DD HH24:MI') AS "createdAt",
               COUNT(DISTINCT peq.question_id) AS "questionCount"
        FROM public.exam_analyze_result ear
                 INNER JOIN public.math_exams me ON me.id = ear.exam_id and me.source in ('用户上传','常规考试卷')
                 left join math_exam_tags met on met.exam_id = ear.exam_id and met.type = 'ALIAS'
                                                     and met.deleted_at is null and (met.properties->>'isPrimary')::boolean = true
                 left JOIN public.personal_exam pe ON pe.student_id = ear.student_id AND pe.exam_id = ear.exam_id
                 left JOIN public.personal_exam_question peq ON peq.personal_exam_id = pe.id
        WHERE ear.student_id = #{studentId} and ear.deleted_at is null
        GROUP BY ear.id, me.name, met.value, ear.exam_id, ear.created_at, ear.result
        ORDER BY ear.created_at DESC
    </select>
    <select id="selectSpecializedExamAnalyzeResultList" resultType="com.joinus.study.model.vo.ExamAnalyzeResultVo">
        WITH LatestPersonalExam AS (
            SELECT DISTINCT ON (pe.student_id, pe.exam_id)
            pe.*
        FROM personal_exam pe
        WHERE pe.student_id = #{studentId}
        ORDER BY pe.student_id, pe.exam_id, pe.created_at DESC
            ),
            LatestAnalyzeResult AS (
        SELECT DISTINCT ON (ear.exam_id, ear.student_id) ear.*
        FROM exam_analyze_result ear
        WHERE ear.deleted_at IS NULL
        ORDER BY ear.exam_id, ear.student_id, ear.created_at DESC
            ),
            ExamData AS (
        SELECT
            me.id AS exam_id,
            me.name AS exam_name,
            me.source,
            me.created_at,
            (  -- 提前聚合问题数量
            SELECT COUNT(DISTINCT peq.question_id)
            FROM public.math_exam_questions peq
            WHERE peq.exam_id = me.id
            ) AS question_count,
            (  -- 直接获取首个文件URL
            SELECT f.oss_url
            FROM public.math_exam_files mef
            JOIN public.files f ON f.id = mef.file_id
            WHERE mef.exam_id = me.id
            ORDER BY mef.sort_no ASC
            LIMIT 1
            ) AS oss_url
        FROM public.math_exams me
        WHERE me.source IN ('专项训练', '考点盲区训练', '暑期训练')
            )
        SELECT
            ear.id AS "examAnalyzeResultId",
            COALESCE(met.value, ed.exam_name) AS "examName",
            ed.exam_id AS "examId",
            ed.oss_url AS "ossUrl",
            ear.result AS "result",
            TO_CHAR(ed.created_at, 'YYYY/MM/DD HH24:MI') AS "createdAt",
            ed.question_count AS "questionCount",
            ed.source,
            lpe.id AS personalExamId
        FROM LatestPersonalExam lpe
                 JOIN ExamData ed ON ed.exam_id = lpe.exam_id
                 left join math_exam_tags met on met.exam_id = lpe.exam_id and met.type = 'ALIAS'
                                                     and met.deleted_at is null and (met.properties->>'isPrimary')::boolean = true
                 LEFT JOIN LatestAnalyzeResult ear
                           ON ear.exam_id = ed.exam_id
                               AND ear.student_id = lpe.student_id
        ORDER BY ed.created_at DESC
    </select>

    <select id="selectSpecializedExamAnalyzeResultListV2" resultType="com.joinus.study.model.vo.ExamAnalyzeResultVo">
        with latest_person_exam as (
            SELECT DISTINCT ON (pe.student_id, pe.exam_id)
            pe.*
        FROM personal_exam pe
            inner join math_exams me on pe.exam_id = me.id and me.deleted_at is null
        WHERE pe.student_id = #{studentId}
            and me.source in ('专项训练', '考点盲区训练', '暑期训练')
        ORDER BY pe.student_id, pe.exam_id, pe.created_at DESC
            ),
            latest_exam_analyze_result as (
        select distinct on (ear.exam_id, ear.student_id) ear.*
        from exam_analyze_result ear
            inner join latest_person_exam lpe on ear.student_id = lpe.id and ear.exam_id = lpe.exam_id
        where ear.deleted_at is null
          and ear.student_id = #{studentId}
        order by ear.exam_id, ear.student_id, ear.created_at desc
            ),
            exam_data as (
        select me.id as exam_id,
            me.source,
            me.created_at,
            count(distinct mq.id) as question_count
        from public.math_exams me
            inner join latest_person_exam lpe on me.id = lpe.exam_id
            left join public.math_exam_questions meq on me.id = meq.exam_id
            left join public.math_questions mq on meq.question_id = mq.id and mq.deleted_at is null
            and lpe.student_id = 1
        group by me.id,me.source,me.created_at
            )
        select lar.id                                       AS "examAnalyzeResultId",
               pe.exam_name                                 AS "examName",
               ed.exam_id                                   AS "examId",
               lar.result                                   AS "result",
               to_char(ed.created_at, 'YYYY/MM/DD HH24:MI') AS "createdAt",
               ed.question_count                            AS "questionCount",
               ed.source,
               pe.id                                        AS personalExamId
        from latest_person_exam pe
                 inner join exam_data ed on ed.exam_id = pe.exam_id
                 left join latest_exam_analyze_result lar on lar.exam_id = ed.exam_id and lar.student_id = pe.student_id
        order by ed.created_at desc
    </select>


    <select id="selectExamAnalyzeResultByExamId" resultType="com.joinus.study.model.vo.ExamAnalysisReportVo">
        SELECT
            me.ID AS exam_id,
            pe.id AS personal_exam_id,
            COALESCE(met.value, me.name) AS name,
            me.source,
            pe.publisher,
            aer.student_id,
            COUNT ( peq.question_id ) AS total_questions,
            aer.overall_score,
            aer.correct_rate AS correct_rate,
            aer.percentile,
            aer.total_knowledge_points,
            aer.mastered_knowledge_points,
            aer.weak_knowledge_points,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'MULTIPLE_CHOICE' THEN peq.question_id END ) AS multiple_choice_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'FILL_IN_THE_BLANK' THEN peq.question_id END ) AS fill_blank_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'PROBLEM_SOLVING' THEN peq.question_id END ) AS free_response_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'TRUE_FALSE' THEN peq.question_id END ) AS true_false_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'CALCULATION' THEN peq.question_id END ) AS calculation_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'APPLICATION' THEN peq.question_id END ) AS application_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'PROOF' THEN peq.question_id END ) AS proof_total,
            COUNT ( DISTINCT CASE WHEN peq.question_type = 'OTHER' THEN peq.question_id END ) AS other_total
        FROM
            exam_analyze_result aer
            LEFT JOIN math_exams me ON aer.exam_id = me.id
            LEFT JOIN personal_exam pe ON aer.personal_exam_id = pe.id
            LEFT JOIN personal_exam_question peq ON pe.id = peq.personal_exam_id
            LEFT join math_exam_tags met on met.exam_id = me.id and met.type = 'ALIAS'
                and met.deleted_at is null and (met.properties->>'isPrimary')::boolean = true

        WHERE
            aer.personal_exam_id = #{personalExamId}
        GROUP BY
            me.ID,
            pe.id,
            me.NAME,met.value,
            me.source,
            me.publisher,
            aer.student_id,
            aer.overall_score,
            aer.correct_rate,
            aer.percentile,
            aer.total_knowledge_points,
            aer.mastered_knowledge_points,
            aer.weak_knowledge_points
    </select>
    <select id="selectTotalStudentCountByExamId" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT pe.student_id)
        FROM
            view_active_students vas
                JOIN
            personal_exam pe ON vas.student_id = pe.student_id
        WHERE
                vas.class_id = (SELECT class_id FROM view_active_students WHERE student_id = #{studentId})
          AND pe.exam_id = #{examId}
        GROUP BY
            vas.class_id, vas.class_name;
    </select>
    <select id="getMasteredCount" resultType="com.joinus.study.model.vo.KnowledgePointStatisticsVo">
        SELECT
        COUNT(vas.student_id) totalQuestionCount,
        SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS totalCorrectCount
        FROM
            view_active_students vas
                JOIN
            personal_exam pe ON vas.student_id = pe.student_id
                JOIN
            personal_exam_question peq ON pe.id = peq.personal_exam_id
                JOIN
            question_knowledge_point qkp ON peq.question_id = qkp.question_id
        WHERE
            qkp.knowledge_point_id = #{knowledgePointId}
          AND qkp.exam_id = #{examId}
          AND vas.class_id = (SELECT class_id FROM view_active_students WHERE student_id = #{studentId})


    </select>
    <select id="getOssUrls" resultType="java.lang.String">
        SELECT
            f.oss_url AS "ossUrl",
            mef.sort_no
        FROM public.exam_analyze_result ear
                 INNER JOIN public.math_exams me on me.id = ear.exam_id
                 INNER JOIN public.math_exam_files mef on mef.exam_id = me.id
                 INNER JOIN public.files f on f.id = mef.file_id
        WHERE ear.id =#{id}
            and mef.type = 'ORIGINAL_PAPER'
        GROUP BY  mef.sort_no,  f.oss_url
        ORDER BY mef.sort_no ASC
    </select>
    <select id="selectTestPapersCount" resultType="java.lang.Integer">
        SELECT COUNT
                   ( DISTINCT student_id)
        FROM
            personal_exam
        WHERE
            exam_id =#{examId}
            and deleted_at is null
    </select>
    <select id="getStudentInfo" resultType="java.util.Map">
        SELECT
            school_name as "schoolName",
            student_id as "studentId",
            student_name as "studentName",
            class_id as "classId",
            class_name as "className",
            grade_name as "gradeName"
        from view_active_students
        WHERE
        student_id =#{studentId}
    </select>

    <select id="selectManualExamScopeSection" resultType="java.util.UUID">
        SELECT
            s ->> 'sectionId' AS section_id
        FROM
            exam_analyze_result e,
            LATERAL jsonb_array_elements(
                    (SELECT jsonb_agg(elements)
                     FROM (
                              SELECT jsonb_array_elements(chapters -> 'sections')
                              FROM jsonb_array_elements(e.manual_exam_scope #> '{publishers}') p(pub),
                                   LATERAL jsonb_array_elements(pub #> '{chapters}') chapters
                          ) t(elements))
                    ) s
        WHERE e.id = #{analyzeReportId}
          AND (s ->> 'select') IN ('true')
    </select>

    <select id="selectKnowledgePointByAnalyzeId" resultType="java.util.UUID">
        select distinct qkp.knowledge_point_id
        from personal_exam pe,
             exam_analyze_result ear,
             question_knowledge_point qkp
        where ear.id = #{analyzeReportId}
          and qkp.exam_id = pe.exam_id
          and pe.id = ear.personal_exam_id
    </select>
    <select id="getOssUrlString" resultType="java.lang.String">
        SELECT STRING_AGG(f.oss_url, ',' ORDER BY mef.sort_no ASC) AS oss_url
        FROM (SELECT exam_id, file_id, sort_no, type
              FROM public.math_exam_files
             ) mef
                 INNER JOIN public.files f ON f.id = mef.file_id
        where mef.type = 'ORIGINAL_PAPER'
          and mef.exam_id = #{examId}
        group by mef.exam_id
    </select>

</mapper>
