<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.study.mapper.MathActivityWeekUnitStudentMapper">
    <insert id="insertBatch">
        insert into math_activity_week_unit_student (activity_id,
                                                     week_id,
                                                     week_unit_id,
                                                     student_id,
                                                     activity_student_id,
                                                     finish_result,
                                                     unlocked,
                                                     created_at)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.activityId}, #{item.weekId}, #{item.weekUnitId}, #{item.studentId},#{item.activityStudentId}, 'NOT_FINISHED',#{item.unlocked}, now())
        </foreach>
    </insert>
    <update id="deleteByActivityIdStudentId">
        update math_activity_week_unit_student set deleted_at = now()
        where activity_id = #{activityId} and student_id = #{studentId}
    </update>
    <select id="selectMathActivityStudentStudyRecord"
            resultType="com.joinus.study.model.dto.MathActivityStudentRecord">
        SELECT maw.activity_id,
               mawus.student_id,
               maw.publisher,
               maw.name,
               maw.grade,
               maw.type,
               COUNT(DISTINCT mawu.chapter_id) AS totalChapter,
               COUNT(DISTINCT mawu.section_id) AS totalSection,
               COUNT(DISTINCT CASE WHEN mawus.finish_result = 'FINISHIED' and mawu.section_id is null THEN mawu.chapter_id END) AS completedChapter,
               COUNT(DISTINCT CASE WHEN mawus.finish_result = 'FINISHIED' AND mawu.chapter_id IS NULL THEN mawu.id END) AS completedBook,
               COUNT(DISTINCT CASE WHEN mawus.finish_result = 'FINISHIED' THEN mawu.section_id END) AS completedSection,
               COUNT(DISTINCT CASE WHEN mawus.finish_result = 'FINISHIED' THEN mkp.knowledge_point_id END) AS completedPoint
        FROM
            math_activity_week_unit_student mawus
                INNER JOIN math_activity_week_unit mawu  ON mawus.week_unit_id = mawu.id and mawu.deleted_at is null
                INNER JOIN math_activity_week maw ON mawu.week_id = maw.id AND maw.activity_id = mawus.activity_id  and maw.deleted_at is null
                LEFT JOIN math_section_knowledge_points mkp  ON mawu.section_id = mkp.section_id
        where mawus.deleted_at is null
          and mawus.activity_id = #{activityId}
          and mawus.student_id = #{studentId}
        GROUP BY maw.activity_id,
                 mawus.student_id,
                 maw.publisher,
                 maw.name,maw.type,
                 maw.grade
    </select>
    <select id="selectFinishedAtByStudentId" resultType="java.util.Date">
        select finished_at from math_activity_week_unit_student_history where
        student_id = #{studentId} and deleted_at is null and finished_at is not null
        order by finished_at desc
    </select>

    <select id="selectSummerPlansStatistics" resultType="java.util.Map">
        select aw.type,
               count(distinct awu.chapter_id)  as "chapterNum",
               count(distinct awu.section_id)  as "sectionNum",
               sum(mskp.knowledge_point_count) as "knowledgePointsNum",
               count(distinct aw.*) as "weekNum"
        from math_activity_week_unit_student awus
                 left join math_activity_week_unit awu on awus.week_unit_id = awu.id
                 left join math_activity_week aw on awu.week_id = aw.id
                 left join (select section_id, count(*) as knowledge_point_count
                            from math_section_knowledge_points
                            group by section_id) mskp on mskp.section_id = awu.section_id
        where awus.student_id = #{studentId}
          and awus.deleted_at is null
--         and aw.type = 'REVIEW'  -- and aw.type = 'PREVIEW'
          and awu.type = 'SECTION_TEST'
          and awus.activity_id = #{activityId}
        group by aw.type
    </select>
    <resultMap id="examSummerPlanListDto" type="com.joinus.study.model.dto.ExamSummerPlanListDto">
        <id property="chapterId" column="chapter_id" javaType="java.util.UUID" jdbcType="OTHER"/>
        <result property="chapterName" column="chapter_name" javaType="java.lang.String"/>
        <result property="bookId" column="book_id" javaType="java.util.UUID"/>
        <result property="publisher" column="publisher" javaType="java.lang.String"/>
        <result property="grade" column="grade" javaType="java.lang.Integer"/>
        <result property="weekType" column="week_type" javaType="java.lang.String"/>
        <result property="name" column="name" javaType="java.lang.String"/>
        <collection property="sectionList" ofType="com.joinus.study.model.dto.ExamSummerPlanListDto$SectionListDto" javaType="java.util.ArrayList">
            <id property="sectionId" column="section_id" javaType="java.util.UUID" jdbcType="OTHER"/>
            <result property="sectionName" column="section_name" javaType="java.lang.String"/>
            <result property="weekUnitType" column="week_unit_type" javaType="java.lang.String"/>
            <result property="weekUnitNo" column="week_unit_no" javaType="java.lang.Integer"/>
            <result property="weekUnitStudentId" column="week_unit_student_id" javaType="java.lang.Long"/>
            <result property="examAnalyzeResultId" column="exam_analyze_result_id" javaType="java.lang.Long"/>
            <result property="finishResult" column="finish_result" javaType="java.lang.String"/>
            <result property="unlocked" column="unlocked" javaType="java.lang.Boolean"/>
            <result property="knowledgePointCount" column="knowledge_point_count" javaType="java.lang.Integer"/>
            <result property="questionTypeCount" column="question_type_count" javaType="java.lang.Integer"/>
        </collection>
    </resultMap>
    <select id="selectStudentSummerPlans" resultMap="examSummerPlanListDto">
        select mawu.chapter_id,--章id
               mawu.chapter_name,--章名称
               aw.publisher, --教材
               aw.grade, --年级
               aw.type      as week_type, --REVIEW复习， PREVIEW预习
               aw.name,
               mawu.book_id,
               mawu.section_id,--小节id
               mawu.section_name,--小节名称
               mawu.type    as week_unit_type,--类型  SECTION_TEST，CHAPTER_TEST，COMPREHENSIVE_TEST
               mawu.sort_no as week_unit_no, --小节排序序号
               mawus.id     as week_unit_student_id,
               mawus.finish_result, --小节完成情况
               mawus.exam_analyze_result_id,
               mawus.unlocked,
               mskp.knowledge_point_count, --知识点个数
               msqt.question_type_count --题型个数
               -- 知识点，题型
        from math_activity_week aw
                 left join math_activity_week_unit mawu on aw.id = mawu.week_id
                 left join math_activity_week_unit_student mawus on mawu.id = mawus.week_unit_id
                 left join (select section_id, count(*) as knowledge_point_count
                            from math_section_knowledge_points
                            group by section_id) mskp on mskp.section_id = mawu.section_id
                 left join (select section_id, count(*) as question_type_count
                            from math_section_question_types
                            group by section_id) msqt on msqt.section_id = mawu.section_id
        where
            1=1
            <if test="weekSort != null">
                and aw.sort_no = #{weekSort}
            </if>
          and mawus.deleted_at is null
          and mawus.student_id = #{studentId}
          and aw.activity_id = #{activityId}
        group by aw.publisher, aw.grade, aw.type, mawu.chapter_id, mawu.chapter_name, mawu.type,mawu.section_id, mawu.sort_no,
                 mawu.section_name,mawus.finish_result,mskp.knowledge_point_count,msqt.question_type_count,mawu.book_id,mawus.id,
                 mawus.unlocked,mawus.exam_analyze_result_id,aw.name
        order by mawu.sort_no
    </select>
    <select id="selectSectionMasteryDegree" resultType="java.lang.Double">
        SELECT
            FLOOR(AVG(correct_rate)*100)/100 AS average_correct_rate
        FROM (
                 SELECT
                     correct_count::FLOAT / NULLIF(total_count, 0) AS correct_rate
                 FROM (
                          SELECT
                              qkp.knowledge_point_id,
                              SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS correct_count,
                              COUNT(qkp.id) AS total_count
                          FROM exam_analyze_result ear
                                   LEFT JOIN personal_exam_question peq ON ear.personal_exam_id = peq.personal_exam_id
                                   LEFT JOIN question_knowledge_point qkp ON peq.question_id = qkp.question_id AND qkp.exam_id = ear.exam_id
                          WHERE ear.student_id = #{studentId}
                            <if test="examAnalyzeResultId != null">
                                and ear.id = #{examAnalyzeResultId}
                            </if>
                            <if test="sectionId != null">
                                AND qkp.knowledge_point_id IN (
                                SELECT mskp.knowledge_point_id
                                FROM math_section_knowledge_points mskp
                                WHERE mskp.section_id = CAST(#{sectionId} AS UUID)
                                )
                            </if>
                          GROUP BY qkp.knowledge_point_id
                      ) AS subquery
             ) AS rates
    </select>
    <select id="selectIdByExamIdHistory" resultType="java.lang.Long">
        select mawus.id
        from math_activity_week_unit_student mawus
        where mawus.exam_id_history @> jsonb_build_array(#{examId})
          and mawus.student_id = #{studentId} limit 1
    </select>
    <select id="selectExamTypeByStudentAndExamId"
            resultType="com.joinus.study.model.entity.MathActivityWeekUnit">
        select mawu.* from math_activity_week_unit mawu
                               left join math_activity_week_unit_student mawus on mawu.id = mawus.week_unit_id
        where mawus.student_id = #{studentId} and mawus.exam_id_history @> jsonb_build_array(#{examId})
    </select>
    <select id="selectComprehensiveTestStatus" resultType="java.util.Map">
        select maw.type,mawu.sort_no,mawu.type,mawus.finish_result
        from math_activity_week maw
            left join math_activity_week_unit mawu on maw.id = mawu.week_id
            left join math_activity_week_unit_student mawus on mawu.id = mawus.week_unit_id
        where mawus.student_id = #{studentId}
          and mawus.deleted_at is null
          and maw.type = #{weekType}
          and mawu.type != 'COMPREHENSIVE_TEST'
  and mawus.finish_result = 'NOT_FINISHED'
    </select>
    <select id="getStudentCurrentWeekInfo" resultType="java.util.Map">
        SELECT
            maws.week_id as "weekId",
            MIN(sub.sort_no) AS "sortNo",
            COUNT(1) AS "sectionCount"
        FROM math_activity_week_unit_student maws
                 JOIN (
            SELECT
                maws.week_id,
                maw.sort_no
            FROM math_activity_week_unit_student maws
                     JOIN math_activity_week maw ON maws.week_id = maw.id and maw.deleted_at IS NULL
            WHERE
                maws.student_id = #{studentId} and maws.activity_id = #{activityId}
              AND maws.finish_result = 'NOT_FINISHED' and maws.deleted_at IS NULL
            ORDER BY maw.sort_no
                LIMIT 1
        ) sub ON maws.week_id = sub.week_id
        WHERE
            maws.student_id = #{studentId} and maws.activity_id = #{activityId} and maws.deleted_at IS NULL
        GROUP BY maws.week_id
    </select>

    <update id="updateExamIdHistoryById">
        UPDATE math_activity_week_unit_student
        SET exam_id_history = COALESCE(exam_id_history, '[]'::jsonb) || jsonb_build_array(#{examIdHistory})
        WHERE id = #{id}
    </update>

    <select id="getMathActivityWeekUnitByTrainingExamId"
            resultType="com.joinus.study.model.entity.MathActivityWeekUnit">
        select mawu.* from math_activity_week_unit mawu
                               inner join math_activity_week_unit_student mawus on mawu.id = mawus.week_unit_id
        where mawus.exam_id_history @> jsonb_build_array(#{examId})
    </select>

    <select id="selectExamTypeByExamIds" resultType="com.joinus.study.model.po.MathActivityWeekUnitPo">
        SELECT mawu.*,
               matched.exam_id AS exam_id
        FROM math_activity_week_unit mawu
        INNER JOIN math_activity_week_unit_student mawus ON mawu.id = mawus.week_unit_id,
                LATERAL (
                    SELECT elem AS exam_id
                    FROM jsonb_array_elements_text(mawus.exam_id_history) AS arr(elem)
                    WHERE elem IN (
                    <foreach collection="examIds" item="examId" separator=",">
                        #{examId}::text
                    </foreach>
                    )
                    ) matched
        WHERE mawus.exam_id_history ?| array[
        <foreach collection="examIds" item="examId" separator=",">
            #{examId}::text
        </foreach>
        ]
    </select>
</mapper>
