spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  application:
    name: basic-admin
  kafka:
    bootstrap-servers: ***************:9092
    consumer:
      properties:
        group:
          id: basic-hik
      enable-auto-commit: true
#    listener:
#      ack-mode: manual_immediate
  redis:
    host: ************
    port: 6379
    database: 0
    password: ijx967111

server:
  port: 8082
  servlet:
    context-path: /api/basic-hik

logging:
  level:
    com.joinus.admin.mapper: debug
    org.apache.kafka.clients: ERROR

apollo:
  bootstrap:
    enabled: true
    namespaces: application,basic.common.database
    eagerLoad:
      enabled: true

swagger:
  enable: true
hik:
  # 即中心管理服务（Central Management Service，可简称为CMS)
  cms-server-ip: ************
  cms-server-port: 7660

  das-server-ip: ************
  das-server-port: 7660

  # 流媒体服务（Stream Media Serivice，可简称为 SMS）
  sms-server-ip: ************
  sms-server-port: 7665
  sms-server-listen-ip: 0.0.0.0
  sms-server-liste-port: 7665

  sms-back-server-ip: ************
  sms-back-server-port: 8000
  sms-back-server-listen-ip: 0.0.0.0
  sms-back-server-listen-port: 8000

  voice-sms-server-ip: ************
  voice-sms-server-port: 7500
  voice-sms-server-listen-ip: 0.0.0.0
  voice-sms-server-listen-port: 7500

  # 报警管理服务（Alarm Management Service，可简称为 AMS）
  alarm-server-ip: ************
  alarm-server-tcp-port: 7663
  alarm-server-udp-port: 7662
  alarm-server-type: 2
  alarm-server-listen-ip: 0.0.0.0
  alarm-server-listen-tcp-port: 7663
  alarm-server-listen-udp-port: 7662

  # 存储服务（Storage Service，可简称为 SS）
  pic-server-ip: ************
  pic-server-port: 6011
  pic-server-type: 2
  pic-server-listen-ip: 0.0.0.0
  pic-server-listen-port: 6011
  pic-server-file-path: D:\Program Files\git_repo\snake-basic-business\hikPicPath\

  isup-key: hik967111
  event-info-print-type: file
