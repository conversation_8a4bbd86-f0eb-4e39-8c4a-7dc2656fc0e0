package com.joinus.hik.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HeartBeatBO implements Serializable {

    /*
     * 通道编号
     */
    private Integer loginId;
    /*
     * 设备编号
     */
    private String deviceId;

    /*
     * 设备最近一次的上线时间
     */
    private Date onlineTime;
    /*
     * 设备SN
     */
    private String sn;
}
