package com.joinus.hik.kafka;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.XmlUtil;
import com.joinus.common.utils.CommonUtils;
import com.joinus.hik.model.event.EventMsg;
import com.joinus.hik.model.event.SignInMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class KafkaProducer {


    @Value("${kafka.topic.hik-vision.event.record:public.aio-gateway.hik-vision.event.record}")
    private String hikRecordSignInTopic;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("#{'${push-event-type-list:5:75,5:105,5:76,5:1,5:9,2:1024,2:1031,3:1038}'.split(',')}")
    private List<String> pushEventTypeList;
    public void sendClientEventToServer(String message) {
        try {
            Document document = XmlUtil.parseXml(message);
            Element ppvspMessage = XmlUtil.getRootElement(document);
            Map<String, Object> xmlMap = XmlUtil.xmlToMap(ppvspMessage);
            //使用hutool将map转为EventMsg对象
            EventMsg eventMsg = BeanUtil.toBean(xmlMap, EventMsg.class);
            SignInMsg signInMsg = eventMsg.getParams();
            String eventType = signInMsg.getMajorType() + ":" + signInMsg.getMinorType();
            log.info("获取事件eventType:{}", eventType);
            if (pushEventTypeList.contains(eventType)) {
                log.info("sending client event of hik-vision to server {}", CommonUtils.cleanLineBreaks(message));
                this.kafkaTemplate.send(hikRecordSignInTopic, message);
            }else{
                log.warn("not sending client event of hik-vision to server message {}", CommonUtils.cleanLineBreaks(message));
            }
        } catch (Exception e) {
            log.error("sending client event of hik-vision to server error {}", CommonUtils.cleanLineBreaks(message), e);
            e.printStackTrace();
        }
    }

}
