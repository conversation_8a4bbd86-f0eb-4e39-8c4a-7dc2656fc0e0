package com.joinus.hik.config;

import com.joinus.hik.service.AlarmService.AlarmDemo;
import com.joinus.hik.service.CmsService.CmsDemo;
import com.joinus.hik.service.SsService.SsDemo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Scanner;

@Component
@Slf4j
public class HikVisionSdkInit implements ApplicationListener<ContextRefreshedEvent> {


    @Autowired
    private AlarmDemo alarmDemo;
    @Autowired
    private CmsDemo cmsDemo;
    @Autowired
    private SsDemo ssDemo;

    public static int lLoginID = -1;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        //初始化报警服务
        alarmDemo.eAlarm_Init();
        alarmDemo.startAlarmListen();

        //初始化注册服务
        cmsDemo.cMS_Init();
        cmsDemo.startCmsListen();


        //初始化存储服务
        ssDemo.eSS_Init();
        ssDemo.startSsListen();


        //停止监听释放SDK
//        if (CmsDemo.CmsHandle >= 0) {
//           log.info("停止注册CMS服务");
//            CmsDemo.hCEhomeCMS.NET_ECMS_StopListen(CmsDemo.CmsHandle);
//            CmsDemo.hCEhomeCMS.NET_ECMS_Fini();
//        }
//        if (AlarmDemo.AlarmHandle >= 0) {
//            log.info("停止报警Alarm服务");
//            AlarmDemo.hcEHomeAlarm.NET_EALARM_StopListen(AlarmDemo.AlarmHandle);
//            AlarmDemo.hcEHomeAlarm.NET_EALARM_Fini();
//        }
//        if (SsDemo.SsHandle >= 0) {
//            log.info("停止存储SS服务");
//            SsDemo.hCEhomeSS.NET_ESS_StopListen(SsDemo.SsHandle);
//            SsDemo.hCEhomeSS.NET_ESS_Fini();
//        }
        return;
    }
}
