package com.joinus.hik.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "hik")
@Data
public class HikProperties {

    /*
     * 注册服务器监听地址(服务器本地地址)
     */
    private String cmsServerIp;
    private String cmsServerPort;
    /*
     * DAS地址（此处的IP和端口与设备网页端设置的平台IP地址和端口一致）
     */
    private String dasServerIp;
    private String dasServerPort;
    /*
     * 取流服务器地址端口（公网对接填入公网地址和端口）
     */
    private String smsServerIp;
    private String smsServerPort;
    /*
     * 取流服务器监听地址端口(服务器本地地址)
     */
    private String smsServerListenIp;
    private String smsServerListenPort;
    /*
     * 回放流媒体服务器地址端口（公网对接填入公网地址和端口）
     */
    private String smsBackServerIp;
    private String smsBackServerPort;
    /*
     * 取流服务器监听地址端口(服务器本地地址)
     */
    private String smsBackServerListenIp;
    private String smsBackServerListenPort;
    /*
     * 语音流媒体服务器地址端口（公网对接填入公网地址和端口）
     */
    private String voiceSmsServerIp;
    private String voiceSmsServerPort;
    /*
     * 语音流媒体服务器监听地址端口(服务器本地地址)
     */
    private String voiceSmsServerListenIp;
    private String voiceSmsServerListenPort;
    /*
     * 报警服务器地址（公网对接填入公网地址和端口）
     */
    private String alarmServerIp;
    private String alarmServerTcpPort;
    private String alarmServerUdpPort;
    /*
     * 报警服务器类型：0- 只支持UDP协议上报，1- 支持UDP、TCP两种协议上报（ISUP4.0） 2-MQTT(ISUP5.0)
     * MQTT复用TCP的端口
     */
    private String alarmServerType;
    /*
     * 报警服务器监听地址端口
     */
    private String alarmServerListenIp;
    private String alarmServerListenTcpPort;
    private String alarmServerListenUdpPort;
    /*
     * 图片存储服务器地址：IUSP5.0版本接入端口建议固定为6011
     */
    private String picServerIp;
    private String picServerPort;
    /*
     * 图片存储服务器类型：0-Tomcat，1-VRB，2-云存储，3-KMS，4-ISUP5.0
     * ISUP5.0版本接入，选择存储服务器类型为云存储，其余版本根据设备类型不同，选择不同类型存储服务器，ISUP4.0版本接入门禁设备选择类型为KMS
     */
    private String picServerType;
    /*
     * 图片存储服务器监听地址端口
     */
    private String picServerListenIp;
    private String picServerListenPort;
    private String picServerFilePath;

    /*
     * ISUP5.0登录秘钥
     */
    private String isupKey;
    /*
     * 报警服务器打印事件模式 file=事件保存到文件中, console=事件打印在控制台上
     */
    private String eventInfoPrintType;
}