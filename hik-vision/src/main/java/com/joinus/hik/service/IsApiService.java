package com.joinus.hik.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.common.exception.BaseException;
import com.joinus.common.model.response.ApiResult;
import com.joinus.common.utils.CommonUtils;
import com.joinus.hik.common.ConfigFileUtil;
import com.joinus.hik.common.Constant;
import com.joinus.hik.config.IsupTest;
import com.joinus.hik.model.HeartBeatBO;
import com.joinus.hik.service.CmsService.CmsDemo;
import com.joinus.hik.service.CmsService.HCISUPCMS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IsApiService {

    @Autowired
    private CmsDemo cmsDemo;

    private int getLoginId(String deviceId) {
        HeartBeatBO heartBeatBO = CmsDemo.deviceHeartBeatMap.get(deviceId);
        if(ObjectUtil.isEmpty(heartBeatBO)){
            throw new BaseException("设备未注册");
        }
        return heartBeatBO.getLoginId();
    }

    public Object proxyRequest(HttpServletRequest request, String deviceId) throws Exception {
        int loginId = this.getLoginId(deviceId);

        String method = request.getMethod();
        String uri = StrUtil.replace(request.getRequestURI(), "/api/basic-hik", "");
        String format = request.getParameter("format");
        String url = StrUtil.format("{} {}{}", method, uri,
                StrUtil.isBlank(format) ? "" : StrUtil.format("?format={}", format));
        log.info("url:{}", url);

        HCISUPCMS.NET_EHOME_PTXML_PARAM m_struParam = new HCISUPCMS.NET_EHOME_PTXML_PARAM();
        m_struParam.read();

        //处理inputBody
        String inputBody = "";
        if (!"GET".equals(method)) {
            StringBuilder requestBody = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
                requestBody.append("\n"); // 添加换行符以保持格式，可能对XML解析有帮助，对JSON可能不是必需的
            }
            inputBody = requestBody.toString();
            log.info("inputBody:{}", inputBody);
        }
        //透传URL，不同功能对应不同的URL，完整协议报文说明需要参考ISAPI协议文档
        HCISUPCMS.BYTE_ARRAY ptrUrl = new HCISUPCMS.BYTE_ARRAY(url.length() + 1);
        System.arraycopy(url.getBytes(), 0, ptrUrl.byValue, 0, url.length());
        ptrUrl.write();
        m_struParam.pRequestUrl = ptrUrl.getPointer();
        m_struParam.dwRequestUrlLen = url.length();
        //输入参数，XML或者JSON数据
        if (!"GET".equals(method)) {
            int iInSize = inputBody.getBytes("utf-8").length;
            HCISUPCMS.BYTE_ARRAY ptrInParam = new HCISUPCMS.BYTE_ARRAY(iInSize + 1);
            System.arraycopy(inputBody.getBytes("utf-8"), 0, ptrInParam.byValue, 0, iInSize);
            ptrInParam.write();
            m_struParam.pInBuffer = ptrInParam.getPointer();//GET获取时不需要输入参数，输入为null
            m_struParam.dwInSize = iInSize;
        } else {
            m_struParam.pInBuffer = null;//GET获取时不需要输入参数，输入为null
            m_struParam.dwInSize = 0;
        }

        //输出参数，分配的内存用于存储返回的数据，需要大于等于实际内容大小
        int iOutSize = 2 * 1024 * 1024;
        HCISUPCMS.BYTE_ARRAY ptrOutByte = new HCISUPCMS.BYTE_ARRAY(iOutSize);
        m_struParam.pOutBuffer = ptrOutByte.getPointer();
        m_struParam.dwOutSize = iOutSize;
        m_struParam.dwRecvTimeOut = 5000;//接收超时时间，单位毫秒
        m_struParam.write();

        if (!cmsDemo.gethCEhomeCMS().NET_ECMS_ISAPIPassThrough(loginId, m_struParam)) {
            log.error("NET_ECMS_ISAPIPassThrough failed,error：{}", cmsDemo.gethCEhomeCMS().NET_ECMS_GetLastError());
            return cmsDemo.gethCEhomeCMS().NET_ECMS_GetLastError();
        } else {
            m_struParam.read();
            ptrOutByte.read();
            String response = CommonUtils.cleanLineBreaks(new String(ptrOutByte.byValue).trim());
            log.info("NET_ECMS_ISAPIPassThrough succeed ptrOutByte:{}", response);
            return response;
        }
    }

    public List<HeartBeatBO> listOnlineDevices() {
        return  CmsDemo.deviceHeartBeatMap.values().stream().filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    public Boolean isDeviceOnlineByTerminalNum(String terminalNum) {
        return CmsDemo.deviceHeartBeatMap.values().stream()
                .filter(heartBeat -> StrUtil.isNotBlank(heartBeat.getDeviceId()))
                .anyMatch(heartBeat -> terminalNum.equals(heartBeat.getDeviceId()));
    }
}
