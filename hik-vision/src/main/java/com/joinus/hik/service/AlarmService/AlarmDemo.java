package com.joinus.hik.service.AlarmService;

import cn.hutool.json.JSONUtil;
import com.joinus.hik.common.Alarm.AlarmEventHandle;
import com.joinus.hik.common.CommonMethod;
import com.joinus.hik.common.osSelect;
import com.joinus.hik.config.HikProperties;
import com.joinus.hik.kafka.KafkaProducer;
import com.joinus.hik.service.CmsService.HCISUPCMS;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AlarmDemo {
    public static HCISUPAlarm hcEHomeAlarm = null;
    public static int AlarmHandle = -1; //Alarm监听句柄
    static EHomeMsgCallBack cbEHomeMsgCallBack;//报警监听回调函数实现
    static HCISUPAlarm.NET_EHOME_ALARM_LISTEN_PARAM net_ehome_alarm_listen_param = new HCISUPAlarm.NET_EHOME_ALARM_LISTEN_PARAM();

    @Autowired
    private HikProperties hikProperties;
    @Autowired
    private KafkaProducer kafkaProducer;
    @Value("${hik.log-level:1}")
    private int hikLogLevel;

    /**
     * 根据不同操作系统选择不同的库文件和库路径
     *
     * @return
     */
    private static boolean CreateSDKInstance() {
        if (hcEHomeAlarm == null) {
            synchronized (HCISUPAlarm.class) {
                String strDllPath = "";
                try {
                    //System.setProperty("jna.debug_load", "true");
                    if (osSelect.isWindows())
                        //win系统加载库路径(路径不要带中文)
                    {
                        strDllPath = System.getProperty("user.dir") + "\\hik-vision\\libs\\dll\\HCISUPAlarm.dll";
                    } else if (osSelect.isLinux())
                        //Linux系统加载库路径(路径不要带中文)
                    {
                        strDllPath = System.getProperty("user.dir") + "/hik-vision/libs/so/libHCISUPAlarm.so";
                    }
                    hcEHomeAlarm = (HCISUPAlarm) Native.loadLibrary(strDllPath, HCISUPAlarm.class);
                } catch (Exception ex) {
                     log.info("loadLibrary: " + strDllPath + " Error: " + ex.getMessage());
                    return false;
                }
            }
        }
        return true;
    }

    @Value("${hik.alarm-server-ip}")
    private String serverIp;

    public void eAlarm_Init() {
        log.info("==============" + serverIp);
        log.info("==============" + JSONUtil.toJsonStr(hikProperties));

        if (hcEHomeAlarm == null) {
            if (!CreateSDKInstance()) {
                 log.info("Load CMS SDK fail");
                return;
            }
        }
        if (osSelect.isWindows()) {
            HCISUPCMS.BYTE_ARRAY ptrByteArrayCrypto = new HCISUPCMS.BYTE_ARRAY(256);
            String strPathCrypto = System.getProperty("user.dir") + "\\hik-vision\\libs\\dll\\libeay32.dll"; //Linux版本是libcrypto.so库文件的路径
            System.arraycopy(strPathCrypto.getBytes(), 0, ptrByteArrayCrypto.byValue, 0, strPathCrypto.length());
            ptrByteArrayCrypto.write();
            hcEHomeAlarm.NET_EALARM_SetSDKInitCfg(0, ptrByteArrayCrypto.getPointer());

            //设置libssl.so所在路径
            HCISUPCMS.BYTE_ARRAY ptrByteArraySsl = new HCISUPCMS.BYTE_ARRAY(256);
            String strPathSsl = System.getProperty("user.dir") + "\\hik-vision\\libs\\dll\\ssleay32.dll";    //Linux版本是libssl.so库文件的路径
            System.arraycopy(strPathSsl.getBytes(), 0, ptrByteArraySsl.byValue, 0, strPathSsl.length());
            ptrByteArraySsl.write();
            hcEHomeAlarm.NET_EALARM_SetSDKInitCfg(1, ptrByteArraySsl.getPointer());

            //报警服务初始化
            boolean bRet = hcEHomeAlarm.NET_EALARM_Init();
            if (!bRet) {
                 log.info("NET_EALARM_Init failed!");
            }
            //设置HCAapSDKCom组件库文件夹所在路径
            HCISUPCMS.BYTE_ARRAY ptrByteArrayCom = new HCISUPCMS.BYTE_ARRAY(256);
            String strPathCom = System.getProperty("user.dir") + "\\hik-vision\\libs\\dll\\HCAapSDKCom";        //只支持绝对路径，建议使用英文路径
            System.arraycopy(strPathCom.getBytes(), 0, ptrByteArrayCom.byValue, 0, strPathCom.length());
            ptrByteArrayCom.write();
            hcEHomeAlarm.NET_EALARM_SetSDKLocalCfg(5, ptrByteArrayCom.getPointer());
        } else if (osSelect.isLinux()) {
            HCISUPCMS.BYTE_ARRAY ptrByteArrayCrypto = new HCISUPCMS.BYTE_ARRAY(256);
            String strPathCrypto = System.getProperty("user.dir") + "/hik-vision/libs/so/libcrypto.so"; //Linux版本是libcrypto.so库文件的路径
            System.arraycopy(strPathCrypto.getBytes(), 0, ptrByteArrayCrypto.byValue, 0, strPathCrypto.length());
            ptrByteArrayCrypto.write();
            hcEHomeAlarm.NET_EALARM_SetSDKInitCfg(0, ptrByteArrayCrypto.getPointer());

            //设置libssl.so所在路径
            HCISUPCMS.BYTE_ARRAY ptrByteArraySsl = new HCISUPCMS.BYTE_ARRAY(256);
            String strPathSsl = System.getProperty("user.dir") + "/hik-vision/libs/so/libssl.so";    //Linux版本是libssl.so库文件的路径
            System.arraycopy(strPathSsl.getBytes(), 0, ptrByteArraySsl.byValue, 0, strPathSsl.length());
            ptrByteArraySsl.write();
            hcEHomeAlarm.NET_EALARM_SetSDKInitCfg(1, ptrByteArraySsl.getPointer());
            //报警服务初始化
            boolean bRet = hcEHomeAlarm.NET_EALARM_Init();
            if (!bRet) {
                 log.info("NET_EALARM_Init failed!");
            }
            //设置HCAapSDKCom组件库文件夹所在路径
            HCISUPCMS.BYTE_ARRAY ptrByteArrayCom = new HCISUPCMS.BYTE_ARRAY(256);
            String strPathCom = System.getProperty("user.dir") + "/hik-vision/libs/so/HCAapSDKCom/";        //只支持绝对路径，建议使用英文路径
            System.arraycopy(strPathCom.getBytes(), 0, ptrByteArrayCom.byValue, 0, strPathCom.length());
            ptrByteArrayCom.write();
            hcEHomeAlarm.NET_EALARM_SetSDKLocalCfg(5, ptrByteArrayCom.getPointer());

        }

        //启用SDK写日志  0 默认 1 错误日志 2 错误和调试日志 3 错误 调试  信息日志
        boolean logToFile = hcEHomeAlarm.NET_EALARM_SetLogToFile(hikLogLevel, System.getProperty("user.dir") + "/EHomeSDKLog", false);
    }

    /**
     * 开启报警服务监听
     */
    public void startAlarmListen() {
        if (cbEHomeMsgCallBack == null) {
            cbEHomeMsgCallBack = new EHomeMsgCallBack();
        }
        if (Short.parseShort(hikProperties.getAlarmServerType()) == 2) {
            System.arraycopy(hikProperties.getAlarmServerListenIp().getBytes(), 0, net_ehome_alarm_listen_param.struAddress.szIP, 0,
                    hikProperties.getAlarmServerListenIp().length());
            net_ehome_alarm_listen_param.struAddress.wPort = Short.parseShort(hikProperties.getAlarmServerListenTcpPort());
            net_ehome_alarm_listen_param.byProtocolType = 2; //协议类型：0- TCP，1- UDP, 2-MQTT
        } else {
            net_ehome_alarm_listen_param.struAddress.wPort =
                    Short.parseShort(hikProperties.getAlarmServerListenUdpPort());
            net_ehome_alarm_listen_param.byProtocolType = 1; //协议类型：0- TCP，1- UDP, 2-MQTT
        }
        net_ehome_alarm_listen_param.fnMsgCb = cbEHomeMsgCallBack;
        net_ehome_alarm_listen_param.byUseCmsPort = 0; //是否复用CMS端口：0- 不复用，非0- 复用
        net_ehome_alarm_listen_param.write();

        //启动报警服务器监听
        AlarmHandle = hcEHomeAlarm.NET_EALARM_StartListen(net_ehome_alarm_listen_param);
        if (AlarmHandle < 0) {
             log.info("NET_EALARM_StartListen failed, error:" + hcEHomeAlarm.NET_EALARM_GetLastError());
            hcEHomeAlarm.NET_EALARM_Fini();
            return;
        } else {
            String AlarmListenInfo = new String(net_ehome_alarm_listen_param.struAddress.szIP).trim() + "_" + net_ehome_alarm_listen_param.struAddress.wPort;
             log.info("报警服务器：" + AlarmListenInfo + ",NET_EALARM_StartListen succeed");
        }
    }

    /**
     * 报警回调函数实现，设备上传事件通过回调函数上传进行解析
     */
    class EHomeMsgCallBack implements HCISUPAlarm.EHomeMsgCallBack {
        @Override
        public boolean invoke(int iHandle, HCISUPAlarm.NET_EHOME_ALARM_MSG pAlarmMsg, Pointer pUser) {
            if ("console".equals(hikProperties.getEventInfoPrintType())) {
                // 输出事件信息到控制台上
                 log.info("AlarmType: " + pAlarmMsg.dwAlarmType + ",dwAlarmInfoLen:" + pAlarmMsg.dwAlarmInfoLen + ",dwXmlBufLen:" + pAlarmMsg.dwXmlBufLen);
            }

            if (pAlarmMsg.dwXmlBufLen != 0) {
                HCISUPAlarm.BYTE_ARRAY strXMLData = new HCISUPAlarm.BYTE_ARRAY(pAlarmMsg.dwXmlBufLen);
                strXMLData.write();
                Pointer pPlateInfo = strXMLData.getPointer();
                pPlateInfo.write(0, pAlarmMsg.pXmlBuf.getByteArray(0, strXMLData.size()), 0, strXMLData.size());
                strXMLData.read();
                String strXML = new String(strXMLData.byValue).trim();
                // 告警接收信息输出到文件中
                if ("file".equals(hikProperties.getEventInfoPrintType())) {
                    // 输出事件信息到文件中
                    CommonMethod.outputToFile("dwAlarmType_pXmlBuf_"+ pAlarmMsg.dwAlarmType, ".xml", strXML);
                } else {
                    // 输出事件信息到控制台上
                     log.info(strXML + "\n");
                }
                kafkaProducer.sendClientEventToServer(strXML);
            }
            AlarmEventHandle.processAlarmData(pAlarmMsg.dwAlarmType,
                    pAlarmMsg.pAlarmInfo, pAlarmMsg.dwAlarmInfoLen,
                    pAlarmMsg.pXmlBuf, pAlarmMsg.dwXmlBufLen,
                    pAlarmMsg.pHttpUrl, pAlarmMsg.dwHttpUrlLen);
//            switch (pAlarmMsg.dwAlarmType) {
//                case 13:
//                    if (pAlarmMsg.pAlarmInfo != null) {
//                        HCISUPAlarm.NET_EHOME_ALARM_ISAPI_INFO strISAPIData = new HCISUPAlarm.NET_EHOME_ALARM_ISAPI_INFO();
//                        strISAPIData.write();
//                        Pointer pISAPIInfo = strISAPIData.getPointer();
//                        pISAPIInfo.write(0, pAlarmMsg.pAlarmInfo.getByteArray(0, strISAPIData.size()), 0, strISAPIData.size());
//                        strISAPIData.read();
//                        if (strISAPIData.dwAlarmDataLen != 0)//Json或者XML数据
//                        {
//                            HCISUPAlarm.BYTE_ARRAY m_strISAPIData = new HCISUPAlarm.BYTE_ARRAY(strISAPIData.dwAlarmDataLen);
//                            m_strISAPIData.write();
//                            Pointer pPlateInfo = m_strISAPIData.getPointer();
//                            pPlateInfo.write(0, strISAPIData.pAlarmData.getByteArray(0, m_strISAPIData.size()), 0, m_strISAPIData.size());
//                            m_strISAPIData.read();
//                             log.info(new String(m_strISAPIData.byValue).trim() + "\n");
//                        }
//                    }
//                    break;
//                default:
//                    break;
//            }
            return true;
        }
    }

}
