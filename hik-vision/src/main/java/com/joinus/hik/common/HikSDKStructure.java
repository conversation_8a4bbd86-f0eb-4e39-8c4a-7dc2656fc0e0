package com.joinus.hik.common;

import com.sun.jna.Structure;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

/**
 * 海康jna.jar比较老,结构体定义没有getFiledOrder，可创建一个类继承 Structure 解决每一个结构体都要实现getFiledOrder的问题
 * @return
 * @description TODO
 * <AUTHOR>
 * @date 2024/6/4 16:05
 */
public class HikSDKStructure extends Structure implements Serializable {

    @Override
    protected List<String> getFieldOrder(){
        List<String> fieldOrderList = new ArrayList<String>();
        for (Class<?> cls = getClass();
             !cls.equals(HikSDKStructure.class);
             cls = cls.getSuperclass()) {
            Field[] fields = cls.getDeclaredFields();
            int modifiers;
            for (Field field : fields) {
                modifiers = field.getModifiers();
                if (Modifier.isStatic(modifiers) || !Modifier.isPublic(modifiers)) {
                    continue;
                }
                fieldOrderList.add(field.getName());
            }
        }
        return fieldOrderList;
    }
}