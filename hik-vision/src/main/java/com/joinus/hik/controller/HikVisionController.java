package com.joinus.hik.controller;

import com.joinus.common.common.BaseController;
import com.joinus.hik.model.HeartBeatBO;
import com.joinus.hik.service.IsApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/devices")
public class HikVisionController extends BaseController {

    @Autowired
    private IsApiService isApiService;

    @GetMapping
    public List<HeartBeatBO> get(HttpServletRequest request) throws Exception {
        return isApiService.listOnlineDevices();
    }

    @GetMapping("/{terminalNum}")
    public Boolean getDeviceOnlineByTerminalNum(HttpServletRequest request, @PathVariable String terminalNum) throws Exception {
        return isApiService.isDeviceOnlineByTerminalNum(terminalNum);
    }

}
