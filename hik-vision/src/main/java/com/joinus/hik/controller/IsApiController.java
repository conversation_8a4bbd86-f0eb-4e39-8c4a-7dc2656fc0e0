package com.joinus.hik.controller;

import com.joinus.hik.service.IsApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/ISAPI")
@Slf4j
public class IsApiController{

    @Autowired
    private IsApiService isApiService;

    @GetMapping("/**")
    public Object get(HttpServletRequest request)  {
        String deviceId = request.getHeader("Deviceid");
        try {
            return isApiService.proxyRequest(request, deviceId);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @PostMapping("/**")
    public Object post(HttpServletRequest request) {
        String deviceId = request.getHeader("Deviceid");
        try {
            return isApiService.proxyRequest(request, deviceId);
        } catch (Exception e) {
            log.warn("设备:{},调用POST接口异常:{}", deviceId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @PutMapping("/**")
    public Object put(HttpServletRequest request){
        String deviceId = request.getHeader("Deviceid");
        try {
            return isApiService.proxyRequest(request, deviceId);
        } catch (Exception e) {
            log.warn("设备:{},调用PUT接口异常:{}", deviceId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }


    @DeleteMapping("/**")
    public Object delete(HttpServletRequest request) {
        String deviceId = request.getHeader("Deviceid");
        try {
            return isApiService.proxyRequest(request, deviceId);
        } catch (Exception e) {
            log.warn("设备:{},调用DELETE接口异常:{}", deviceId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }


}
