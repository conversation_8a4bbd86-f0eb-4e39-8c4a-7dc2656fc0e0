package com.joinus.hik.UIModule;

import javax.swing.*;
import java.awt.*;

/**
 * <AUTHOR>
 * @date 2023/8/21 19:02
 * @desc 预览视频的窗口界面
 */
public class PreviewVideoUI {

    // 创建一个JFrame对象
    public static JFrame jf = new JFrame("预览窗口");

    public static Panel panelRealPlay;

    public static void jRealWinInit()
    {
        // 设置窗口大小和位置
        PreviewVideoUI.jf.setBounds(500, 200, 800, 450);
        // 创建一个JPanel对象
        PreviewVideoUI.panelRealPlay =new Panel();
        // 设置背景色
        PreviewVideoUI.panelRealPlay.setBackground(Color.white);
        // 将面板添加到窗口
        PreviewVideoUI.jf.add(PreviewVideoUI.panelRealPlay);
        PreviewVideoUI.jf.setVisible(true);    //设置窗口可见
    }

    public static void jRealWinDestroy() {
        PreviewVideoUI.jf.dispose();
    }

}
