package com.joinus.hik.UIModule;

import javax.swing.*;
import java.awt.*;

/**
 * <AUTHOR>
 * @date 2023/8/21 19:02
 * @desc 回放视频的窗口界面
 */
public class PlaybackVideoUI {

    // 创建一个JFrame对象
    public static JFrame jf = new JFrame("回放窗口");

    public static Panel panelPlay;

    public static void jRealWinInit()
    {
        // 设置窗口大小和位置
        PlaybackVideoUI.jf.setBounds(500, 200, 800, 450);
        // 创建一个JPanel对象
        PlaybackVideoUI.panelPlay =new Panel();
        // 设置背景色
        PlaybackVideoUI.panelPlay.setBackground(Color.white);
        // 将面板添加到窗口
        PlaybackVideoUI.jf.add(PlaybackVideoUI.panelPlay);
        PlaybackVideoUI.jf.setVisible(true);    //设置窗口可见
    }

    public static void jRealWinDestroy() {
        PlaybackVideoUI.jf.dispose();
    }

}
