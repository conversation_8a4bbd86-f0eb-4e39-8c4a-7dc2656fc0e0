package com.joinus.hik.DevModule.Acs;


import cn.hutool.core.util.StrUtil;
import com.joinus.hik.common.ConfigFileUtil;
import com.joinus.hik.service.CmsService.HCISUPCMS;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import static com.joinus.hik.service.CmsService.CmsDemo.hCEhomeCMS;

/**
 * 门禁 人脸管理模块示例
 *
 * <AUTHOR>
 * @create 2023-08-15-16:20
 */
@Slf4j
public class AcsFaceManagement {

    /**
     * 下发门禁人脸图片数据
     *
     * @param loginID    登录设备句柄ID
     * @param employeeNo 人员工号，以人为中心方式，下发人脸前需要先下发人员工号，人脸绑定在工号上
     * @param faceURL    人脸图片URL，ISUP协议下发人脸使用URL方式
     * @throws UnsupportedEncodingException
     */
    public static void addFacePicInfo(int loginID, String employeeNo, String faceURL) throws UnsupportedEncodingException {
        HCISUPCMS.NET_EHOME_PTXML_PARAM m_struParam2 = new HCISUPCMS.NET_EHOME_PTXML_PARAM();
        m_struParam2.read();
        //透传URL，不同功能对应不同的URL，完整协议报文说明需要参考ISAPI协议文档
        String urlInBuffer = "POST /ISAPI/Intelligent/FDLib/FaceDataRecord?format=json"; //添加人脸URL
        HCISUPCMS.BYTE_ARRAY ptrurlInBuffer = new HCISUPCMS.BYTE_ARRAY(urlInBuffer.length() + 1);
        System.arraycopy(urlInBuffer.getBytes(), 0, ptrurlInBuffer.byValue, 0, urlInBuffer.length());
        ptrurlInBuffer.write();
        m_struParam2.pRequestUrl = ptrurlInBuffer.getPointer();
        m_struParam2.dwRequestUrlLen = urlInBuffer.length();

        //输入参数，XML或者JSON数据,添加人脸图片json报文
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("faceURL", faceURL);   //图片需要使用URL方式下发，建议使用ISUP中存储服务生成图片URL 参考SdkFunctionDemo类中上传图片至存储服务器接口，
        parameter.put("employeeNo", employeeNo); // 员工ID号
        String input = ConfigFileUtil.getReqBodyFromTemplate("conf/acs/AddFaceInfoParam.json", parameter);
        log.info(input);

        byte[] byInbuffer = input.getBytes("utf-8");
        int iInBufLen = byInbuffer.length;
        HCISUPCMS.BYTE_ARRAY ptrInBuffer = new HCISUPCMS.BYTE_ARRAY(iInBufLen);
        ptrInBuffer.read();
        System.arraycopy(byInbuffer, 0, ptrInBuffer.byValue, 0, iInBufLen);
        ptrInBuffer.write();
        m_struParam2.pInBuffer = ptrInBuffer.getPointer();//GET获取时不需要输入参数，输入为null
        m_struParam2.dwInSize = iInBufLen;
        //输出参数，分配的内存用于存储返回的数据，需要大于等于实际内容大小
        int iOutSize2 = 2 * 1024 * 1024;
        HCISUPCMS.BYTE_ARRAY ptrOutByte2 = new HCISUPCMS.BYTE_ARRAY(iOutSize2);
        m_struParam2.pOutBuffer = ptrOutByte2.getPointer();
        m_struParam2.dwOutSize = iOutSize2;
        m_struParam2.dwRecvTimeOut = 5000; //接收超时时间，单位毫秒
        m_struParam2.write();
        if (!hCEhomeCMS.NET_ECMS_ISAPIPassThrough(loginID, m_struParam2)) {
            log.info("下发门禁人脸失败，NET_ECMS_ISAPIPassThrough failed,error：" + hCEhomeCMS.NET_ECMS_GetLastError());
            return;
        } else {
            m_struParam2.read();
            ptrOutByte2.read();
            log.info("NET_ECMS_ISAPIPassThrough succeed 输出报文:" + StrUtil.cleanBlank(new String(ptrOutByte2.byValue).trim()));
        }
        return;
    }

    /**
     * 修改门禁人脸图片数据
     *
     * @param loginID    登录设备句柄ID
     * @param employeeNo 人员工号，以人为中心方式，下发人脸前需要先下发人员工号，人脸绑定在工号上
     * @param faceURL    人脸图片URL，ISUP协议下发人脸使用URL方式
     */
    public static void modifyFacePicInfo(int loginID, String employeeNo, String faceURL) throws UnsupportedEncodingException {

        HCISUPCMS.NET_EHOME_PTXML_PARAM m_struParam2 = new HCISUPCMS.NET_EHOME_PTXML_PARAM();
        m_struParam2.read();
        // 透传URL，不同功能对应不同的URL，完整协议报文说明需要参考ISAPI协议文档
        String urlInBuffer = "PUT /ISAPI/Intelligent/FDLib/FDModify?format=json"; //修改人脸URL
        HCISUPCMS.BYTE_ARRAY ptrurlInBuffer = new HCISUPCMS.BYTE_ARRAY(urlInBuffer.length() + 1);
        System.arraycopy(urlInBuffer.getBytes(), 0, ptrurlInBuffer.byValue, 0, urlInBuffer.length());
        ptrurlInBuffer.write();
        m_struParam2.pRequestUrl = ptrurlInBuffer.getPointer();
        m_struParam2.dwRequestUrlLen = urlInBuffer.length();

        //输入参数，XML或者JSON数据,添加人脸图片json报文
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("faceURL", faceURL); // 人脸图片URL
        parameter.put("employeeNo", employeeNo); // 员工ID号
        String input = ConfigFileUtil.getReqBodyFromTemplate("conf/acs/ModifyFaceInfoParam.json", parameter);
        log.info(input);

        byte[] byInbuffer = input.getBytes("utf-8");
        int iInBufLen = byInbuffer.length;
        HCISUPCMS.BYTE_ARRAY ptrInBuffer = new HCISUPCMS.BYTE_ARRAY(iInBufLen);
        ptrInBuffer.read();
        System.arraycopy(byInbuffer, 0, ptrInBuffer.byValue, 0, iInBufLen);
        ptrInBuffer.write();
        m_struParam2.pInBuffer = ptrInBuffer.getPointer();//GET获取时不需要输入参数，输入为null
        m_struParam2.dwInSize = iInBufLen;
        //输出参数，分配的内存用于存储返回的数据，需要大于等于实际内容大小
        int iOutSize2 = 2 * 1024 * 1024;
        HCISUPCMS.BYTE_ARRAY ptrOutByte2 = new HCISUPCMS.BYTE_ARRAY(iOutSize2);
        m_struParam2.pOutBuffer = ptrOutByte2.getPointer();
        m_struParam2.dwOutSize = iOutSize2;
        m_struParam2.dwRecvTimeOut = 5000; //接收超时时间，单位毫秒
        m_struParam2.write();
        if (!hCEhomeCMS.NET_ECMS_ISAPIPassThrough(loginID, m_struParam2)) {
            log.info("修改门禁人脸失败，NET_ECMS_ISAPIPassThrough failed,error：" + hCEhomeCMS.NET_ECMS_GetLastError());
            return;
        } else {
            m_struParam2.read();
            ptrOutByte2.read();
            log.info("NET_ECMS_ISAPIPassThrough succeed 输出报文:" + StrUtil.cleanBlank(new String(ptrOutByte2.byValue).trim()));
        }
        return;
    }

    /**
     * 删除人脸图片数据
     *
     * @param loginID    登录设备句柄ID
     * @param employeeNo 人员工号，按照指定人员删除关联人脸图片
     * @throws UnsupportedEncodingException
     */
    public static void deleteFacePicInfo(int loginID, String employeeNo) throws UnsupportedEncodingException {
        HCISUPCMS.NET_EHOME_PTXML_PARAM m_struParam2 = new HCISUPCMS.NET_EHOME_PTXML_PARAM();
        m_struParam2.read();
        //透传URL，不同功能对应不同的URL，完整协议报文说明需要参考ISAPI协议文档
        String urlInBuffer = "PUT /ISAPI/Intelligent/FDLib/FDSearch/Delete?format=json&FDID=1&faceLibType=blackFD"; //删除人脸URL
        HCISUPCMS.BYTE_ARRAY ptrurlInBuffer = new HCISUPCMS.BYTE_ARRAY(urlInBuffer.length() + 1);
        System.arraycopy(urlInBuffer.getBytes(), 0, ptrurlInBuffer.byValue, 0, urlInBuffer.length());
        ptrurlInBuffer.write();
        m_struParam2.pRequestUrl = ptrurlInBuffer.getPointer();
        m_struParam2.dwRequestUrlLen = urlInBuffer.length();

        //输入参数，XML或者JSON数据,删除人脸图片json报文
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("employeeNo", employeeNo); // 员工ID号
        String input = ConfigFileUtil.getReqBodyFromTemplate("conf/acs/DeleteFaceInfoParam.json", parameter);
        log.info(input);

        byte[] byInbuffer = input.getBytes("utf-8");
        int iInBufLen = byInbuffer.length;
        HCISUPCMS.BYTE_ARRAY ptrInBuffer = new HCISUPCMS.BYTE_ARRAY(iInBufLen);
        ptrInBuffer.read();
        System.arraycopy(byInbuffer, 0, ptrInBuffer.byValue, 0, iInBufLen);
        ptrInBuffer.write();
        m_struParam2.pInBuffer = ptrInBuffer.getPointer();//GET获取时不需要输入参数，输入为null
        m_struParam2.dwInSize = iInBufLen;
        //输出参数，分配的内存用于存储返回的数据，需要大于等于实际内容大小
        int iOutSize2 = 2 * 1024 * 1024;
        HCISUPCMS.BYTE_ARRAY ptrOutByte2 = new HCISUPCMS.BYTE_ARRAY(iOutSize2);
        m_struParam2.pOutBuffer = ptrOutByte2.getPointer();
        m_struParam2.dwOutSize = iOutSize2;
        m_struParam2.dwRecvTimeOut = 5000; //接收超时时间，单位毫秒
        m_struParam2.write();
        if (!hCEhomeCMS.NET_ECMS_ISAPIPassThrough(loginID, m_struParam2)) {
            log.info("删除门禁人脸失败，NET_ECMS_ISAPIPassThrough failed,error：" + hCEhomeCMS.NET_ECMS_GetLastError());
            return;
        } else {
            m_struParam2.read();
            ptrOutByte2.read();
            log.info("NET_ECMS_ISAPIPassThrough succeed 输出报文:" + StrUtil.cleanBlank(new String(ptrOutByte2.byValue).trim()));
        }
        return;


    }

    /**
     * 查询门禁人脸图片
     *
     * @param loginID    登录设备句柄ID
     * @param employeeNo 人员工号，按照指定人员查询关联人脸图片
     * @throws UnsupportedEncodingException
     */
    public static void searchFacePicInfo(int loginID, String employeeNo) throws UnsupportedEncodingException {
        HCISUPCMS.NET_EHOME_PTXML_PARAM m_struParam2 = new HCISUPCMS.NET_EHOME_PTXML_PARAM();
        m_struParam2.read();
        //透传URL，不同功能对应不同的URL，完整协议报文说明需要参考ISAPI协议文档
        String urlInBuffer = "POST /ISAPI/Intelligent/FDLib/FDSearch?format=json"; //查询人脸URL
        HCISUPCMS.BYTE_ARRAY ptrurlInBuffer = new HCISUPCMS.BYTE_ARRAY(urlInBuffer.length() + 1);
        System.arraycopy(urlInBuffer.getBytes(), 0, ptrurlInBuffer.byValue, 0, urlInBuffer.length());
        ptrurlInBuffer.write();
        m_struParam2.pRequestUrl = ptrurlInBuffer.getPointer();
        m_struParam2.dwRequestUrlLen = urlInBuffer.length();

        //输入参数，XML或者JSON数据,查询人脸图片json报文
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("employeeNo", employeeNo); // 员工ID号
        String input = ConfigFileUtil.getReqBodyFromTemplate("conf/acs/SearchFaceInfoParam.json", parameter);
        //批量查询人脸图片json报文
/*        String input = "{\n" +
                "    \"searchResultPosition\":0,\n" +
                "    \"maxResults\":30,\n" +
                "    \"faceLibType\":\"blackFD\",\n" +
                "    \"FDID\":\"1\"\n" +
                "}";*/
        log.info(input);

        byte[] byInbuffer = input.getBytes("utf-8");
        int iInBufLen = byInbuffer.length;
        HCISUPCMS.BYTE_ARRAY ptrInBuffer = new HCISUPCMS.BYTE_ARRAY(iInBufLen);
        ptrInBuffer.read();
        System.arraycopy(byInbuffer, 0, ptrInBuffer.byValue, 0, iInBufLen);
        ptrInBuffer.write();
        m_struParam2.pInBuffer = ptrInBuffer.getPointer();//GET获取时不需要输入参数，输入为null
        m_struParam2.dwInSize = iInBufLen;
        //输出参数，分配的内存用于存储返回的数据，需要大于等于实际内容大小
        int iOutSize2 = 2 * 1024 * 1024;
        HCISUPCMS.BYTE_ARRAY ptrOutByte2 = new HCISUPCMS.BYTE_ARRAY(iOutSize2);
        m_struParam2.pOutBuffer = ptrOutByte2.getPointer();
        m_struParam2.dwOutSize = iOutSize2;
        m_struParam2.dwRecvTimeOut = 5000; //接收超时时间，单位毫秒
        m_struParam2.write();
        if (!hCEhomeCMS.NET_ECMS_ISAPIPassThrough(loginID, m_struParam2)) {
            log.info("删除门禁人脸失败，NET_ECMS_ISAPIPassThrough failed,error：" + hCEhomeCMS.NET_ECMS_GetLastError());
            return;
        } else {
            m_struParam2.read();
            ptrOutByte2.read();
            log.info("NET_ECMS_ISAPIPassThrough succeed 输出报文:" + StrUtil.cleanBlank(new String(ptrOutByte2.byValue).trim()));
        }
        return;


    }


}
