package com.joinus.hik.DevModule.Acs;

import cn.hutool.core.util.StrUtil;
import com.joinus.hik.common.ConfigFileUtil;
import com.joinus.hik.service.CmsService.HCISUPCMS;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.joinus.hik.service.CmsService.CmsDemo.hCEhomeCMS;


/**
 * 门禁 人员管理模块示例
 *
 * <AUTHOR>
 * @create 2023-08-15-13:50
 */
@Slf4j
public class AcsUserManagement {


    /**
     * 添加人员工号信息
     *
     * @param loginID 登录设备句柄ID
     * @throws UnsupportedEncodingException
     */
    public static void addEmployeeInfo(int loginID) throws UnsupportedEncodingException {
        HCISUPCMS.NET_EHOME_PTXML_PARAM m_struParam2 = new HCISUPCMS.NET_EHOME_PTXML_PARAM();
        m_struParam2.read();
        //透传URL，不同功能对应不同的URL，完整协议报文说明需要参考ISAPI协议文档
        String urlInBuffer = "POST /ISAPI/AccessControl/UserInfo/Record?format=json"; //添加人员URL
        HCISUPCMS.BYTE_ARRAY ptrurlInBuffer = new HCISUPCMS.BYTE_ARRAY(urlInBuffer.length() + 1);
        System.arraycopy(urlInBuffer.getBytes(), 0, ptrurlInBuffer.byValue, 0, urlInBuffer.length());
        ptrurlInBuffer.write();
        m_struParam2.pRequestUrl = ptrurlInBuffer.getPointer();
        m_struParam2.dwRequestUrlLen = urlInBuffer.length();

        //输入参数，XML或者JSON数据,下发人员信息json报文
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("employeeNo", "test001"); // 员工ID
        parameter.put("name", "测试账号98"); // 员工名称
        parameter.put("enable", true); // 是否启用
        parameter.put("doorNo", 1); // 门编号
        String input = ConfigFileUtil.getReqBodyFromTemplate("conf/acs/AddUserInfoParam.json", parameter);

        log.info(input);
        byte[] byInbuffer = input.getBytes("utf-8");
        int iInBufLen = byInbuffer.length;
        HCISUPCMS.BYTE_ARRAY ptrInBuffer = new HCISUPCMS.BYTE_ARRAY(iInBufLen);
        ptrInBuffer.read();
        System.arraycopy(byInbuffer, 0, ptrInBuffer.byValue, 0, iInBufLen);
        ptrInBuffer.write();
        m_struParam2.pInBuffer = ptrInBuffer.getPointer();//GET获取时不需要输入参数，输入为null
        m_struParam2.dwInSize = iInBufLen;
        //输出参数，分配的内存用于存储返回的数据，需要大于等于实际内容大小
        int iOutSize2 = 2 * 1024 * 1024;
        HCISUPCMS.BYTE_ARRAY ptrOutByte2 = new HCISUPCMS.BYTE_ARRAY(iOutSize2);
        m_struParam2.pOutBuffer = ptrOutByte2.getPointer();
        m_struParam2.dwOutSize = iOutSize2;
        m_struParam2.dwRecvTimeOut = 5000; //接收超时时间，单位毫秒
        m_struParam2.write();
        if (!hCEhomeCMS.NET_ECMS_ISAPIPassThrough(loginID, m_struParam2)) {
            log.info("添加人员失败，NET_ECMS_ISAPIPassThrough failed,error：" + hCEhomeCMS.NET_ECMS_GetLastError());
            return;
        } else {
            m_struParam2.read();
            ptrOutByte2.read();
            log.info("NET_ECMS_ISAPIPassThrough succeed 输出报文:" + StrUtil.cleanBlank(new String(ptrOutByte2.byValue).trim()));
        }
        return;

    }

    /**
     * 删除人员工号信息
     *
     * @param loginID 登录设备句柄ID
     * @throws UnsupportedEncodingException
     */
    public static void deleteEmployeeInfo(int loginID) throws UnsupportedEncodingException {
        HCISUPCMS.NET_EHOME_PTXML_PARAM m_struParam2 = new HCISUPCMS.NET_EHOME_PTXML_PARAM();
        m_struParam2.read();
        //透传URL，不同功能对应不同的URL，完整协议报文说明需要参考ISAPI协议文档
        String urlInBuffer = "PUT /ISAPI/AccessControl/UserInfoDetail/Delete?format=json"; //删除人员URL
        HCISUPCMS.BYTE_ARRAY ptrurlInBuffer = new HCISUPCMS.BYTE_ARRAY(urlInBuffer.length() + 1);
        System.arraycopy(urlInBuffer.getBytes(), 0, ptrurlInBuffer.byValue, 0, urlInBuffer.length());
        ptrurlInBuffer.write();
        m_struParam2.pRequestUrl = ptrurlInBuffer.getPointer();
        m_struParam2.dwRequestUrlLen = urlInBuffer.length();

        //输入参数，XML或者JSON数据,删除人员信息json报文
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("mode", "byEmployeeNo"); // 删除方式, byEmployeeNo: 按员工ID号
        parameter.put("employeeNo", "989898"); // 员工ID号
        String input = ConfigFileUtil.getReqBodyFromTemplate("conf/acs/DeleteUserInfoParam.json", parameter);

        log.info(input);
        byte[] byInbuffer = input.getBytes("utf-8");
        int iInBufLen = byInbuffer.length;
        HCISUPCMS.BYTE_ARRAY ptrInBuffer = new HCISUPCMS.BYTE_ARRAY(iInBufLen);
        ptrInBuffer.read();
        System.arraycopy(byInbuffer, 0, ptrInBuffer.byValue, 0, iInBufLen);
        ptrInBuffer.write();
        m_struParam2.pInBuffer = ptrInBuffer.getPointer();//GET获取时不需要输入参数，输入为null
        m_struParam2.dwInSize = iInBufLen;
        //输出参数，分配的内存用于存储返回的数据，需要大于等于实际内容大小
        int iOutSize2 = 2 * 1024 * 1024;
        HCISUPCMS.BYTE_ARRAY ptrOutByte2 = new HCISUPCMS.BYTE_ARRAY(iOutSize2);
        m_struParam2.pOutBuffer = ptrOutByte2.getPointer();
        m_struParam2.dwOutSize = iOutSize2;
        m_struParam2.dwRecvTimeOut = 5000; //接收超时时间，单位毫秒
        m_struParam2.write();
        if (!hCEhomeCMS.NET_ECMS_ISAPIPassThrough(loginID, m_struParam2)) {
            log.info("删除人员失败，NET_ECMS_ISAPIPassThrough failed,error：" + hCEhomeCMS.NET_ECMS_GetLastError());
            return;
        } else {
            m_struParam2.read();
            ptrOutByte2.read();
            log.info("NET_ECMS_ISAPIPassThrough succeed 输出报文:" + StrUtil.cleanBlank(new String(ptrOutByte2.byValue).trim()));
        }
        return;
    }


    /**
     * 查询人员工号信息
     *
     * @param loginID 登录设备句柄ID
     * @throws UnsupportedEncodingException
     */
    public static void searchEmployeeInfo(int loginID) throws UnsupportedEncodingException {
        HCISUPCMS.NET_EHOME_PTXML_PARAM m_struParam2 = new HCISUPCMS.NET_EHOME_PTXML_PARAM();
        m_struParam2.read();
        //透传URL，不同功能对应不同的URL，完整协议报文说明需要参考ISAPI协议文档
        String urlInBuffer = "POST /ISAPI/AccessControl/UserInfo/Search?format=json"; //查询人员URL
        HCISUPCMS.BYTE_ARRAY ptrurlInBuffer = new HCISUPCMS.BYTE_ARRAY(urlInBuffer.length() + 1);
        System.arraycopy(urlInBuffer.getBytes(), 0, ptrurlInBuffer.byValue, 0, urlInBuffer.length());
        ptrurlInBuffer.write();
        m_struParam2.pRequestUrl = ptrurlInBuffer.getPointer();
        m_struParam2.dwRequestUrlLen = urlInBuffer.length();
        UUID uuid = UUID.randomUUID();

        //输入参数，XML或者JSON数据,查询多条人员信息json报文
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("searchID", uuid); // 查询id
        parameter.put("maxResults", 30); // 最大查询数量
        String input = ConfigFileUtil.getReqBodyFromTemplate("conf/acs/SearchUserInfoParam.json", parameter);
        //输入参数，XML或者JSON数据,指定人员工号查询json报文
        log.info(input);

        byte[] byInbuffer = input.getBytes("utf-8");
        int iInBufLen = byInbuffer.length;
        HCISUPCMS.BYTE_ARRAY ptrInBuffer = new HCISUPCMS.BYTE_ARRAY(iInBufLen);
        ptrInBuffer.read();
        System.arraycopy(byInbuffer, 0, ptrInBuffer.byValue, 0, iInBufLen);
        ptrInBuffer.write();
        m_struParam2.pInBuffer = ptrInBuffer.getPointer();//GET获取时不需要输入参数，输入为null
        m_struParam2.dwInSize = iInBufLen;
        //输出参数，分配的内存用于存储返回的数据，需要大于等于实际内容大小
        int iOutSize2 = 2 * 1024 * 1024;
        HCISUPCMS.BYTE_ARRAY ptrOutByte2 = new HCISUPCMS.BYTE_ARRAY(iOutSize2);
        m_struParam2.pOutBuffer = ptrOutByte2.getPointer();
        m_struParam2.dwOutSize = iOutSize2;
        m_struParam2.dwRecvTimeOut = 5000; //接收超时时间，单位毫秒
        m_struParam2.write();
        if (!hCEhomeCMS.NET_ECMS_ISAPIPassThrough(loginID, m_struParam2)) {
            log.info("查询人员失败，NET_ECMS_ISAPIPassThrough failed,error：" + hCEhomeCMS.NET_ECMS_GetLastError());
            return;
        } else {
            m_struParam2.read();
            ptrOutByte2.read();
            log.info("NET_ECMS_ISAPIPassThrough succeed 输出报文:" + StrUtil.cleanBlank(new String(ptrOutByte2.byValue).trim()));
        }
        return;
    }
}
