FROM ijx-registry.cn-beijing.cr.aliyuncs.com/ijx-public/openjdk11:alpine-jre
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories \
    && apk add tzdata openssl \
    && apk add --update ttf-dejavu fontconfig && rm -rf /var/cache/apk/* \
    && apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
WORKDIR /app
# 从本地目录复制 /hik-vision/libs/so 下的所有文件到镜像中的 /app/hik-vision/libs/so 目录
COPY libs/so /app/hik-vision/libs/so
COPY build/libs/basic-hik-vision.jar /basic-hik-vision.jar
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/basic-hik-vision.jar"]