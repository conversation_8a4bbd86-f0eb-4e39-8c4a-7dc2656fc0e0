
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;
#pid        logs/nginx.pid;

events {
    worker_connections  2000;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    access_log  off;
    sendfile        on;
    #tcp_nopush     on;
    keepalive_timeout  300;
	client_max_body_size 20M;
	proxy_connect_timeout       300s;
	proxy_send_timeout          300s;
	proxy_read_timeout          300s;
	send_timeout                300s;
    #gzip  on;
	
	upstream ISUPDemo_server{
			server 127.0.0.1:18081;
			keepalive 1024;
		}
	# HTTP server
    server {
        listen       18082;
	
	    # HTTPS server
		#listen       443 ssl default;
		
		#可以添加多个listen，实现多端口监听
        server_name  0.0.0.0;
		
		#添加HTTP方法屏蔽，除了PUT、POST、GET、DELETE以外都屏蔽掉,444是Nginx定义的响应状态码，会立即断开连接，没有响应正文
		if ($request_method !~* GET|POST|PUT|DELETE) {
		return 444;
		}
		
        #ssl_certificate      cert.pem;
        #ssl_certificate_key  cert.key;
		#ssl_password_file 	  key.pass;
        #ssl_session_cache     shared:SSL:1m;
        #ssl_session_timeout  5m;
        #ssl_ciphers  AES128-SHA:AES256-SHA:RC4-SHA:DES-CBC3-SHA:RC4-MD5;
        #ssl_prefer_server_ciphers  on;

        add_header X-Content-Type-Options 'nosniff';
		add_header X-Frame-Options SAMEORIGIN;
		add_header X-Xss-Protection '1; mode=block';		
		
		location /index {
				try_files $uri $uri/ /index.html;
           		root   html;
           		index  index.html;
				
				if ($request_filename ~* ^.*?\.(txt|doc|pdf|rar|gz|zip|docx|exe|xlsx|ppt|pptx)$){
				add_header Content-Disposition: 'attachment;';				
				}
        }				
		
		location /doc {
			proxy_pass http://ISUPDemo_server;
			proxy_http_version 1.1;
		   	proxy_set_header Host $host:$server_port;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-Port $remote_port;
			proxy_set_header Connection 'Keep-Alive';
		}
		
		location /SDK {
			proxy_pass http://ISUPDemo_server;
			proxy_http_version 1.1;
		   	proxy_set_header Host $host:$server_port;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-Port $remote_port;
			proxy_set_header Connection 'Keep-Alive';
		}
		
		location /ISAPI {
			proxy_pass http://ISUPDemo_server;
			proxy_http_version 1.1;
		   	proxy_set_header Host $host:$server_port;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-Port $remote_port;
			proxy_set_header Connection 'Keep-Alive';
		}
		
		location /SoftwareLicense.txt {
			proxy_pass http://ISUPDemo_server;
			proxy_http_version 1.1;
		   	proxy_set_header Host $host:$server_port;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-Port $remote_port;
			proxy_set_header Connection 'Keep-Alive';
		}
		
		location /codebase {
			proxy_pass http://ISUPDemo_server;
			proxy_http_version 1.1;
		   	proxy_set_header Host $host:$server_port;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-Port $remote_port;
			proxy_set_header Connection 'Keep-Alive';
		}
		
		location /help {
			proxy_pass http://ISUPDemo_server;
			proxy_http_version 1.1;
		   	proxy_set_header Host $host:$server_port;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Real-Port $remote_port;
			proxy_set_header Connection 'Keep-Alive';
		}
		
    }

}
