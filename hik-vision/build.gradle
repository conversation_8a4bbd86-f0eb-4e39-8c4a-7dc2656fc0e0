plugins {
    id 'java'
    id "org.springframework.boot"
    id "com.palantir.docker" version "0.32.0"
}

group 'com.jounus.basic'
version 'basic-hik-vision-1.0.20'

ext {
    set('dockerRepo', 'harbor.ijx.icu')
    if (!project.hasProperty('dockerPrefix')) {
        dockerPrefix = 'ijx'
    }
}

java {
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11
}

dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.7.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.7.0'

    implementation fileTree(dir: 'libs', includes: ['*'])
    implementation project(':common')
    annotationProcessor 'org.projectlombok:lombok'
    implementation 'org.projectlombok:lombok'

    implementation 'com.github.xiaoymin:knife4j-spring-boot-starter:3.0.3'
    implementation("net.java.dev.jna:jna:5.14.0")
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'org.apache.commons:commons-text:1.10.0'


}

configurations.configureEach {
    exclude group: 'com.baomidou', module: 'dynamic-datasource-spring-boot-starter'
    exclude group: 'com.baomidou', module: 'mybatis-plus-boot-starter'
    exclude group: 'com.baomidou', module: 'dynamic-datasource-spring-boot-starter'
    exclude group: 'mysql', module: 'mysql-connector-java'
}

bootJar {
    launchScript()
    archiveName "basic-hik-vision.jar"
}

tasks.register('copyHikLibraryFiles', Copy) {
    from 'libs'
    include '*'
    into "${buildDir}/libs"
}
dockerPrepare.dependsOn(bootJar)
dockerPrepare.dependsOn(tasks.copyHikLibraryFiles)